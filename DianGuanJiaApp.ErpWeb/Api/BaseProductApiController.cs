using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Utility.Net;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using System.Threading;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct;

namespace DianGuanJiaApp.ErpWeb.Api
{
    public class BaseProductApiController : ApiBaseController
    {
        private CommonSettingService _service = new CommonSettingService();

        [LogForOperatorFilter("跨云平台更新配置API")]
        public ActionResult SaveCommonSettingForPdd()
        {
            var setting = RequestModel.GetParamModel<CommonSetting>();
            bool result = true;

            Log.Debug(() => $"源自Pdd的配置保存，setting={setting.ToJson()}", "SaveCommonSettingFromPdd.txt");

            //记录请求日志
            var logView = new InvokeApiDataLogModel
            {
                BatchId = Guid.NewGuid().ToString(),
                RequestContent = setting.ToJson(),
                ShopId = setting.ShopId,
                StartTime = DateTime.Now,
                Status = "接收端"
            };
            InvokeApiDataTrackingService.Instance.WriteLog(logView);

            var retryTimes = 3;
            for (var i = 0; i < retryTimes; i++)
            {
                try
                {
                    _service.Set(setting.Key, setting.Value, setting.ShopId);

                    break;
                }
                catch (Exception ex)
                {
                    ExceptionLogDataEventTrackingService.Instance.WriteLog(ex, GetType().Name);
                    if (i >= retryTimes - 1)
                        throw new LogicException("服务器繁忙，请稍后再试！");
                    Thread.Sleep(100 * i);
                }
            }

            //发往其他云
            var apiUrl = "/CommonApi/SaveCommonSetting";
            var aliCloudHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;
            var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;

            //分发到精选云
            if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString() && !string.IsNullOrEmpty(CustomerConfig.AlibabaFenFaSystemUrl))
            {
                Common.PostFxSiteApi<CommonSetting, bool>(
                    aliCloudHost,
                    SiteContext.Current.CurrentFxUserId,
                    setting,
                    "分单系统分发到其他云平台站点更新配置");

                Log.Debug(() => "源自Pdd的配置保存，转发到精选", "SaveCommonSettingFromPdd.txt");
            }
            //分发到抖店云
            if (CustomerConfig.CloudPlatformType != PlatformType.TouTiao.ToString() && !string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl))
            {
                Common.PostFxSiteApi<CommonSetting, bool>(
                    ttCloudHost,
                    SiteContext.Current.CurrentFxUserId,
                    setting,
                    "分单系统分发到其他云平台站点更新配置");

                Log.Debug(() => "源自Pdd的配置保存，转发到抖店", "SaveCommonSettingFromPdd.txt");
            }

            //分发到京东云
            if (CustomerConfig.CloudPlatformType != PlatformType.Jingdong.ToString() && !string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl))
            {
                Common.PostFxSiteApi<CommonSetting, bool>(
                    jdCloudHost,
                    SiteContext.Current.CurrentFxUserId,
                    setting,
                    "分单系统分发到其他云平台站点更新配置");

                Log.Debug(() => "源自Pdd的配置保存，转发到京东", "SaveCommonSettingFromPdd.txt");
            }
            
            return SuccessResult(result);
        }

        /// <summary>
        /// 跨云平台生成基础资料
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云平台生成基础资料")]
        public ActionResult GenerateBaseProduct()
        {
            var baseProducts = RequestModel.GetParamModel<List<BaseProductSkuAddModel>>();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var baseProductService = new BaseProductSkuService();
                var res = baseProductService.CreateBaseProductSkus(baseProducts, fxUserId);
                return SuccessResult(true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云平台生成基础资料异常：{ex}");
                return SuccessResult(false);
            }
        }

        /// <summary>
        /// 跨云平台生成基础资料
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云平台生成基础资料V2")]
        public ActionResult GenerateBaseProductV2()
        {
            var baseProducts = RequestModel.GetParamModel<List<BaseProductSkuAddModel>>();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var baseProductService = new BaseProductSkuService();
                var res = baseProductService.CreateBaseProductSkus(baseProducts, fxUserId);
                // 继续生成即时零售货源商品
                var baseProductUidList = res.Select(p => p.Uid).ToList();
                var result = new SupplierProductService().GenerateSupplierProduct(baseProductUidList, fxUserId, "SaleShop"); 
                Log.Debug(() => $"生成即时零售货源商品：{result}", LogModuleTypeEnum.DistributionProduct);
                return SuccessResult(res);
            }
            catch (Exception ex)
            {
                Log.WriteError($"V2云平台【{CustomerConfig.CloudPlatformType}】跨云平台生成基础资料异常：{ex}");
                return SuccessResult(null);
            }
        }

        /// <summary>
        /// 跨云基础商品解绑
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云基础商品解绑")]
        public ActionResult SkuRelationUnbind()
        {
            var model = RequestModel.GetParamModel<BaseProductSkuUnbindModel>();
            try
            {
                var logFileName = "AbnormalUnBindCloud.txt";
                Log.Debug($"跨云，model={model.ToJson()}", logFileName);
                //切到指定业务库
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() || CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                    new SiteContext(SiteContext.Current.CurrentFxUser, model.ProductDbName.ToString2());

                var baseProductService = new BaseProductSkuCommonService();
                var result = baseProductService.BaseProductSkuRelationUnbind(model);
                if (result.Success)
                    return SuccessResult(true);
                else
                    throw new LogicException(result.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云基础商品解绑：{ex}");
                return SuccessResult(false);
            }
        }

        /// <summary>
        /// 跨云基础商品解绑
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云基础商品批量解绑")]
        public ActionResult BatchSkuRelationUnbind()
        {
            var models = RequestModel.GetParamModel<List<BaseProductSkuUnbindModel>>();
            UnbindRelationResultModel tempResult = null;
            var logFileName = "AbnormalUnBindCloud.txt";
            try
            {
                Log.Debug(()=>$"跨云，model={models.ToJson()}", logFileName);
                
                //切到指定业务库
                var dbGroups = models.GroupBy(m => m.ProductDbName).ToList();
                foreach (var dbGroup in dbGroups)
                {
                    // ReSharper disable once ObjectCreationAsStatement
                    new SiteContext(SiteContext.Current.CurrentFxUser, dbGroup.Key.ToString2());

                    var dbModels = dbGroup.ToList();
                    tempResult = new BaseProductSkuCommonService().BaseProductSkuRelationUnbind(dbModels, true);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云基础商品解绑发生异常：{ex}", logFileName);
            }
            return SuccessResult(tempResult);
        }

        /// <summary>
        /// 跨平台获取基础商品Sku详情
        /// </summary>
        /// <returns></returns>
        public ActionResult GetBaseProductSkuDetail()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();
            // Item1是BaseProductUid， Item2是BaseProductSkuUid
            var model = RequestModel.GetParamModel<Tuple<long, long>>();

            try
            {
                var baseProductSkuInfo = new BaseProductSkuCommonService().GetBaseProductSkuDetail(model.Item1, model.Item2);
                return baseProductSkuInfo == null ? SuccessResult() : SuccessResult(baseProductSkuInfo);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云获取基础商品详情：{ex}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 跨云查询商品详情接口(查询商品接口-->保存)
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云查询商品详情接口")]
        public ActionResult GetProductForListing()
        {
            var model = RequestModel.GetParamModel<GetProductFromApiModel>();
            try
            {
                var logFileName = "GetProductForListing.txt";
                Log.Debug($"跨云，model={model.ToJson()}", logFileName);
                var productFxService = new ProductFxService();
                var result = productFxService.GetProductForListing(model);
                Log.Debug($"跨云，model={model.ToJson()},result={result?.ToJson()}", logFileName);
                return SuccessResult(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云查询商品详情接口：{ex}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 跨云在业务库添加关联同款
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云添加关联同款")]
        public ActionResult AddBaseOfPtSkuRelation()
        {
            var models = RequestModel.GetParamModel<List<BaseOfPtSkuRelation>>();
            try
            {
                var logFileName = "AddBaseOfPtSkuRelation.txt";
                Log.Debug($"跨云，models={models.ToJson()}", logFileName);
                //业务库
                new BaseOfPtSkuRelationService(false).Merger(models);
                return SuccessResult(true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云在业务库添加关联同款：{ex}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 跨云处理路径关系-铺货
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云处理路径关系-铺货")]
        public ActionResult BindSupplierForListing()
        {
            var model = RequestModel.GetParamModel<BindSupplierForListingModel>();
            try
            {
                var logFileName = "BindSupplierForListing.txt";
                Log.Debug($"跨云，model={model.ToJson()}", logFileName);

                new PathFlowService().BulkMerger(model.PathFlows);
                new PathFlowReferenceService().BulkMergerFromListing(model.PathFlowReferences, new List<int> { model.FxUserId });

                return SuccessResult(true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云处理路径关系-铺货：{ex}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 跨云处理结算价-铺货
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云处理结算价-铺货")]
        public ActionResult SetProductSettlementPrice()
        {
            var models = RequestModel.GetParamModel<List<ProductSettlementPrice>>();
            try
            {
                var logFileName = "SetProductSettlementPrice.txt";
                Log.Debug($"跨云，models={models.ToJson()}", logFileName);
                var fxUserId = SiteContext.Current.CurrentFxUserId;

                var changeType = FinancialSettlementRepository.ChangeType.SyncBaseProduct.ToInt();
                new FinancialSettlementService().SetProductSettlementPrice(models, fxUserId, changeType: changeType, needSetIdFromDb: true);

                return SuccessResult(true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云处理结算价-铺货：{ex}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 跨云处理商品更新
        /// </summary>
        /// <returns></returns>
        public ActionResult SyncPtInfo()
        {
            var model = RequestModel.GetParamModel<BaseSkuSyncToPtSkuModel>();
            model.IsFromCenter = false;
            try
            {
                // 切换分区
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() || 
                    CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                {
                    var site = new SiteContext(SiteContext.Current.CurrentFxUser, model.DbName); 
                }
                var logFileName = "BindSupplierForListing.txt";
                Log.Debug($"跨云，model={model.ToJson()}", logFileName);
                var changeSkuCodes = new List<string>();
                new BaseProductSkuCommonService().SyncToPtSku(model, ref changeSkuCodes);
                return SuccessResult(true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云处理同步商品信息：{ex}");
                return FalidResult("系统异常");
            } 
        }

        /// <summary>
        /// 跨平台获取基础商品Sku数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetBaseProductSkuSimple()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();

            var model = RequestModel.GetParamModel<BaseProductSkuSimpleReq>();
            model.FxUserId = SiteContext.Current.CurrentFxUserId;

            try
            {
                var baseProductSkuInfo = new BaseProductSkuCommonService().GetBaseProductSkuDetail(model);
                return baseProductSkuInfo == null ? SuccessResult() : SuccessResult(baseProductSkuInfo);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云基础商品信息失败：{ex}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 跨平台获取基础商品Sku数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetBaseProductSkuSimpleByCondition()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();

            var model = RequestModel.GetParamModel<BpSkuSimpleConditionReq>();

            try
            {
                model.FxUserId = SiteContext.Current.CurrentFxUserId;
                var baseProductSkuInfo = new BaseProductSkuCommonService().GetBaseProductSkuSimpleByCondition(model);
                return baseProductSkuInfo == null ? SuccessResult() : SuccessResult(baseProductSkuInfo);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】({nameof(GetBaseProductSkuSimpleByCondition)})跨云基础商品信息失败：{ex}");
                return SuccessResult();
            }
        }
        /// <summary>
        /// 跨云获取基础商品信息
        /// </summary>
        /// <returns></returns>
        public ActionResult GetBaseProductInfo()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();
            var model = RequestModel.GetParamModel<BaseProductSkuSimpleReq>();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            
            if (model == null || model.SkuUId.IsNullOrEmpty())
                return SuccessResult();
            
            try
            {
                // 获取基础商品Sku信息
                var baseSkuList = new BaseProductSkuService().GetListBySkuUids(model.SkuUId, fxUserId);
                var baseSpuUids = baseSkuList.Select(x => x.BaseProductUid).Distinct().ToList();
                if (baseSpuUids.IsNullOrEmpty()) return SuccessResult();
                
                // 获取基础商品信息
                var baseProductList = new BaseProductEntityService().GetListByUids(baseSpuUids, fxUserId); 
                
                // 匹配基础商品信息
                foreach (var baseProduct in baseProductList)
                {
                    baseProduct.Skus = baseSkuList.Where(x => x.BaseProductUid == baseProduct.Uid).ToList();
                }
                
                return SuccessResult(baseProductList);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云获取基础商品信息失败：{ex}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 跨云获取基础商品Sku列表
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadBaseProductSkuList()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult(); 
            
            var model = RequestModel.GetParamModel<BaseProductSearchModel>();
            if (model == null) return SuccessResult();
            
            var (listCount, list) = new BaseProductSkuCommonService().LoadBaseProductSkuList(model);
            
            var result = new PagedResultModel<BaseProductSku>
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Rows = list,
                Total = listCount
            }; 
            
            return SuccessResult(result);
        }

        /// <summary>
        /// 跨云获取基础商品Sku关联情况
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("获取基础商品Sku关联情况")]
        [HttpPost]
        public ActionResult GetSkuRelation()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();  
            
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var model = RequestModel.GetParamModel<BaseProductSkuRelationModel>();
            if (model == null || model.FxUserId != fxUserId) return SuccessResult();
            
            // 查找对应的基础商品Sku信息
            var baseProductSku =  new BaseProductSkuService().GetByUid(model.SkuUid);
            return SuccessResult(baseProductSku); 
        }

        /// <summary>
        /// 由pdd发起获取图片
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetImageByPdd()
        {
            // 判断当前云平台是否为拼多多
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString()) return SuccessResult();
            Log.Debug(()=>$"调用api-GetImageByPdd", "OssStorageLog.txt");
            var model = RequestModel.GetParamModel<ImageModel>();
            var token = model.Token;
            var platform = model.Platform;
            var objectKey = model.ObjectKey;
            var imgUrl = model.Url;
            try
            {
                // 有图片链接，非OSS链接
                if (string.IsNullOrEmpty(imgUrl) == false && ImgHelper.NeedsConversion(imgUrl) == false)
                {
                    var httpGetStream = HttpMethods.HttpGetStream(imgUrl, Encoding.UTF8);
                    if (httpGetStream == null) return Content("未获取到相关资源");
                        
                    // 读取图片流并返回
                    using (var mStream = new MemoryStream())
                    {
                        // 将获取到的网络流复制到内存流中
                        httpGetStream.CopyTo(mStream);
                        var type = $"image/{Path.GetExtension(imgUrl)?.TrimStart('.')}";

                        var imgModel = new ImageResultModel
                        {
                            Type = type,
                            Bytes = mStream.ToArray()
                        };
                        
                        return SuccessResult(imgModel);
                    }
                }
                    
                platform = CommUtls.ChangePlatformType(platform, model.BusinessType);
                if (string.IsNullOrWhiteSpace(token)) return FalidResult("Token验证失败！");
                var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                if (string.IsNullOrEmpty(json))
                    return FalidResult("Token验证失败！");
                var authToken = new ShopService().GetToken(json.ToInt());
                if (authToken == null || authToken.FxUserId <= 0) return FalidResult("Token验证失败！");
                    
                if (string.IsNullOrEmpty(platform)) { return null; }

                if (string.IsNullOrWhiteSpace(objectKey)) return FalidResult("请求KEY不存在");
                    
                // 解密
                var decryptObjectKey = DES.DecryptUrl(objectKey, CustomerConfig.LoginCookieEncryptKey);
                Log.Debug($"开始:入参objectKey={objectKey}，platform={platform}", "OssStorageLog.txt");
                var bytes = CloudStorageUploaderFactory.GetObjectStorage(platform, decryptObjectKey);
                if (bytes == null)
                    return FalidResult("未获取到相关资源");
                var contentType = $"image/{decryptObjectKey.Substring(decryptObjectKey.LastIndexOf(".", StringComparison.Ordinal) + 1)}";
                Log.Debug($"结束 :入参objectKey={decryptObjectKey}，返回字字节长度为：{bytes.Length},contentType;{contentType}", "OssStorageLog.txt");
                var resultModel = new ImageResultModel
                {
                    Type = contentType,
                    Bytes = bytes
                };
                return SuccessResult(resultModel);
            }
            catch (Exception ex)
            {
                Log.WriteError("获取图片服务器异常：" + ex.Message, "OssStorageLog.txt");
                return FalidResult(ex.Message);
            }
        }

        /// <summary>
        /// 跨云删除基础商品关联关系，仅用于解绑程序
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult DelBaseProductRelation()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult(); 
            
            var model = RequestModel.GetParamModel<DelBaseProductRelationModel>();
            if (model == null) return SuccessResult();
            
            var supplierFxUserIds = model.FxUserIds;
            var productCodes = model.ProductCodes;
            
            // 获取对应数据库
            var productDbConfigRepository = new ProductDbConfigRepository();
            var basePtDbNameIds = productDbConfigRepository.GetDbConfigByFxUserId(supplierFxUserIds)
                .Select(x => x.DbNameConfigId).Distinct().ToList();
            // 获取所有的基础分库连接串
            var basePtDbConfigs = productDbConfigRepository.GetDbConfigModelByDbNameId(basePtDbNameIds)
                .Select(x => x.ConnectionString).Distinct().ToList();
            // 删除基础商品关联关系
            var result = basePtDbConfigs.Sum(conn => new BaseOfPtSkuRelationRepository(conn).DeleteByProductCodes(productCodes));
            return SuccessResult(result);
        }

        /// <summary>
        /// 获取基础商品Sku列表信息
        /// </summary>
        /// <returns></returns>
        public ActionResult GetBaseSkuInfo()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();  
            
            var model = RequestModel.GetParamModel<GetBaseProductSkuModel>();
            if (model == null) return SuccessResult();

            var infos = new BaseProductSkuRepository().GetListBySkuUids(model.Uids, model.FxUserId);
            return SuccessResult(infos);
        }

        /// <summary>
        /// 跨云查询基础商品规格关联
        /// </summary>
        /// <returns></returns>
        public ActionResult BaseProductSkuRelationDetailList()
        {
            //Log.Debug(() => $"跨云查询基础商品规格关联，model：{RequestModel.ToJson()}", ModuleFileName.BaseProduct);
            var model = RequestModel.GetParamModel<BaseProductSkuBindSearchModel>();

            Log.Debug(() => $"{SiteContext.GetCurrentFxUserId()}用户跨云查询基础商品规格关联Api，当前云：{CustomerConfig.CloudPlatformType}，model：{model.ToJson()}", ModuleFileName.BaseProduct);
            if (model == null) return SuccessResult();

            var res = new ProductFxService().GetProductSkuBindDetailList(model);
            Log.Debug(() => $"{SiteContext.GetCurrentFxUserId()}用户跨云查询基础商品规格关联Api，查询结果：{res.ToJson()}", ModuleFileName.BaseProduct);
            return SuccessResult(res);
        }

        /// <summary>
        /// 跨云查询我的小站关联规格
        /// </summary>
        /// <returns></returns>
        public ActionResult GetSupplierSkuRelations()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                Log.WriteError($"{SiteContext.GetCurrentFxUserId()}用户跨云查询我的小站关联规格api，当前云{CustomerConfig.CloudPlatformType}非精选云！", ModuleFileName.BaseProduct);
                return SuccessResult();
            }
            var model = RequestModel.GetParamModel<BaseProductSkuBindSearchModel>();
            Log.Debug(() => $"{SiteContext.GetCurrentFxUserId()}用户跨云查询我的小站关联规格api，model：{model.ToJson()}", ModuleFileName.BaseProduct);
            if (model == null) return SuccessResult();

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var res = new BaseOfSupplierSkuRelationService(fxUserId).GetSupplierSkuRelations(model, fxUserId);

            Log.Debug(() => $"{SiteContext.GetCurrentFxUserId()}用户跨云查询我的小站关联规格Api，查询结果：{res.ToJson()}", ModuleFileName.BaseProduct);
            return SuccessResult(res);
        }

        /// <summary>
        /// 业务库关联数据补偿
        /// </summary>
        /// <returns></returns>
        public ActionResult BusinessDbRelationDataCompensate()
        {
            var (baseSkuUid,filterSkuCodeList) = RequestModel.GetParamModel<Tuple<long,List<string>>>();
            if(baseSkuUid<=0)
            {
                return FalidResult("传入的基础商品规格Uid为0");
            }

            var fxUserId = SiteContext.GetCurrentFxUserId();
            var result =string.Empty;

            #region 获取任务

            var taskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = fxUserId,
                StatusList = new List<int>() { 0, 1 },
                Flag = "BaseProduct-BusinessDbRelationDataCompensate",
                IsSetExpiress = true,
                ExpiressTime = 5
            };

            var task = taskService.GetAsyncTask(query);

            #endregion

            if (task == null)
            {
                task = new AsyncTask
                {
                    Flag = "BaseProduct-BusinessDbRelationDataCompensate",
                    CData = $"BaseSkuUid：{baseSkuUid}",
                };
                taskService.AddAsyncTask(task);
                try
                {
                    task.Status = 1;
                    task.UpdateTime = DateTime.Now;
                    taskService.UpdateAsyncTask(task);

                    // 查询基础商品库的关联数据
                    List<BaseOfPtSkuRelation> baseProductDbRelationList = null;

                    #region 查询基础商品库的关联数据

                    // 精选平台直接连接基础商品库查询

                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    {

                        var baseProductService = new BaseOfPtSkuRelationRepository(true, fxUserId);
                        baseProductDbRelationList = baseProductService.GetListBySkuUid(baseSkuUid, fxUserId: fxUserId);

                        var list = baseProductDbRelationList;
                        Log.Debug(() => $"用户{fxUserId}补偿业务库基础商品关联数据，在精选云平台查询基础商品库，关联数：{list?.Count}",
                            ModuleFileName.BaseProduct);

                    }
                    else // 非精选平台通过WebPost查询基础商品库
                    {
                        Log.Debug(
                            () => $"用户{fxUserId}补偿业务库基础商品关联数据，在云平台{CustomerConfig.CloudPlatformType}调用WebPost查询基础商品库",
                            ModuleFileName.BaseProduct);

                        var token = Request.Params["token"];
                        if (token.IsNullOrEmpty())
                            token = new ShopService().GetTokenByShopId(SiteContext.GetCurrentShopId())?.Token;
                        if (token.IsNullOrEmpty())
                            throw new LogicException($"{fxUserId}用户token获取失败！");

                        var apiUrl = "/BaseProductApi/GetBaseSkuRelationListApi";
                        var targetSiteUrl = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/") + apiUrl;

                        // 拼多多无法直接访问外网，需要中转请求
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                        {
                            targetSiteUrl = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                        }

                        baseProductDbRelationList = Common.PostFxSiteApi<long, List<BaseOfPtSkuRelation>>(targetSiteUrl,
                            fxUserId, baseSkuUid, "跨云查询基础商品规格关联数", isEncrypt: true);

                        Log.Debug(() =>
                                $@"用户{fxUserId}补偿业务库基础商品关联数据，在云平台{CustomerConfig.CloudPlatformType}调用WebPost查询基础商品库，url：{apiUrl}
                                   ，当前Ip：{Request?.UserHostAddress}，查询数据条数{baseProductDbRelationList?.Count}",
                            ModuleFileName.BaseProduct);

                    }

                    if (baseProductDbRelationList.IsNullOrEmptyList())
                    {
                        Log.WriteWarning($"用户{fxUserId}补偿业务库基础商品关联数据，查询基础商品库关联数据为空！", ModuleFileName.BaseProduct);
                        return null;
                    }

                    #endregion

                    result = new BaseOfPtSkuRelationService(false, fxUserId)
                        .BusinessDbRelationDataCompensate(baseSkuUid, baseProductDbRelationList,
                            filterSkuCodeList: filterSkuCodeList)?.Item1;
                    Log.Debug(
                        () => $"用户{fxUserId}通过Api补偿基础商品关联数据，当前云：{CustomerConfig.CloudPlatformType}，执行结果：{result}",
                        ModuleFileName.BaseProduct);
                }
                catch (Exception ex)
                {
                    task.ExceptionDesc = $"{ex.ToJson()}";
                    Log.WriteError($"用户{fxUserId}调用WebApi补偿业务库关联数据发生异常：{ex}", ModuleFileName.BaseProduct);
                }
                finally
                {
                    // 测试环境才更新状态，正式环境不更新任务状态，过期时间5分钟之内，都会是执行中，防止请求过于频繁
                    if (CustomerConfig.IsDebug && fxUserId != 74)
                        task.Status = task.ExceptionDesc.IsNullOrEmpty() ? 5 : -1;
                    task.UpdateTime = DateTime.Now;
                    taskService.UpdateAsyncTask(task);
                }
            }
            else
            {
                Log.WriteWarning($"用户{fxUserId}检查关联数据一致性，当前云平台：{CustomerConfig.CloudPlatformType}。有任务正在进行，TaskId：{task.Id}", ModuleFileName.BaseProduct);
                return FalidResult("有任务正在执行");
            }


            return SuccessResult(result);
        }


        /// <summary>
        /// 跨云获取基础商品Sku关联数据
        /// 查的是基础商品库
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("获取基础商品Sku关联数据Api")]
        public ActionResult GetBaseSkuRelationListApi()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString() && !CustomerConfig.IsLocalDbDebug)
                return FalidResult("当前平台非精选平台");
            var skuUid = RequestModel.GetParamModel<long>();
            if(skuUid == 0)
                return FalidResult("基础商品SkuUid不能为空");

            var fxUserId = SiteContext.Current.CurrentFxUserId;

            // 查找对应的基础商品Sku的关联数据
            var relations = new BaseOfPtSkuRelationService(fxUserId).GetListBySkuUid(skuUid);

            Log.Debug(() => $"{fxUserId}用户通过接口查询基础商品sku关联数据 {relations.Count} 条", ModuleFileName.BaseProduct);
            return SuccessResult(relations);
        }

        /// <summary>
        /// 跨云验证验证码
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("跨云验证码验证")]
        [HttpPost]
        public ActionResult VerifyCaptcha()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != nameof(CloudPlatformType.Alibaba) && !CustomerConfig.IsLocalDbDebug)
                return FalidResult("当前平台非精选平台"); 
            var param = RequestModel.GetParamModel<string>(); 
            
            // 验证阿里云验证码
            var sceneId = CustomerConfig.AlibabaCaptchaSceneId;
            var result = AlibabaCaptchaService.Instance.VerifyCaptcha(param, sceneId);
            return SuccessResult(result);
        }

        #region 售后地址-跨云操作
        /// <summary>
        /// 跨平台获取-厂家售后地址
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetAfterSaleAddresDetail()
        {
            // 如果不是精选云平台，直接返回
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();

            var model = RequestModel.GetParamModel<GetAfterSaleAddresDetailReq>();
            model.CurrentFxUserId = SiteContext.Current.CurrentFxUserId;

            try
            {
                var data = new AfterSaleAddressService().GetAfterSaleAddresDetail(model);
                Log.Debug(() => $"跨平台获取厂家售后地址,入参 {model.ToJson()} 出参:{data.ToJson()}", ModuleFileName.AfterSaleAddres);
                return data == null ? SuccessResult() : SuccessResult(data);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云获取[厂家售后地址]失败：{ex}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 获取厂家售后地址分页列表1
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetAfterSalesAddressesList()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();

            try
            {
                var requsetParms = RequestModel.GetParamModel<GetAfterSalesAddressesListReq>();
                var pageList = new AfterSaleAddressService().GetPageList(requsetParms);
                return SuccessResult(pageList);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云获取[获取厂家售后地址分页列表]失败：{ex}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 获取厂家设置的售后地址。前端场景：从自定义改成自动同步1
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetAddressByManufacturer()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();
            var requsetParms = RequestModel.GetParamModel<AddressByManufacturerReq>();

            try
            {
                var result = new AfterSaleAddressService().GetAddressByManufacturer(requsetParms);
                return SuccessResult(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】[获取厂家设置的售后地址]失败：{ex.Message} 堆栈: {ex.StackTrace}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 设置创建者厂家的售后地址1
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult AfterSaleAddressSave()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();

            try
            {
                var requsetParms = RequestModel.GetParamModel<AfterSaleAddressSaveReq>();
                var result = new AfterSaleAddressService().AfterSaleAddressSave(requsetParms);
                return SuccessResult(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】[设置创建者厂家的售后地址]失败：{ex.Message} 堆栈: {ex.StackTrace}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 【跨平台设置】首次开启，同步厂家的售后地址1
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult EnableAllFsAsAutoSync()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();

            try
            {
                var result = new AfterSaleAddressService().CheckSaveSupplierListByAddress();
                return SuccessResult(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】[精选云将地区信息表初始化]失败：{ex.Message} 堆栈: {ex.StackTrace}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 厂家售后地址修改日志 1
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetAfterSaleAddressLogList()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();
            try
            {
                var requsetParms = RequestModel.GetParamModel<PageRequest>();
                var pageList = new AfterSaleAddressService().GetAfterSaleAddressLogList(requsetParms);
                return SuccessResult(pageList);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】[厂家售后地址修改日志]失败：{ex.Message} 堆栈: {ex.StackTrace}");
                return SuccessResult();
            }
        }

        /// <summary>
        /// 厂家售后地址改成 “已完善”
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult SyncSupperCompletionStatus()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString()) return SuccessResult();
            try
            {
                var requsetParms = RequestModel.GetParamModel<SyncSupperCompletionStatusReq>();
                var pageList = new AfterSaleAddressService().SyncCompletionStatus(requsetParms);
                return SuccessResult(pageList);
            }
            catch (Exception ex)
            {
                Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】[厂家售后地址改成 “已完善”]失败：{ex.Message} 堆栈: {ex.StackTrace}");
                return SuccessResult();
            }
        }
        
        #endregion
    }
}
