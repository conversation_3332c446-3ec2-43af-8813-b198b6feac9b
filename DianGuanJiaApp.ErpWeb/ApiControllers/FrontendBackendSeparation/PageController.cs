using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.APIUser;
using DianGuanJiaApp.ErpWeb.Controllers;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.SettingsService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DianGuanJiaApp.ErpWeb.ApiControllers.FrontendBackendSeparation
{
    /// <summary>
    /// PageAPI控制器
    /// </summary>
    public partial class PageController : SeparationBaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        public PageController()
        {
        }

        private readonly FxUserShopService _fxUserShopService = new FxUserShopService();
        private readonly CommonSettingService _commonSettingService = new CommonSettingService();
        private readonly UserFxService _userFxService = new UserFxService();
        private readonly CheckRuleService _checkRuleService = new CheckRuleService();
        private readonly SupplierUserService _supplierUserService = new SupplierUserService();
        private readonly SettlementBillService _settlementBillService = new SettlementBillService();
        private readonly ExportTaskService _exportTaskService = new ExportTaskService();
        private readonly PathFlowService _pathFlowService = new PathFlowService();
        private readonly ShopService _shopService = new ShopService();
        private readonly AreaCodeInfoService _areaCodeInfoService = new AreaCodeInfoService();
        private readonly BusinessSettingsService _businessSettingsService = new BusinessSettingsService();

        #region 当前用户

        private static void SetCurrentFxUser()
        {
            CurrentSite = SiteContext.Current;
            CurrentFxUserId = SiteContext.Current.CurrentFxUserId;
            CurrentShopId = SiteContext.Current.CurrentShopId;
            CurrentShop = SiteContext.Current.CurrentLoginShop;
        }

        /// <summary>
        /// 
        /// </summary>
        public static Shop CurrentShop { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public static int CurrentShopId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public static int CurrentFxUserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public static SiteContext CurrentSite { get; set; }

        #endregion


        #region 首页模块

        /// <summary>
        ///GeneralizeIndex的Index页面
        /// </summary>
        /// <code>
        ///  var sid = Request["sid"];
        ///  var pt = Request["pt"];
        /// </code>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetGeneralizeIndexPageInfo()
        {
            var sid = Request["sid"];
            var pt = Request["pt"];
            var lastRefreshTime =
                _commonSettingService.GetString("/GeneralizeIndex/LastRefreshTime",
                    SiteContext.Current.CurrentShopId) ?? "";
            var tuple = new GeneralizeIndexController().GetWaitBindShop(sid, pt);
            var fromShop = tuple?.Item2;
            var isTouTiaoFxApp = 0;
            // 判断是否头条店铺有多个应用，在后台点铺货代发去使用，前端提示订购使用分销代发应用
            if (sid.IsNotNullOrEmpty() && pt == PlatformType.TouTiao.ToString() && fromShop != null)
            {
                var shopExList = new ShopExtensionService().GetRawShopExtensionByShopIds(new List<int> { fromShop.Id });
                var appCount = shopExList.GroupBy(x => x.AppKey).Count();
                if (appCount > 1)
                {
                    var appKey = shopExList?.OrderByDescending(x => x.LastRefreshTokenTime).FirstOrDefault()?.AppKey ??
                                 "";
                    isTouTiaoFxApp = appKey == CustomerConfig.TouTiaoFxAppKey ? 1 : 0;
                }
            }

            // 常用功能按钮显示权限
            var oftenUseShowDict = new Dictionary<string, bool>()
            {
                {
                    nameof(FxPermission.NewOrderPrintSetting),
                    SiteContext.HasPermission(FxPermission.NewOrderPrintSetting)
                },
                {
                    nameof(FxPermission.NewOrderEbillAccount),
                    SiteContext.HasPermission(FxPermission.NewOrderEbillAccount)
                },
                { nameof(FxPermission.OfflineOrder), SiteContext.HasPermission(FxPermission.OfflineOrder) },
                { nameof(FxPermission.SendOrderIndex), SiteContext.HasPermission(FxPermission.SendOrderIndex) },
                { nameof(FxPermission.WaybillCodeList_cj), SiteContext.HasPermission(FxPermission.WaybillCodeList_cj) },
                { nameof(FxPermission.WaitOrder), SiteContext.HasPermission(FxPermission.WaitOrder) },
                { nameof(FxPermission.ProductIndex), SiteContext.HasPermission(FxPermission.ProductIndex) },
                { nameof(FxPermission.StoreManagement), SiteContext.HasPermission(FxPermission.StoreManagement) }
            };
            var binded = Request["binded"];
            var result = _userFxService.CheckFxUserHasTwoApp(SiteContext.Current.CurrentFxUserId);
            var hasTwoApp = result > 1;
            return SuccessResult(new
            {
                LastRefreshTime = lastRefreshTime,
                IsTouTiaoFxApp = isTouTiaoFxApp,
                OftenUseShowDict = oftenUseShowDict,
                binded = binded ?? "",
                Title = "首页",
                MenuId = "GeneralizeIndex",
                MenuActive = "Workbench",
                HasTwoApp = hasTwoApp,
            });
        }

        #endregion


        #region 授权管理模块

        /// <summary>
        /// Partner的Index页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPartnerIndexPageInfo()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var isAddAvailable = true;
            var isTouTiaoOldUser = _commonSettingService.IsTipsDouYinNewApp();
            var result = _userFxService.CheckFxUserHasTwoApp(SiteContext.Current.CurrentFxUserId);
            var hasTwoApp = result > 1;
            var isWxVideoOldUser = _userFxService.CheckWxVideoUser(fxUserId) ? "true" : "false";
            // 页面按钮展示权限
            var showPermDict = new Dictionary<string, bool>
            {
                { $"#{nameof(FxPermission.AddNewShop)}", SiteContext.HasPermission(FxPermission.AddNewShop) },
                { $".{nameof(FxPermission.RenewalShop)}", SiteContext.HasPermission(FxPermission.RenewalShop) },
                { $".{nameof(FxPermission.EditShop)}", SiteContext.HasPermission(FxPermission.EditShop) },
                { $".{nameof(FxPermission.UnbindShop)}", SiteContext.HasPermission(FxPermission.UnbindShop) },
            };
            var defaultShopId = Request["shopId"].ToString2();
            return SuccessResult(new
            {
                // ReSharper disable once ConditionIsAlwaysTrueOrFalse
                IsAddAvailable = isAddAvailable,
                IsTouTiaoOldUser = isTouTiaoOldUser,
                HasTwoApp = hasTwoApp,
                IsWxVideoOldUser = isWxVideoOldUser,
                ShowPermDict = showPermDict,
                DefaultShopId = defaultShopId ?? "",
                AvailableCountInfo = "null",
                Title = "我的店铺",
                MenuId = "Partner",
                MenuActive = "Authorization",
            });
        }

        /// <summary>
        /// Partner的MySupplier页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPartnerMySupplierPageInfo()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            // 页面按钮展示权限
            var showPermDict = new Dictionary<string, bool>
            {
                { $"#{nameof(FxPermission.AddBindSupplier)}", SiteContext.HasPermission(FxPermission.AddBindSupplier) },
                { $"#{nameof(FxPermission.SupplierInvite)}", SiteContext.HasPermission(FxPermission.SupplierInvite) },
                {
                    $"#{nameof(FxPermission.AddVirtualSupplier)}",
                    SiteContext.HasPermission(FxPermission.AddVirtualSupplier)
                },
                { $".{nameof(FxPermission.UnbindSupplier)}", SiteContext.HasPermission(FxPermission.UnbindSupplier) },
                {
                    $".{nameof(FxPermission.AgreeAndRefuseSupplier)}",
                    SiteContext.HasPermission(FxPermission.AgreeAndRefuseSupplier)
                },
                { $".{nameof(FxPermission.ApplyPrepay)}", SiteContext.HasPermission(FxPermission.ApplyPrepay) },
            };

            var checkResult =
                _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.FactoryUnit);
            var isAddAvailable = checkResult.IsAvailable;
            //TODO:这个是下拉框使用的，看是直接在页面数据返回，还是独立成一个新的接口提供数据
            var agentBingSupplierStatus = Enum.GetValues(typeof(AgentBingSupplierStatus)).Cast<int>()
                .ToDictionary(item => ((AgentBingSupplierStatus)item).ToString(), item => item);
            ;
            var applyPrepayStatus = Enum.GetValues(typeof(ApplyPrepayStatus)).Cast<int>()
                .ToDictionary(item => ((ApplyPrepayStatus)item).ToString(), item => item);
            ;

            var newStatusDic = Enum.GetValues(typeof(AgentBingSupplierStatus)).Cast<object>()
                .ToDictionary(item => (int)item, item => ((AgentBingSupplierStatus)item).GetEnumDescription());

            var keyOrderDisplaySetting = CommonSettingService.OrderDisplaySetting;
            var keys = new List<string>
            {
                "/FenFa/System/Config/IsShowCancelUser",
                "/FenFa/System/Config/IsSalePricePublic",
                "/FenFa/System/Config/IsAgentSendAddress",
                "/FenFa/System/Config/IsShopNamePublic",
                "/FenFa/System/Config/StockOutType",
                "/FenFa/System/Config/StockOutZHType",
                "/ErpWeb/OutAccount/Agent/UnSetPriceDailog",
                "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog",
                keyOrderDisplaySetting
            };
            var commonSettings = _commonSettingService.GetSets(keys, shopId);

            //没有设置则用默认
            if (commonSettings.Any(x => x.Key == "/FenFa/System/Config/IsAgentSendAddress") == false)
            {
                var defaultIsAgentSendAddress = _commonSettingService.Get("/FenFa/System/Config/IsAgentSendAddress", 0);
                if (defaultIsAgentSendAddress != null)
                {
                    commonSettings.Add(defaultIsAgentSendAddress);
                }
            }

            if (commonSettings.Any(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value
                    .toDateTime();
                commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value =
                    date.AddDays(15) < DateTime.Now ? "true" : "false";
            }

            if (commonSettings.Any(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value
                    .toDateTime();
                commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value =
                    date.AddDays(15) < DateTime.Now ? "true" : "false";
            }

            if (commonSettings.Any(x => x.Key == keyOrderDisplaySetting) == false)
            {
                //设置默认值
                commonSettings.Add(new CommonSetting
                {
                    Key = keyOrderDisplaySetting,
                    ShopId = shopId,
                    Value = CommonSettingService.DefaultOrderDisplaySetting.ToJson()
                });
            }

            var supplierUsers = _supplierUserService.GetSupplierUserNames(fxUserId);
            var suppliers = supplierUsers.Select(x => new { FxUserId = x.Key, UserName = x.Value, Status = 1 })
                .ToList();
            //是否显示1688菜单
            var isShow1688Menu = _commonSettingService.GetIsShow1688Menu(fxUserId, SiteContext.Current.CurrentShopId);

            var outStatus = Request["status"];
            return SuccessResult(new
            {
                IsAddAvailable = isAddAvailable,
                AgentBingSupplierStatus = agentBingSupplierStatus,
                ApplyPrepayStatus = applyPrepayStatus,
                Status = newStatusDic,
                IsShow1688Menu = isShow1688Menu,
                SupplierUsers = suppliers,
                ShowPermDict = showPermDict,
                CommonSettings = commonSettings,
                OutStatus = outStatus,
                Title = "我的厂家",
                MenuId = "Mysupplier",
                MenuActive = "Authorization",
            });
        }

        /// <summary>
        /// Partner的MyAgent页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPartnerMyAgentPageInfo()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;

            #region 不展示非合作展示

            var shopId = SiteContext.Current.CurrentShopId;
            var keyOrderDisplaySetting = CommonSettingService.OrderDisplaySetting;
            var keys = new List<string>()
            {
                "/FenFa/System/Config/IsShowCancelUser",
                "/FenFa/System/Config/IsSalePricePublic",
                "/FenFa/System/Config/IsAgentSendAddress",
                "/FenFa/System/Config/IsShopNamePublic",
                "/FenFa/System/Config/StockOutType",
                "/FenFa/System/Config/StockOutZHType",
                "/ErpWeb/OutAccount/Agent/UnSetPriceDailog",
                "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog",
                "/FenFa/System/Config/UnSetMemberLevelDialog",
                keyOrderDisplaySetting
            };
            var commonSettings = _commonSettingService.GetSets(keys, shopId);

            //没有设置则用默认
            if (commonSettings.Any(x => x.Key == "/FenFa/System/Config/IsAgentSendAddress") == false)
            {
                var defaultIsAgentSendAddress = _commonSettingService.Get("/FenFa/System/Config/IsAgentSendAddress", 0);
                if (defaultIsAgentSendAddress != null)
                    commonSettings.Add(defaultIsAgentSendAddress);
            }

            if (commonSettings.Any(x => x.Key == "/FenFa/System/Config/UnSetMemberLevelDialog") == false)
            {
                //设置默认值
                commonSettings.Add(new CommonSetting
                    { Key = "/FenFa/System/Config/UnSetMemberLevelDialog", ShopId = shopId, Value = "false" });
            }

            if (commonSettings.Any(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value
                    .toDateTime();
                commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value =
                    date.AddDays(15) < DateTime.Now ? "true" : "false";
            }

            if (commonSettings.Any(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value
                    .toDateTime();
                commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value =
                    date.AddDays(15) < DateTime.Now ? "true" : "false";
            }

            if (commonSettings.Any(x => x.Key == keyOrderDisplaySetting) == false)
            {
                //设置默认值
                commonSettings.Add(new CommonSetting
                {
                    Key = keyOrderDisplaySetting,
                    ShopId = shopId,
                    Value = CommonSettingService.DefaultOrderDisplaySetting.ToJson()
                });
            }

            #endregion

            // 页面按钮展示权限 
            var showPermDict = new Dictionary<string, bool>
            {
                { $"#{nameof(FxPermission.AddBindAgent)}", SiteContext.HasPermission(FxPermission.AddBindAgent) },
                { $"#{nameof(FxPermission.AgentInvite)}", SiteContext.HasPermission(FxPermission.AgentInvite) },
                {
                    $".{nameof(FxPermission.AgreeAndRefuseAgent)}",
                    SiteContext.HasPermission(FxPermission.AgreeAndRefuseAgent)
                },
                { $".{nameof(FxPermission.UnbindAgent)}", SiteContext.HasPermission(FxPermission.UnbindAgent) },
                { $".{nameof(FxPermission.SetPrePay)}", SiteContext.HasPermission(FxPermission.SetPrePay) },
            };

            //是否显示1688菜单
            var isShow1688Menu = _commonSettingService.GetIsShow1688Menu(fxUserId, SiteContext.Current.CurrentShopId);
            var checkResult =
                _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.SellerUnit);
            var isAddAvailable = checkResult.IsAvailable;

            var agentBingSupplierStatus = Enum.GetValues(typeof(AgentBingSupplierStatus)).Cast<int>()
                .ToDictionary(item => ((AgentBingSupplierStatus)item).ToString(), item => item);
            ;
            var newStatusDic = Enum.GetValues(typeof(AgentBingSupplierStatus)).Cast<object>()
                .ToDictionary(item => (int)item, item => ((AgentBingSupplierStatus)item).GetEnumDescription());

            var outStatus = Request["status"];
            var defaultMobile = Request["mobile"].ToString2();

            return SuccessResult(new
            {
                ShowPermDict = showPermDict,
                CommonSettings = commonSettings,
                IsShow1688Menu = isShow1688Menu,
                IsAddAvailable = isAddAvailable,
                AgentBingSupplierStatus = agentBingSupplierStatus,
                Status = newStatusDic,
                OutStatus = outStatus,
                DefaultMobile = defaultMobile,
                Title = "我的商家",
                MenuId = "MyAgent",
                MenuActive = "Authorization",
            });
        }

        /// <summary>
        /// Partner的CrossBorder页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPartnerCrossBorderPageInfo()
        {
            var isAddAvailable = true;
            var defaultShopId = Request["shopId"].ToString2();
            var token = Request.QueryString["token"];
            var supportPlatform = CustomerConfig.GetFxUserCrossBorderPlatform(token);
            return SuccessResult(new
            {
                // ReSharper disable once ConditionIsAlwaysTrueOrFalse
                IsAddAvailable = isAddAvailable,
                DefaultShopId = defaultShopId,
                AvailableCountInfo = "null",
                SupportPlatform = supportPlatform,
                Title = "我的店铺",
                MenuId = "Partner",
                MenuActive = "Authorization",
            });
        }

        /// <summary>
        /// System的DistributeSet页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetSystemDistributeSetPageInfo()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            var keyOrderDisplaySetting = CommonSettingService.OrderDisplaySetting;
            var isEditSellerRemarkSetting = SystemSettingKeys.IsEditSellerRemarkSetting; //允许厂家使用卖家备注
            var isEditAfterSaleRemarkSetting = SystemSettingKeys.IsEditAfterSaleRemarkSetting; //允许厂家使用售后备注

            var keys = new List<string>
            {
                "/FenFa/System/Config/IsShowCancelUser",
                "/FenFa/System/Config/IsSalePricePublic",
                "/FenFa/System/Config/IsAgentSendAddress",
                "/FenFa/System/Config/IsShopNamePublic",
                "/FenFa/System/Config/StockOutType",
                "/FenFa/System/Config/StockOutZHType",
                "/ErpWeb/OutAccount/Agent/UnSetPriceDailog",
                "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog",
                "/FenFa/System/Config/AgentProductInfoPublic",
                "/FenFa/System/Config/IsEnableQuote", // 是否允许厂家使用下游分销货源创建货品信息
                keyOrderDisplaySetting,
                isEditSellerRemarkSetting,
                isEditAfterSaleRemarkSetting
            };
            var commonSettings = _commonSettingService.GetSets(keys, shopId);

            //没有设置则用默认
            if (commonSettings.Any(x => x.Key == "/FenFa/System/Config/IsAgentSendAddress") == false)
            {
                var defaultIsAgentSendAddress = _commonSettingService.Get("/FenFa/System/Config/IsAgentSendAddress", 0);
                if (defaultIsAgentSendAddress != null)
                    commonSettings.Add(defaultIsAgentSendAddress);
            }

            if (commonSettings.Any(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value
                    .toDateTime();
                commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value =
                    date.AddDays(15) < DateTime.Now ? "true" : "false";
            }

            if (commonSettings.Any(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value
                    .toDateTime();
                commonSettings.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value =
                    date.AddDays(15) < DateTime.Now ? "true" : "false";
            }

            if (commonSettings.Any(x => x.Key == keyOrderDisplaySetting) == false)
            {
                //设置默认值
                commonSettings.Add(new CommonSetting
                {
                    Key = keyOrderDisplaySetting,
                    ShopId = shopId,
                    Value = CommonSettingService.DefaultOrderDisplaySetting.ToJson()
                });
            }

            if (commonSettings.Any(x => x.Key == isEditSellerRemarkSetting) == false)
            {
                //设置默认值
                commonSettings.Add(new CommonSetting
                {
                    Key = isEditSellerRemarkSetting,
                    ShopId = shopId,
                    Value = new OrderRemarkSetting() { Enable = false, NoteMode = 1 }.ToJson()
                });
            }

            if (commonSettings.Any(x => x.Key == isEditAfterSaleRemarkSetting) == false)
            {
                //设置默认值
                commonSettings.Add(new CommonSetting
                {
                    Key = isEditAfterSaleRemarkSetting,
                    ShopId = shopId,
                    Value = new OrderRemarkSetting { Enable = false, NoteMode = 1 }.ToJson()
                });
            }

            //默认值
            if (commonSettings.Any(x => x.Key == "/FenFa/System/Config/AgentProductInfoPublic") == false)
            {
                commonSettings.Add(new CommonSetting
                {
                    Key = "/FenFa/System/Config/AgentProductInfoPublic",
                    ShopId = shopId,
                    Value = new AgentProductInfoPublicSetting().ToJson()
                });
            }

            // 默认开
            if (commonSettings.Any(x => x.Key == "/FenFa/System/Config/IsEnableQuote") == false)
            {
                commonSettings.Add(new CommonSetting
                    { Key = "/FenFa/System/Config/IsEnableQuote", ShopId = shopId, Value = "true" });
            }

            var checkRule = _checkRuleService.getRule(fxUserId);
            return SuccessResult(new
            {
                CommonSettings = commonSettings,
                CheckRule = checkRule,
                Title = "分发设置",
                MenuId = "DistributeSet",
                MenuActive = "Authorization",
            });
        }

        #endregion


        #region 商品管理模块

        /// <summary>
        /// 店铺商品和代发商品页面
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetProductIndexPageInfo(string id)
        {
            SetCurrentFxUser();
            var commonPageModel = GetCommonPageModel(id);

            var times = new SyncStatusService().LastTimeProductByFxUserId(CurrentFxUserId);
            //绑定店铺
            var reqModel = new FxUserShopQueryModel { FxUserId = CurrentFxUserId };
            var fxUserShops = new List<FxUserShop>();
            var shops = fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToList();
            var shopsEffect = new List<FxUserForeignShop>().Select(x => new { x.NickName, x.ShopId, x.PlatformType });
            if (CustomerConfig.IsCrossBorderSite)
            {
                var fxUserForeignShopService = new FxUserForeignShopService();
                var fxUserForeignShops =
                    fxUserForeignShopService.GetList(reqModel)?.Item2 ?? new List<FxUserForeignShop>();
                fxUserForeignShops = fxUserForeignShops.Where(x => x.Status != FxUserShopStatus.UnBind).ToList();
                //绑定店铺
                shops = fxUserForeignShops.Select(x => new
                    { NickName = $"{x.NickName}-{x.SubPlatformType}", x.ShopId, x.PlatformType }).Distinct().ToList();
                //未过期店铺
                shopsEffect = fxUserForeignShops.Where(w => w.Status == FxUserShopStatus.Binded).Select(x =>
                        new { NickName = $"{x.NickName}-{x.SubPlatformType}", x.ShopId, x.PlatformType }).Distinct()
                    .ToList();
                fxUserShops = fxUserForeignShops.Select(f => f as FxUserShop).ToList();
            }
            else
            {
                fxUserShops = GetFxUserShops();
                //绑定店铺
                shops = fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToList();
                //未过期店铺
                shopsEffect = fxUserShops.Where(w => w.Status == FxUserShopStatus.Binded)
                    .Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToList();
            }

            //店铺对应的平台
            var pts = CustomerConfig.GetAllPlatformAuthLinks().ToList();
            if (!pts.Any(x => x.PlatformType == "Toutiao"))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "TouTiao",
                    Name = "抖音",
                    Index = 4
                });
            }

            if (pts.All(x => x.PlatformType != nameof(PlatformType.Virtual)))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "Virtual",
                    Name = "线下单",
                    Index = 6
                });
            }

            var platformTypes = pts.Where(x => shops.Any(n => n.PlatformType.ToLower() == x.PlatformType.ToLower()))
                .OrderBy(x => x.Index).Select(x => new { x.PlatformType, x.Name });
            //商家数据源
            var agents = _supplierUserService
                .GetAgentList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true)
                .Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct()
                .ToList();
            //厂家数据源
            var suppliers = _supplierUserService
                .GetSupplierList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true)
                ?.Select(x => new
                {
                    UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter
                }).Distinct().ToList();

            //
            var keys = new List<string>
            {
                "/ErpWeb/Product/PageSize",
                "/ErpWeb/Product/HideOfflineProduct",
            };
            var commSets = _commonSettingService.GetSets(keys, CurrentShopId);
            var pageSize = commSets.FirstOrDefault(x => x.Key == "/ErpWeb/Product/PageSize")?.Value ?? "20";
            var hideOfflineProduct =
                commSets.FirstOrDefault(x => x.Key == "/ErpWeb/Product/HideOfflineProduct")?.Value ?? "true";
            //
            var showPermDict = new Dictionary<string, bool>
            {
                { $"#{nameof(FxPermission.SyncProduct)}", SiteContext.HasPermission(FxPermission.SyncProduct) },
                { $"#{nameof(FxPermission.DeleteProduct)}", SiteContext.HasPermission(FxPermission.DeleteProduct) },
                {
                    $".{nameof(FxPermission.TriggerBindSupplier)}",
                    SiteContext.HasPermission(FxPermission.TriggerBindSupplier)
                },
                {
                    $".{nameof(FxPermission.SaveProductShortTitleOrWeight)}",
                    SiteContext.HasPermission(FxPermission.SaveProductShortTitleOrWeight)
                },
                {
                    $".{nameof(FxPermission.SetProductSettlementPrice)}",
                    SiteContext.HasPermission(FxPermission.SetProductSettlementPrice)
                },
                { $".{nameof(FxPermission.AbnormalProduct)}", SiteContext.HasPermission(FxPermission.AbnormalProduct) },
            };

            var isSyncButton = new SyncStatusService().GetIsShowProductSyncButton(CurrentFxUserId);
            //如果停止，则不检测
            if (CustomerConfig.IsSyncDisabledByProductExamine)
            {
                isSyncButton = "1";
            }

            return SuccessResult(new
            {
                LastTime = times,
                LastTimeRemark = "若您后台更新/上架了新的商品链接，请及时更新到店管家分销系统",
                PageSize = pageSize,
                Shops = shops,
                ShopsEffect = shopsEffect,
                PlatformTypes = platformTypes,
                Agents = agents,
                Suppliers = suppliers,
                HideOfflineProduct = hideOfflineProduct,
                ShowPermDict = showPermDict,
                IsShowSyncButton = isSyncButton,
                Title = commonPageModel.Title,
                IsIFrame = commonPageModel.IsIFrame
            });
        }

        /// <summary>
        /// 基础商品
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetNewBaseProductPageInfo()
        {
            SetCurrentFxUser();
            //厂家数据源
            var suppliers = _supplierUserService
                .GetSupplierList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true)?.Select(x => new
                {
                    UserName = x.SupplierMobileAndRemark,
                    FxUserId = x.SupplierFxUserId,
                    x.Status,
                    x.IsTop,
                    x.IsFilter
                }).Distinct().ToList();

            // 页码配置
            var pageKey = "/ErpWeb/BaseProduct/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, CurrentShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            //
            var inventoryKeys = new List<string>
                { "/Export/StockDetail/ExportUpdateTime", "ErpWeb/StockDetail/WarnStockNum" };
            var inventoryCommSets = _commonSettingService.GetSets(inventoryKeys, CurrentShopId);
            var warnStockNum =
                inventoryCommSets.FirstOrDefault(x => x.Key == "ErpWeb/StockDetail/WarnStockNum")?.Value ?? "";

            var pageSize = int.Parse(pageSizeSet?.Value ?? "20");

            return SuccessResult(new
            {
                Suppliers = suppliers,
                PageSize = pageSize,
                WarnStockNum = warnStockNum,
                Token = Request.QueryString["token"],
                Dbname = Request.QueryString["dbname"]
            });
        }

        /// <summary>
        /// 创建基础商品
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetCreateBaseProductPageInfo()
        {
            SetCurrentFxUser();
            return SuccessResult(new
            {
                WareHouseStatus = CheckWareHouseStatus()
            });
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetBaseProductSkuRelationDetailIndexPageInfo()
        {
            SetCurrentFxUser();
            var baseProductSkuUid = Request.Params["baseProductSkuUid"];
            var baseProductUid = Request.Params["baseProductUid"];
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;
            // 页码配置
            var pageKey = "/ErpWeb/BaseProductSkuRelationDetailList/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            var pageSize = int.Parse(pageSizeSet?.Value ?? "20");
            var model = LoadBaseProductDefaultConfig();
            return SuccessResult(new
            {
                BaseProductSku = new List<dynamic>(),
                PageSize = pageSize,
                IFrameSrc = string.Empty,
                UnIFrameSrc = string.Empty,
                Shops = model.Shops,
                ShopsEffect = model.ShopsEffect,
                PlatformTypes = model.PlatformTypes,
                Agents = model.Agents,
                Suppliers = model.Suppliers,
                SupplierUsers = model.SupplierUsers,
                AllAgents = model.AllAgents,
            });
        }


        /// <summary>
        /// 基础商品（关联明细页面）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetBaseOfPtSkuRelation_equal_productsPageInfo()
        {
            SetCurrentFxUser();
            var fxUserShops = GetFxUserShops();
            //绑定店铺
            var shops =
                fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToList();
            //商家数据源
            var dbAgents = _supplierUserService
                .GetAgentList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            var agents = dbAgents.Select(x =>
                new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToList();
            //
            var currentUser = SiteContext.Current.CurrentFxUser;
            var currentAgent = new SupplierUser
            {
                Mobile = currentUser.Mobile, Remark = currentUser.Remark, NickName = currentUser.NickName,
                UserFx = currentUser, FxUserId = CurrentFxUserId, IsTop = true
            };
            dbAgents.Insert(0, currentAgent);
            var allAgents = dbAgents
                .Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct()
                .ToList();
            //厂家数据源
            var suppliers = _supplierUserService
                .GetSupplierList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true)?.Select(x =>
                    new
                    {
                        UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop,
                        x.IsFilter
                    }).Distinct().ToList();

            return SuccessResult(new
            {
                Shops = shops,
                Agents = agents,
                AllAgents = allAgents,
                Suppliers = suppliers,
                WareHouseStatus = CheckWareHouseStatus()
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPrepareDistributionPageInfo()
        {
            SetCurrentFxUser();
            var token = Request.QueryString["token"];
            var supportPlatform = CustomerConfig.GetFxUserPlatform(token);
            var supportPlatformForListing = CustomerConfig.GetFxUserPlatformForListing(token);
            //厂家数据源
            var dbSuppliers =
                _supplierUserService.GetSupplierList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            var suppliers =
                dbSuppliers?.Select(x => new
                {
                    UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter
                }).Distinct().ToList();
            var supplierUsers =
                dbSuppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId })
                    .GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");

            return SuccessResult(new
            {
                SupportPlatformForListing = supportPlatformForListing,
                Suppliers = suppliers,
                SupplierUsers = supplierUsers,
                WareHouseStatus = CheckWareHouseStatus()
            });
        }

        /// <summary>
        /// 基础商品
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetSelectionDistributionPageInfo()
        {
            SetCurrentFxUser();
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///商品采集+商品采集箱+全球商品
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetCollectViewPageInfo()
        {
            SetCurrentFxUser();
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetCreateBasePlatformProductPageInfo()
        {
            SetCurrentFxUser();
            //厂家数据源
            var dbSuppliers =
                _supplierUserService.GetSupplierList(CurrentFxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            var suppliers =
                dbSuppliers?.Select(x => new
                {
                    UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter
                }).Distinct().ToList();
            var supplierUsers =
                dbSuppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId })
                    .GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");


            return SuccessResult(new
            {
                Suppliers = suppliers,
                SupplierUsers = supplierUsers,
                WareHouseStatus = CheckWareHouseStatus()
            });
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetBaseOfPtProductsPageInfo()
        {
            SetCurrentFxUser();
            var sysShopId = SiteContext.Current.CurrentShopId;
            // 获取当前页码配置
            const string pageKey = "/ErpWeb/BaseOfPtProducts/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, sysShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            var pageSize = int.Parse(pageSizeSet?.Value ?? "200");
            return SuccessResult(new
            {
                PageSize = pageSize
            });
        }


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetBaseOfPtSkuRelation_detail_productsPageInfo()
        {
            SetCurrentFxUser();
            return SuccessResult(new
            {
                WareHouseStatus = CheckWareHouseStatus()
            });
        }

        /// <summary>
        /// 页面初始配置
        /// </summary>
        private LoadBaseProductDefaultConfigModel LoadBaseProductDefaultConfig()
        {
            var model = new LoadBaseProductDefaultConfigModel();
            // 渠道数据类型商家 // 渠道数据类型店铺
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;

            var pts = CustomerConfig.GetAllPlatformAuthLinks().ToList();
            if (!pts.Any(x => x.PlatformType == "Toutiao"))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "TouTiao",
                    Name = "抖音",
                    Index = 4
                });
            }

            if (!pts.Any(x => x.PlatformType == PlatformType.Virtual.ToString()))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "Virtual",
                    Name = "线下单",
                    Index = 6
                });
            }

            var fxUserShops = GetFxUserShops();
            //绑定店铺
            model.Shops =
                fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToList();
            //未过期店铺
            model.ShopsEffect =
                fxUserShops.Where(w => w.Status == FxUserShopStatus.Binded)
                    .Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToList();

            var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
            var sids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).ToList();
            var shops = _shopService.GetShopByIds(sids, "id,shopId,nickName,platformtype");
            switch (CustomerConfig.CloudPlatformType)
            {
                case nameof(PlatformType.Pinduoduo):
                    shops = shops.Where(x =>
                        x.PlatformType == nameof(PlatformType.Pinduoduo) ||
                        x.PlatformType == nameof(PlatformType.KuaiTuanTuan)).ToList();
                    break;
                case nameof(PlatformType.Jingdong):
                    shops = shops.Where(x => CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType))
                        .ToList();
                    break;
                case nameof(PlatformType.TouTiao):
                    shops = shops.Where(x => x.PlatformType == nameof(PlatformType.TouTiao)).ToList();
                    break;
                default:
                    shops = shops.Where(x =>
                        x.PlatformType != nameof(PlatformType.Pinduoduo) &&
                        x.PlatformType != nameof(PlatformType.KuaiTuanTuan) &&
                        CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType) == false).ToList();
                    break;
            }

            fxUserShops.ForEach(fx =>
            {
                if (shops.All(s => s.Id != fx.ShopId))
                    shops.Add(new Shop { Id = fx.ShopId, PlatformType = fx.PlatformType });
            });

            //店铺对应的平台
            model.PlatformTypes = pts.Where(x => shops.Any(n => n.PlatformType.ToLower() == x.PlatformType.ToLower()))
                .OrderBy(x => x.Index).Select(x => new { x.PlatformType, x.Name }).ToList();


            //商家数据源
            var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            model.Agents = agents
                .Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct()
                .ToList();
            //厂家数据源
            var suppliers =
                _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            model.Suppliers = suppliers?.Select(x => new
                {
                    UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter
                })
                .Distinct().ToList();
            model.SupplierUsers = suppliers
                ?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId })
                .GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            var currentUser = SiteContext.Current.CurrentFxUser;
            var currentAgent = new SupplierUser
            {
                Mobile = currentUser.Mobile, Remark = currentUser.Remark, NickName = currentUser.NickName,
                UserFx = currentUser, FxUserId = fxUserId, IsTop = true
            };
            agents.Insert(0, currentAgent);
            model.AllAgents = agents
                .Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct()
                .ToList();

            return model;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        #endregion


        #region 售后管理模块

        /// <summary>
        ///手工售后单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetManualAfterSalePageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///平台售后单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetManualAfterSaleIndexPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        #endregion


        #region 财务结算

        /// <summary>
        ///对账设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetPriceSettingPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///对账中心
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetBillManagementPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///厂家底单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetWaybillCodeListIndexPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///已发货明细
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetSendOrderIndexPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        /// <summary>
        ///运费设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetNewShippingFeeSetPageInfo()
        {
            return SuccessResult(new
            {
            });
        }

        #endregion


        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetCommonPagePageInfo(string id)
        {
            var commonPageModel = GetCommonPageModel(id);

            return SuccessResult(new
            {
                IFrameSrc = commonPageModel.IFrameSrc,
                TbmcShops = commonPageModel.TbmcShops,
                SysShopDbName = commonPageModel.SysShopDbName,
                AgentDb = commonPageModel.AgentDb,
                AgentDbCloud = commonPageModel.AgentDbCloud,
                MenuId = commonPageModel.MenuId,
                IsShowCrossBorder = commonPageModel.IsShowCrossBorder,
                IsOrderLifeCycleRigth = commonPageModel.IsOrderLifeCycleRigth,
                FxUserLastSyncTime = default(DateTime?),
            });
        }
    }
}