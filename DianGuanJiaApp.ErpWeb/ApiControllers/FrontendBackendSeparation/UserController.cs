using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.APIUser;
using DianGuanJiaApp.Data.Repository.Settings;
using DianGuanJiaApp.ErpWeb.Controllers;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace DianGuanJiaApp.ErpWeb.ApiControllers.FrontendBackendSeparation
{
    /// <summary>
    /// UserAPI控制器
    /// </summary>
    public class UserController : SeparationBaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        public UserController()
        {
        }

        private readonly FxUserShopService _fxUserShopService = new FxUserShopService();
        private readonly ShopService _shopService = new ShopService();
        private readonly CommonSettingService _commonSettingService = new CommonSettingService();
        private readonly SupplierUserService _supplierUserService = new SupplierUserService();

        /// <summary>
        /// 获取用户基本信息
        /// </summary>
        [HttpGet]
        public AjaxResult<UserInfoModel> GetUserInfo()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var mainMobile = currentSite.CurrentFxUser.Mobile;
            var registeredDays = (DateTime.Now - currentSite.CurrentFxUser.CreateTime).TotalDays.ToString("F0");
            // 子账号有仅绑定微信的情况，mobile为空，产品要求显示微信的名称
            var mobile =
                currentSite.SubFxUserId > 0
                    ? currentSite.SubFxUser.Mobile.IsNullOrEmpty()
                        ? currentSite.SubFxUser.WxNickName
                        : currentSite.SubFxUser.Mobile
                    : currentSite.CurrentFxUser.Mobile;
            var fxUserNickName = SiteContext.Current.CurrentFxUser.NickName;
            var isShowCrossBorder = SiteContext.Current.IsShowCrossBorder;
            var userFlag = SiteContext.Current.CurrentFxUser.UserFlag.ToString2();
            //
            var isUseColdDb = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage() &&
                              SiteContext.Current.IsUseColdDb;


            var version = SiteContext.Current.CurrentLoginShop.Version.ToString2();
            var pageVersionClassName = GetFxUserVersionCssClassName(version);
            //
            string nickName;
            var shopName = currentSite.CurrentLoginShop.NickName;
            var shopId = currentSite.CurrentShopId;
            if (SiteContext.Current.SubFxUserId > 0)
            {
                //子账号兼容需求调整【ID1035813】
                nickName = currentSite.SubFxUser.NickName;
                nickName = nickName.IsNullOrEmpty() ? "" : nickName;
                nickName = nickName.Length > 4 ? nickName.Substring(0, 4) + "..." : nickName;
                nickName = $"{currentSite.CurrentFxUser.Mobile}[{nickName}]";
                //前端页面标题追加的值
                if (string.IsNullOrEmpty(currentSite.SubFxUser.NickName) == false)
                    shopName = currentSite.SubFxUser.NickName;
                else if (string.IsNullOrEmpty(currentSite.SubFxUser.Mobile) == false)
                    shopName = currentSite.SubFxUser.Mobile;
            }
            else
            {
                //子账号兼容需求调整【ID1035813】
                nickName = $"{currentSite.CurrentFxUser.Mobile}[主账号]";
            }

            //是否显示1688菜单
            var isShow1688Menu = _commonSettingService.GetIsShow1688Menu(fxUserId, SiteContext.Current.CurrentShopId);
            //是否是白名单用户
            var isWhite = SiteContext.Current.IsWhiteUser;


            //菜单数据,只有使用新导航样式才查询
            var navService = new FenDanSystemNavService();
            List<FenDanSystemNav> navs = null;

            // 当前环境，根据环境获取不同的菜单
            var navVersion = CustomerConfig.FxPermissionVersion;

            if (SiteContext.Current.IsOnlyListingUser)
                navVersion = "Only_Listing";

            var baseController = new DianGuanJiaApp.Controllers.BaseController();

            if (!currentSite.IsOldNavStyle)
            {
                var navSetting = navService.GetNavSettings(SiteContext.Current.CurrentShopId, navVersion, true);
                navs = navSetting.Item1;
                //1688菜单的处理
                baseController.HandleNav1688(isShow1688Menu, ref navs);
                //处理利润统计白名单
                baseController.HandleProfitStatistics(navs);
                //处理跨境采集菜单
                baseController.HandleCrossBorderProfitStatistics(navs);

                //处理短视频发布白名单
                baseController.HandShopShopVideo(navs);
            }
            // 240619xwm：子账号功能改造，旧版菜单也由后台给
            else
            {
                navs = navService.GetNavSettings(SiteContext.Current.CurrentShopId, navVersion).Item1;
                //1688菜单的处理
                baseController.HandleNav1688(isShow1688Menu, ref navs);
                //处理利润统计白名单
                baseController.HandleProfitStatistics(navs);

                //处理跨境采集菜单
                baseController.HandleCrossBorderProfitStatistics(navs);
                //处理短视频发布白名单
                baseController.HandShopShopVideo(navs);
            }

            //分单白名单
            if (isWhite == false && CustomerConfig.IsFxWhiteUserFilter && !SiteContext.Current.IsOnlyListingUser)
                baseController.HandleWhiteUserNav(ref navs);
            else baseController.RemoveNavItem(navs, "商品管理", "自营商品");
            
            // 如果非即时零售商家端
            if (SiteContext.Current.IsSaleShopAgent == false) baseController.RemoveNavItem(navs, "首页", "货源中心");

            // 顶部按钮对应权限字典 是否展示站内信按钮/快速邀请/异常商品消息
            var topBtnPermDict = new Dictionary<string, bool>()
            {
                { nameof(FxPermission.SiteMessage), SiteContext.HasPermission(FxPermission.SiteMessage) },
                { nameof(FxPermission.AgentInvite), SiteContext.HasPermission(FxPermission.AgentInvite) },
                { nameof(FxPermission.SupplierInvite), SiteContext.HasPermission(FxPermission.SupplierInvite) },
                { nameof(FxPermission.AbnormalProduct), SiteContext.HasPermission(FxPermission.AbnormalProduct) },
            };

            var hasModifyRemarkApi =
                CustomerConfig.HasModifyRemarkApi(SiteContext.Current.CurrentLoginShop.PlatformType);
            var isTouTiaoXi = CustomerConfig.IsTouTiaoXi(SiteContext.Current.CurrentLoginShop.PlatformType);
            var isShowSiteMessage = _commonSettingService.IsWhiteUserBySiteMessage();
            var navQuickEntry = new List<FenDanSystemNav>();
            var navQuickEntrySort = new List<string>();
            //
            if (!currentSite.IsOldNavStyle)
            {
                var navSetting = navService.GetNavSettings(SiteContext.Current.CurrentShopId, navVersion, true);
                navQuickEntry = navSetting.Item2;
                navQuickEntrySort = navSetting.Item3;
            }

            var model = new UserInfoModel
            {
                FxUserId = fxUserId,
                CurrentShopId = currentSite.CurrentShopId,
                PlatformType = currentSite.CurrentLoginShop.PlatformType,
                MainMobile = mainMobile,
                RegisteredDays = registeredDays,
                Mobile = mobile,
                IsWhiteUser = SiteContext.Current.IsWhiteUser,
                IsSaleShopAgent= SiteContext.Current.IsSaleShopAgent,
                FxUserNickName = fxUserNickName,
                IsShowCrossBorder = isShowCrossBorder,
                UserFlag = userFlag,
                IsUseColdDb = isUseColdDb,
                PageVersionClassName = pageVersionClassName,
                TraceId = Guid.NewGuid().ToString().ToShortMd5(),
                FxUserAddres = new FxUserAddress(), //缓存性能优化:默认不加载地址数据
                //SiteContextCurrent = SiteContext.Current,
                CurrentLoginShopModel = SiteContext.Current.CurrentLoginShopModel,
                NickName = nickName,
                ShopName = shopName,
                FenDanSystemNavs = navs,
                TopBtnPermDict = topBtnPermDict,
                IsShow1688Menu = isShow1688Menu,
                HasModifyRemarkApi = hasModifyRemarkApi,
                IsTouTiaoXi = isTouTiaoXi,
                Token = PageToken,
                IsShowSiteMessage = isShowSiteMessage,
                AllShopsModel = "[]", //去除前端所有店铺加载，防止页面某些地方保持。若发现前端异常，可在异常页面上加上对应所有店铺数据d,
                NavQuickEntry = navQuickEntry,
                NavQuickEntrySort = navQuickEntrySort,
                ShopId = shopId
            };
            return SuccessResult(model);
        }

        /// <summary>
        /// 获取分区信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<AreasInfoModel> GetAreasInfo()
        {
            var currentSite = SiteContext.Current;
            var dbAreas = currentSite.CurrentDbAreaConfig.Select(x => x.DbNameConfig).OrderBy(x => x.Id).Select(y =>
                new
                {
                    DbName = DES.EncryptDES(y.DbName, CustomerConfig.LoginCookieEncryptKey),
                    y.NickName,
                    y.ApplicationName
                }).ToList();

            var curDbName =
                _commonSettingService.GetString("/ErpWeb/DefaultPlatformDbArea", SiteContext.Current.CurrentShopId);
            var dbName = curDbName.IsNotNullOrEmpty() && dbAreas.Any(x => x.DbName == curDbName)
                ? curDbName
                : DES.EncryptDES(currentSite.CurrentDbConfig?.DbNameConfig.DbName,
                    CustomerConfig.LoginCookieEncryptKey);
            var model = new AreasInfoModel
            {
                DbArea = dbAreas,
                DbName = dbName
            };
            return SuccessResult(model);
        }

        /// <summary>
        /// 获取待绑定的店铺
        /// </summary>
        /// <code>
        ///  var sid = Request["sid"];
        ///  var pt = Request["pt"];
        /// </code>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<WaitBindShopsModel> GetWaitBindShop()
        {
            var sid = Request["sid"];
            var pt = Request["pt"];
            var tuple = new GeneralizeIndexController().GetWaitBindShop(sid, pt);
            var model = new WaitBindShopsModel
            {
                WaitBindShop = tuple?.Item1
            };
            return SuccessResult(model);
        }

        /// <summary>
        /// 获取店铺集合
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<OutShopsModel> GetOutShops()
        {
            var model = new OutShopsModel
            {
                OutShops = _shopService.GetAllShops()?.Select(x => new { x.ShopName, x.ShopId }).Distinct()
            };
            return SuccessResult(model);
        }

        /// <summary>
        /// 获取店铺集合
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<ShopsModel> GetShops()
        {
            var currentSite = SiteContext.Current;
            var model = new ShopsModel
            {
                Shops = _fxUserShopService.GetShopsByFxUserId(currentSite.CurrentFxUserId, false)
            };
            return SuccessResult(model);
        }

        /// <summary>
        /// 获取轻应用信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetQingAppInfo()
        {
            var info1688Model = new OpenInfo1688Model
            {
                IsOpenQing = QingServiceBase.Instance.IsOpenQing,
                IsOpenAlibabaOld = QingServiceBase.Instance.IsOpenAlibabaOld,
                IsOpen1688Shops = QingServiceBase.Instance.IsOpen1688Shops,
                Is1688new = QingServiceBase.Instance.Is1688new
            };
            return SuccessResult(new
            {
                OpenInfo1688 = info1688Model
            });
        }


        /// <summary>
        /// 获取厂家数据源
        /// </summary>
        [HttpGet]
        public dynamic GetSuppliersV1()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, needEncryptAccount: true)?.Select(x =>
                new
                {
                    UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId,
                    x.SupplierFxUserId,
                    x.Status,
                    x.IsTop,
                    x.NickName,
                    x.RemarkName,
                    x.IsFilter
                }).Distinct().ToList();

            return SuccessResult(new
            {
                Suppliers = suppliers
            });
        }

        /// <summary>
        /// 获取厂家数据源
        /// </summary>
        [HttpGet]
        public dynamic GetSuppliersV2()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var suppliers = _supplierUserService
                .GetSupplierList(fxUserId, "", null, 1, 10000, needEncryptAccount: true).Item2;
            return SuccessResult(new
            {
                Suppliers = suppliers
            });
        }

        /// <summary>
        /// 获取商家数据源
        /// </summary>
        [HttpGet]
        public dynamic GetAgentsV1()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId, needEncryptAccount: true)?.Select(x =>
                    new
                    {
                        UserName = x.AgentMobileAndRemark,
                        x.FxUserId, x.Status,
                        x.IsTop,
                        x.NickName,
                        x.Remark
                    })
                .Distinct();
            return SuccessResult(new
            {
                Agents = agents
            });
        }

        /// <summary>
        /// 获取商家数据源
        /// </summary>
        [HttpGet]
        public dynamic GetAgentsV2()
        {
            var currentSite = SiteContext.Current;
            var fxUserId = currentSite.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId, "", null, 1, 10000, needEncryptAccount: true)
                .Item2;
            return SuccessResult(new
            {
                Agents = agents
            });
        }

        /// <summary>
        /// 获取支持的平台
        /// </summary>
        /// <code>
        /// Request.QueryString["token"]
        /// </code>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetSupportPlatform()
        {
            var token = Request.QueryString["token"];
            var supportPlatform = CustomerConfig.GetFxUserPlatform(token);
            return SuccessResult(new
            {
                SupportPlatform = supportPlatform
            });
        }

        /// <summary>
        /// 获取系统相关信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public dynamic GetSystemInfo()
        {
            var isShowCrossBorder = SiteContext.Current.IsShowCrossBorder;
            var qqLink = "https://wpa1.qq.com/biYkihFL?_type=wpa&qidian=true"; //联系客服的链接
            if (isShowCrossBorder)
            {
                qqLink = "https://wpa1.qq.com/L18kefQo?_type=wpa&qidian=true";
            }

            return SuccessResult(new
            {
                CustomerConfig.SystemVersion,
                CustomerConfig.CloudPlatformType,
                CustomerConfig.IsDebug,
                CustomerConfig.IsCrossBorderSite,
                CustomerConfig.DefaultSubAccountPassword,
                QQLink = qqLink
            });
        }
    }
}