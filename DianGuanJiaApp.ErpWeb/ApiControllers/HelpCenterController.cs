using System;
using System.Collections.Generic;
using System.Web.Http;
using DianGuanJiaApp.Data.Entity.HelpCenter;
using DianGuanJiaApp.Data.Enum.HelpCenter;
using DianGuanJiaApp.Data.Model.HelpCenter;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services.Services.HelpCenter;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 帮助中心控制器，前台 
    /// </summary>
    [ControllerLock]
	public class HelpCenterController : BaseApiController
    {

        private readonly HelpContainerService _helpContainerService = new HelpContainerService();
        private readonly HelpArticleService _helpArticleService = new HelpArticleService();
        private readonly HelpArticleFeedbackService _feedbackService = new HelpArticleFeedbackService();

        /// <summary>
        /// 获取版块
        /// </summary>
        /// <param name="siteVersion"></param>
        /// <param name="userVersion"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HelpContainer>> GetContainerTree(HelpCenterSiteVersion siteVersion, HelpCenterUserVersion userVersion)
        {
            var tree = _helpContainerService.GetContainerTree(siteVersion, userVersion);
            return SuccessResult(tree);
        }

        /// <summary>
        /// 获取单个文章
        /// </summary>
        /// <param name="articleCode"></param>
        /// <param name="from">查询文章的来源，如果是关键字搜索的，就传入关键字</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<HelpArticleDocument> GetArticleDocument(string articleCode,string from)
        {
            if (articleCode.IsNullOrEmpty())
                return FailedResult(new HelpArticleDocument(), "请指定需要查询的文章编码");

            return SuccessResult(_helpArticleService.GetArticleDocument(articleCode,from));
        }

        /// <summary>
        /// 获取文章栏目树
        /// </summary>
        /// <param name="siteVersion"></param>
        /// <param name="userVersion"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HelpArticleColumnModel>> GetArticleColumnTree(HelpCenterSiteVersion siteVersion, HelpCenterUserVersion userVersion)
        {
            return SuccessResult(_helpArticleService.GetArticleColumnTree(siteVersion, userVersion));
        }

        /// <summary>
        /// 提交反馈
        /// </summary>
        /// <param name="feedback"></param>
        /// <param name="verifyCode">验证码</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> SubmitFeedback(HelpArticleFeedbackDocument feedback,string verifyCode)
        {
            if (!CheckValidCode(verifyCode))
                return FailedResult(string.Empty, "验证码错误，请重新输入");

            _feedbackService.SubmitFeedback(feedback);

            return SuccessResult(string.Empty);
        }

        /// <summary>
        /// 查询文章标题
        /// </summary>
        /// <param name="title"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<Dictionary<string, string>> SearchArticleTitle(string title)
        {
            if (title.IsNullOrEmpty())
                return SuccessResult(new Dictionary<string, string>());
            //var list = _helpArticleService.GetList(new List<long>{1,2,3,4,5,6});
            //_helpArticleService.SaveArticleDocument(list);
            //_helpArticleService.SubmitFeedbackTest();
            return SuccessResult(_helpArticleService.SearchArticleTitle(title));
            //SearchArticleDocument(new HelpArticleQueryModel()
            //{
            //    KeyWord = "测试",
            //    Status = 1,
            //});
            //return SuccessResult(new Dictionary<string, string>());
        }

        /// <summary>
        /// 查询文章
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RateLimit]
        public AjaxResult<object> SearchArticleDocument(HelpArticleQueryModel model)
        {
            if (model == null)
                return SuccessResult<object>(null);
            if(model.KeyWord.IsNullOrEmpty())
                return SuccessResult<object>(null);

            var (total, list) = _helpArticleService.SearchArticleDocument(model);
            return SuccessResult<object>(new {Total=total,List = list});
        }

        /// <summary>
        /// 查询文章，先校验 验证码
        /// </summary>
        /// <param name="model"></param>
        /// <param name="validCode"></param>
        /// <returns></returns>
        [HttpPost]
        [RateLimit(true)]
        public AjaxResult<object> SearchWithValidCode(HelpArticleQueryModel model, string validCode)
        {
            return SearchArticleDocument(model);
        }


        /// <summary>
        /// 检查验证码是否正确
        /// </summary>
        /// <param name="code">验证码</param>
        /// <returns></returns>
        private static bool CheckValidCode(string code)
        {
            if (Utility.Other.VerifyCode.CheckVerifyCode(code))
                return true;
            return false;
        }

    }
}