using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using DianGuanJiaApp.Data.Entity.HelpCenter;
using DianGuanJiaApp.Data.Enum.HelpCenter;
using DianGuanJiaApp.Data.Model.HelpCenter;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services.Services.HelpCenter;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 帮助中心后台Api接口
    /// </summary>
    [FxAdminSessionAuthFilter]
    public class HelpCenterBackendController : ApiController
    {

        private readonly HelpArticleService _helpArticleService = new HelpArticleService();
        private readonly HelpContainerService _helpContainerService = new HelpContainerService();

        #region 后台接口

        /// <summary>
        /// 保存文章目录
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> SaveArticle(HelpArticleSaveModel model)
        {
            if(model.IsNullOrEmpty())
                return FailedResult(string.Empty,"参数错误");

            if(model.Articles.IsNullOrEmptyList() && model.RemoveArticleCodes.IsNullOrEmptyList())
                return FailedResult(string.Empty,"未传入需要保存或删除的文章");

            if (model.Articles?.SkipWhile(a => a.ParentArticleCode.IsNullOrEmpty()).Any() ?? false)
                return FailedResult(string.Empty,"每次仅允许保存一个目录");

            if((model.Articles?.Any(a=>a.Status == 1) ?? false) && (model.SiteVersions.IsNullOrEmptyList() || model.UserVersions.IsNullOrEmptyList()))
                return FailedResult(string.Empty,"文章发布时，需要指定站点版本和用户版本");

            if(model.Articles?.Any(a=>a.ArticleCode.IsNullOrEmpty()) ?? false)
                return FailedResult(string.Empty,"文章唯一码不能为空");


            _helpArticleService.SaveArticle(model);

            return SuccessResult("保存成功");
        }

        ///// <summary>
        ///// 删除文章
        ///// </summary>
        ///// <param name="articleCodes"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public AjaxResult<string> RemoveArticle(List<string> articleCodes)
        //{
        //    if (articleCodes.IsNullOrEmptyList())
        //        return SuccessResult(string.Empty);

        //    _ = _helpArticleService.RemoveArticle(articleCodes);

        //    return SuccessResult(string.Empty);
        //}

        /// <summary>
        /// 保存版块
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> SaveContainer(HelpContainerSaveModel model)
        {
            if (model.IsNullOrEmpty() || model.Containers.IsNullOrEmptyList())
                return FailedResult(string.Empty,"参数错误");

            if (model.Containers.SkipWhile(a => a.ParentContainerCode.IsNullOrEmpty()).Any())
                return FailedResult(string.Empty,"每次仅允许保存一组版块");

            if (model.SiteVersion.IsNullOrEmpty() || model.UserVersion.IsNullOrEmpty())
                return FailedResult(string.Empty, "请指定站点版本和用户版本");

            _helpContainerService.SaveContainer(model);

            return SuccessResult(string.Empty,"保存成功");
        }

        /// <summary>
        /// 获取文章目录树
        /// </summary>
        /// <param name="siteVersion"></param>
        /// <param name="userVersion"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HelpArticle>> GetArticleTree(HelpCenterSiteVersion? siteVersion, HelpCenterUserVersion? userVersion)
        {
            var tree = _helpArticleService.GetArticleTree(siteVersion, userVersion);
            return SuccessResult(tree);
        }

        /// <summary>
        /// 获取文章列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<object> GetArticleList(HelpArticleQueryModel model)
        {
            var (total,list) = _helpArticleService.GetArticleList(model);
            return SuccessResult<object>(new{ List = list,Total = total});
        }

        /// <summary>
        /// 获取容器目录树
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<HelpContainer>> GetContainerTree(HelpCenterSiteVersion siteVersion, HelpCenterUserVersion userVersion)
        {
            var tree = _helpContainerService.GetContainerTree(siteVersion, userVersion);
            return SuccessResult(tree);
        }

        /// <summary>
        /// 恢复文章
        /// </summary>
        /// <param name="articleCode"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<bool> RestoreArticle(string articleCode)
        {
            if (articleCode.IsNullOrEmpty())
                return SuccessResult(true);

            _helpArticleService.RestoreArticle(new List<string>{articleCode });

            return SuccessResult(true);
        }

        /// <summary>
        /// 获取唯一码
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<string> GetUniqueCode()
        {
            return SuccessResult(Guid.NewGuid().ToString().ToShortMd5()) ;
        }
        #endregion


        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        private static AjaxResult<T> SuccessResult<T>(T data, string message = "成功！")
        {
            return new AjaxResult<T>()
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        private static AjaxResult<T> FailedResult<T>(T data, string message)
        {
            return new AjaxResult<T>()
            {
                Success = false,
                Data = data,
                Message = message
            };
        }
    }


}