; (function (win) {  

    win.ComViewBag = {  //以下均为同步  ComViewBag公共视图数据对象  如单独页面视图数据不要写在里面

        GetUserInfo: function () {  //用户信息
            if (JSON.stringify(commonModule.CommonUserInfo) != "{}" && commonModule.CommonUserInfo != undefined) {
                return commonModule.CommonUserInfo;
            }
            var UserInfo = {};
            commonModule.Ajax({
                url: '/api/user/GetUserInfo',
                async: false, 
                type:'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return UserInfo;
                    }
                    UserInfo = rsp.Data;
                }
            });
            return UserInfo;
        },
        GetOpenInfo1688: function () {   //1688轻应用信息
            var OpenInfo1688 = {};
            commonModule.Ajax({
                url: '/api/user/GetQingAppInfo',
                async: false,  
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return OpenInfo1688;
                    }
                    OpenInfo1688 = rsp.Data.OpenInfo1688;
                }
            });
            return OpenInfo1688;
        },
        GetSupportPlatform: function () {   //获取支持所有的平台
            var supportPlatforms = [];
            commonModule.Ajax({
                url: '/api/user/GetSupportPlatform',
                async: false,  
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return supportPlatforms;
                    }
                    supportPlatforms = rsp.Data.SupportPlatform;
                }
            });
            return supportPlatforms;
        },
        GetWaitBindShop: function () {   //获取支持所有的平台
            var WaitBindShop = null;
            commonModule.Ajax({
                url: '/api/user/GetWaitBindShop',
                async: false,  
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return WaitBindShop;
                    }
                    WaitBindShop = rsp.Data.WaitBindShop;
                }
            });
            return WaitBindShop;
        },
        GetAgents: function () {   //获取商家数据源
            var AgentList = [];
            commonModule.Ajax({
                url: '/api/user/GetAgentsV1',
                async: false, 
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return AgentList;
                    }
                    AgentList = rsp.Data.Agents;
                }
            });
            return AgentList;
        },
        GetSuppliers: function () {   //获取厂家数据源
            var SupplierList = [];
            commonModule.Ajax({
                url: '/api/user/GetSuppliersV1',
                async: false,  
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return SupplierList;
                    }
                    SupplierList = rsp.Data.Suppliers;
                }
            });
            return SupplierList;
        },
        GetAreasInfo: function () {   //获取分区
            if (JSON.stringify(commonModule.CommonGetAreasInfo) != "{}" && commonModule.CommonGetAreasInfo != undefined) {
                return commonModule.CommonGetAreasInfo;
            }
            var AreasInfo = {};
            AreasInfo.DbArea = [];
            AreasInfo.DbName = "";
            commonModule.Ajax({
                url: '/api/user/GetAreasInfo',
                async: false,
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return AreasInfo;
                    }
                    AreasInfo = rsp.Data;
                }
            });
            return AreasInfo;

        },
        GetOutShops: function () {   //获取分区
            var OutShops = [];
            commonModule.Ajax({
                url: '/api/user/GetOutShops',
                async: false,
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return OutShops;
                    }
                    OutShops = rsp.Data.OutShops;
                }
            });
            return OutShops;
        },
        GetPartnerIndexPageInfo: function () {
            var PartnerIndexPageInfo = {
                "IsAddAvailable": false,
                "IsTouTiaoOldUser": false,
                "HasTwoApp": false,
                "IsWxVideoOldUser": "false",
                "ShowPermDict": {},
                "DefaultShopId": "",
                "AvailableCountInfo": "null"
            }
            commonModule.Ajax({
                url: '/api/Page/GetPartnerIndexPageInfo',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return PartnerIndexPageInfo;
                    }
                    PartnerIndexPageInfo = rsp.Data;
                }
            });
            return PartnerIndexPageInfo;
        },

        GetPartnerCrossBorderPageInfo : function () {
            var PartnerCrossageInfo = {
                "IsAddAvailable": false,
                "DefaultShopId": "",
                "AvailableCountInfo": "null",
                "SupportPlatform": ""
            }
            commonModule.Ajax({
                url: '/api/Page/GetPartnerCrossBorderPageInfo',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return PartnerCrossageInfo;
                    }
                    PartnerCrossageInfo = rsp.Data;
                }
            });
            return PartnerCrossageInfo;
        },
        GetPartnerMySupplierPageInfo: function () {
            var MySupplierPageInfo = {
                "AgentBingSupplierStatus": {},
                "CommonSettings": {},
                "IsAddAvailable": false,
                "ApplyPrepayStatus": {},
                "ShowPermDict": {},
                "SupplierUsers": [],
                "Status": {}
            };
            commonModule.Ajax({
                url: '/api/page/GetPartnerMySupplierPageInfo',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return MySupplierPageInfo;
                    }
                    MySupplierPageInfo = rsp.Data;
                }
            });
            return MySupplierPageInfo;
        },
        GetPartnerMyAgentPageInfo: function () {
            var MyAgentPageInfo = {
                "AgentBingSupplierStatus": {},
                "CommonSettings": {},
                "IsAddAvailable": false,
                "ApplyPrepayStatus": {},
                "ShowPermDict": {},
                "SupplierUsers": [],
                "Status": {}
            };
            commonModule.Ajax({
                url: '/api/page/GetPartnerMyAgentPageInfo',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return MyAgentPageInfo;
                    }
                    MyAgentPageInfo = rsp.Data;
                }
            });
            return MyAgentPageInfo;
        },
        GetSystemDistributeSetPageInfo: function () {
            var DistributeSetPageInfo = {
                CheckRule: {},
                CommonSettings:[]
            };
            commonModule.Ajax({
                url: '/api/page/GetSystemDistributeSetPageInfo',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return DistributeSetPageInfo;
                    }
                    DistributeSetPageInfo = rsp.Data;
                }
            });
            return DistributeSetPageInfo;
        },
        GetShops: function () {
            var Shops = [];
            commonModule.Ajax({
                url: '/api/user/GetShops',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return Shops;
                    }
                    Shops = rsp.Data.Shops || [];
                }
            });
            return Shops;
        },
        GetAgentsV2: function () {
            var FxUserAgents = [];
            commonModule.Ajax({
                url: '/api/user/GetAgentsV2',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return FxUserAgents;
                    }
                    FxUserAgents = rsp.Data.Agents || [];
                }
            });
            return FxUserAgents;
        },
        GetSuppliersV2: function () {
            var FxUserSuppliers = [];
            commonModule.Ajax({
                url: '/api/user/GetSuppliersV2',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return FxUserSuppliers;
                    }
                    FxUserSuppliers = rsp.Data.Suppliers || [];
                }
            });
            return FxUserSuppliers;
        },
        GetSystemInfo: function () {  //
            if (JSON.stringify(commonModule.CommonSystemInfo) != "{}" && commonModule.CommonSystemInfo != undefined) {
                return commonModule.CommonSystemInfo;
            }
            var SystemInfo = {
                "SystemVersion": "",
                "CloudPlatformType": "",
                "IsDebug": false,
                "IsCrossBorderSite": false
            };
            commonModule.Ajax({
                url: '/api/user/GetSystemInfo',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return SystemInfo;
                    }
                    SystemInfo = rsp.Data;
                }
            });
            return SystemInfo;
        },
        GetOrderDistributionConfig () {
            var data = {
                "FendanLoginUrl": "",
                "DefaultFenFaSystemUrl": "",
                "AlibabaFenFaSystemUrl": "",
                "TikTokFenFaSystemUrl": "",
            }
            commonModule.Ajax({
                url: '/api/CustomerConfig/GetOrderDistributionConfig',
                async: false,  //false 为同步取数据
                type: 'Get',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return data;
                    }
                    data = rsp.Data;
                }
            });
            return data;
        },
    };

})(window)