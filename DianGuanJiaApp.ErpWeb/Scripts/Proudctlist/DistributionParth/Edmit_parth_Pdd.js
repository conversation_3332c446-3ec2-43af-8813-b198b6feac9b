var PrepareDistributionModule = (function (module, $, layer) {
	var edmitTemplateObj = [];//运费模板数据
	var SkuModeType = 0;   //录入规则 ：0平台规则录入  1 自定规则录入
	var IsNewSpuCode = null;
	var IsNewSkuCode = null;
	var OldSpuCode = "";
	var OldSkuCode = "";
	var edmitBaseProductSkuUid = '';
	var DeleteObjectIdStr = "";//编辑删除主图图片
	var ImageObjectId = "";//编辑图片id
	var readDetailsPicsNum = 0;
	var OldproductOuterId = "";
	var changPicMun = 1;
	var EdmitRewValueSorts = [];
	var lsReSortlmage = false;
	var IsSyncPtProductInfo = true;//是否同步到平台资料
	var BatchShopCount = 0;
	var IsSyncPtProductInfoButton = true;

	var productDetailsPics = []; //详情图片
	var setProductDetailsPics = []; //提交详情图片
	var productImages = []//商品主图
	var IsNewImage = [];
	var ImageId = [];//编辑商品传给后台，旧图片用id 新添加图片用0
	var setProductImages = []; //提交商品主图
	var productFullName = ''; //商品标题
	var productOuterId = ''; //商品编码
	var setSkus = [];
	var rawResultSkus = [];//编辑 原有sku数据;
	var isRawResultSkus = false;//是否展示 原有sku数据
	var rewAttributeTypes = []; //编辑时原始的规格类型
	var isConcatOld = true;  //是否合并老数据
	var businessType = 'BaseProduct';
	var resultSkus = [];//获取编辑商品skus
	var resultDelSkus = [];//获取编辑被删除的商品skus
	var OldSkuUidStr = '';
	var IsReSortSku = false;//是否改变规格值排序
	var CatePathList = [];//选择后商品类目
	var CateProps = [];//商品类目属性
	var IsPublic = 0;


	//以下铺货设置
	var UniqueCode = '';
	var DirectUploadType = 1;//完成铺货的商品状态 0上加架  1放入仓库 2放入草稿箱
	var PromiseDeliveryTime = 48;//发货时间
	var UserListingSetting = {};
	var BaseProductUid = "";
	var GetCreateFrom = "";
	var GetFromFxUserId = "";
	var FromBaseProductUid = "";
	var FromSupplierProductUid = "";
	var GetSpuCode = "";
	var GetCategoryId = "";
	var IsPublic = false;
	var RootNodeFxUserId = "";
	var SharePathCode = "";
	var PathNodeDeep = "";
	var GetFxUserId = "";
	var GetFromType = "";
	var GetFromCode = "";
	var IsWaitSyncBaseProduct = false;
	var IsSkuChange = false;
	var getAttributeTypes = null;
	var getCategoryInfoList = null;
	var ListingConfig = null;
	var LastCategoryId = '';
	var InitCategoryId = '';

	var PtProductDatas = null;  //商品详情model（提交入参）
	//var ListingShopIds = [];//接收重新铺货传过来id
	var SourceSku = [];  //来源sku


	module.EdmitEventInit = function () {//进入编辑页面触发函数
		var productDetailsPicsOptions = {};
		productDetailsPicsOptions.isMultiple = true;
		productDetailsPicsOptions.callBack = module.getProductDetailsPic;
		productDetailsPicsOptions.ele = "#productPics";
		productDetailsPicsOptions.businessType = businessType;
		productDetailsPicsOptions.maxSize = 5000000;
		productDetailsPicsOptions.typeFlag = 2;
		var upProductPicsOptionsMenu = new upPicFileModule();
		upProductPicsOptionsMenu.initData(productDetailsPicsOptions);
		initCommonSelectEvent();
		GetShippingFeeTemplate();//加载自定义运费模板
		showRemindTarData();//渲染填写提示
	}

	function renderEdmitFreightTemplate() {  //渲染下拉框
		var html = '';
		var html02 = '';
		edmitTemplateObj.forEach(function (item, i) {
			if (i != edmitTemplateObj.length - 1) {
				html += '<li class="n-mySelect-showContent-ul-li" data-uniquecode="' + item.UniqueCode + '" onclick=\'selectSetValTemplate.bind(this)("' + item.UniqueCode + '","' + item.IsSelect + '","' + i + '")\'>' + item.TemplateGroupName + '</li> ';

			}
		});

		BatchEdmitTemplateObj.forEach(function (item, i) {
			if (i != BatchEdmitTemplateObj.length - 1) {
				html02 += '<li class="n-mySelect-showContent-ul-li" data-uniquecode="' + item.UniqueCode + '" onclick=\'BatchSelectSetValTemplate.bind(this)("' + item.UniqueCode + '","' + item.IsSelect + '","' + i + '")\'>' + item.TemplateGroupName + '</li> ';
			}
		});

		$("#freightTemplate_ul").html(html);
		$("#Batch_freightTemplate_ul").html(html02);
	}
	function renderEdmitTemplateHtml() {

		var tplt = $.templates("#edmitShippingTemplates_templates");
		edmitTemplateObj.forEach(function (item, i) {
			item.IsActive = false;
			if (i == 0) {
				item.IsActive = true;
			}
		})
		var html = tplt.render({
			edmitTemplateObj: edmitTemplateObj,
		});
		$("#edmitShippingTemplatesWrap").html(html);
		initCommonSelectEvent();
	}

	//获取模板
	getTemplates = function (callBack) {

		var ShopsIds = [];
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				ShopsIds.push(item.ShopId);
			}
		})

		var options = {};
		options.PlatformType = setPlatformTypeName;
		options.ShopIds = ShopsIds;

		commonModule.Ajax({
			url: '/api/Listing/ShopAllDeliveryTemplate',
			type: "POST",
			loading: true,
			contentType: 'application/json',
			data: JSON.stringify(options),
			success: function (rsp) {

				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					var Data = rsp.Data || [];

					ShopsData.forEach(function (item) {
						Data.forEach(function (cItem) {
							if (item.ShopId == cItem.ShopId) {
								item.AllExpressTemplates = cItem.AllExpressTemplates || [];
							}
						})
					})
					commonModule.Ajax({
						url: '/api/ListingTemplateGroup/Get',
						data: { platformType: setPlatformTypeName },
						async: true,
						loading: true,
						type: 'GET',
						success: function (rsp) {
							if (rsp.Success == false) {
								return;
							}
							edmitTemplateObj = rsp.Data || [];
							BatchEdmitTemplateObj = rsp.Data || [];

							var creatObj = {
								FxUserId: '-3',
								UniqueCode: "",
								PlatformType: setPlatformTypeName,
								TemplateGroupName: "新增模板类型",
								SaveLisTempGroupItemModel: [],
								IsSelect: false,
								IsActive: false,
								Type: 'creat',
								ShopDeliveryTemplates: [],

							}
							edmitTemplateObj.push(creatObj);
							BatchEdmitTemplateObj.push(creatObj);

							pushEdmitTemplateObj(edmitTemplateObj);
							pushEdmitTemplateObj(BatchEdmitTemplateObj);


							function pushEdmitTemplateObj(edmitTemplateObj) {

								var newShopsData = JSON.parse(JSON.stringify(ShopsData));
								ChooseShopsData = newShopsData.filter(function (item) { return item.IsCheck });
								ChooseShopId = ChooseShopsData.map(function (item) { return item.ShopId });

								edmitTemplateObj.forEach(function (item, i) {
									item.SaveLisTempGroupItemModel = JSON.parse(JSON.stringify(ChooseShopsData));
								});
								edmitTemplateObj.forEach(function (item, i) {
									item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {
										item.ShopDeliveryTemplates.forEach(function (ccItem, ccI) {
											if (cItem.ShopId == ccItem.ShopId) {
												cItem.PlatformTemplateName = ccItem.PlatformTemplateName;
												cItem.PlatformTemplateId = ccItem.PlatformTemplateId;
												cItem.ShopDeliveryTemplateId = ccItem.Id;
											}
										})

									});
								});
								edmitTemplateObj.forEach(function (item, i) {
									item.IsSelect = true;
									item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {
										if (!cItem.PlatformTemplateId) {
											item.IsSelect = false;
										}
									});
								});

							}

							renderEdmitFreightTemplate();//渲染运费模板下拉框
							setUserListingSetting(UserListingSetting);//渲染铺货设置
							if (typeof callBack == "function") {
								callBack();
							}
						}
					});

				}
			}
		});

	}

	showEdmitFreightTemplate = function () {

		renderEdmitTemplateHtml();

		layer.open({
			type: 1,
			title: '编辑运费模板', //不显示标题
			content: $("#edmitShippingTemplatesWrap"),
			area: '560px', //宽高
			skin: 'n-skin edmitShippingTemplatesSkin',
			success: function () {
				try {
					warnFreightTemplate(0);
					eventTemplatesNav();
				} catch (err) { }
			},
			btn: ['取消', '保存'],
			btn1: function (index) {

				edmitTemplateObj.forEach(function (item, i) {
					if (i == 0) {
						item.IsActive = true;
					} else {
						item.IsActive = false;
					}
				});

				layer.close(index);

			},
			btn2: function () {
				if ($(".edmitShippingTemplatesSkin .layui-layer-btn1").hasClass("stop")) {
					return false;
				}
				var Id = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-id");
				var index = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-index");
				var val = $("#templateName_" + index).val().trim();
				var oldName = $("#templateName").attr("data-oldName");
				var isHas = false;
				var UniqueCode = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-uniquecode");

				edmitTemplateObj.forEach(function (item, i) {
					if (val == item.Name && val != oldName) {
						isHas = true;
					}
				});

				if (isHas) {
					commonModule.w_alert({ type: 2, content: '模板类型名称已存在' });
					return false;
				}

				var setSaveLisTempGroupItemModel = [];

				//console.log("edmitTemplateObjedmitTemplateObj", edmitTemplateObj)

				edmitTemplateObj.forEach(function (item, i) {
					if (i == index) {
						item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {
							var obj = {
								"Id": cItem.ShopDeliveryTemplateId ? cItem.ShopDeliveryTemplateId : 0,
								"ShopId": cItem.ShopId,
								"GroupUniqueCode": UniqueCode ? UniqueCode : '',
								"PlatformTemplateId": cItem.PlatformTemplateId,
								"PlatformTemplateName": cItem.PlatformTemplateName,
								"PlatformType": setPlatformTypeName
							}
							setSaveLisTempGroupItemModel.push(obj);
						})
					}
				})

				var SaveLisTempGroupModel = {
					"FxUserId": 0,
					"UniqueCode": UniqueCode ? UniqueCode : '',
					"PlatformType": setPlatformTypeName,
					"TemplateGroupName": val,
					"SaveLisTempGroupItemModel": setSaveLisTempGroupItemModel
				}

				commonModule.Ajax({
					url: '/api/ListingTemplateGroup/Save',
					type: "POST",
					loading: true,
					contentType: 'application/json',
					data: JSON.stringify(SaveLisTempGroupModel),
					success: function (rsp) {

						if (commonModule.IsError(rsp)) {
							return;
						}
						if (rsp.Success) {
							getTemplates();
							renderEdmitFreightTemplate();
							commonModule.w_alert({ type: 1, content: '保存成功！' });
							return false;
						}
					}
				});


			}
		});
	}


	showBatchEdmitFreightTemplate = function () {
		renderEdmitTemplateHtml();

		layer.open({
			type: 1,
			title: '编辑运费模板', //不显示标题
			content: $("#edmitShippingTemplatesWrap"),
			area: '560px', //宽高
			skin: 'n-skin edmitShippingTemplatesSkin',
			success: function () {
				try {
					warnFreightTemplate(0);
					eventTemplatesNav();
				} catch (err) {

				}
			},
			btn: ['取消', '保存'],
			btn1: function (index) {

				BatchEdmitTemplateObj.forEach(function (item, i) {
					if (i == 0) {
						item.IsActive = true;
					} else {
						item.IsActive = false;
					}
				});
				layer.close(index);

			},
			btn2: function () {
				if ($(".edmitShippingTemplatesSkin .layui-layer-btn1").hasClass("stop")) {
					return false;
				}
				var Id = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-id");
				var index = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-index");
				var val = $("#templateName_" + index).val().trim();
				var oldName = $("#templateName").attr("data-oldName");
				var isHas = false;
				var UniqueCode = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-uniquecode");

				edmitTemplateObj.forEach(function (item, i) {
					if (val == item.Name && val != oldName) {
						isHas = true;
					}
				});

				if (isHas) {
					commonModule.w_alert({ type: 2, content: '模板类型名称已存在' });
					return false;
				}


				var setSaveLisTempGroupItemModel = [];

				BatchEdmitTemplateObj.forEach(function (item, i) {
					if (i == index) {
						item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {
							var obj = {
								"Id": cItem.ShopDeliveryTemplateId ? cItem.ShopDeliveryTemplateId : 0,
								"ShopId": cItem.ShopId,
								"GroupUniqueCode": UniqueCode ? UniqueCode : '',
								"PlatformTemplateId": cItem.PlatformTemplateId,
								"PlatformTemplateName": cItem.PlatformTemplateName,
								"PlatformType": setPlatformTypeName
							}
							setSaveLisTempGroupItemModel.push(obj);
						})
					}
				})

				var SaveLisTempGroupModel = {
					"FxUserId": 0,
					"UniqueCode": UniqueCode ? UniqueCode : '',
					"PlatformType": setPlatformTypeName,
					"TemplateGroupName": val,
					"SaveLisTempGroupItemModel": setSaveLisTempGroupItemModel
				}

				commonModule.Ajax({
					url: '/api/ListingTemplateGroup/Save',
					type: "POST",
					loading: true,
					contentType: 'application/json',
					data: JSON.stringify(SaveLisTempGroupModel),
					success: function (rsp) {

						if (commonModule.IsError(rsp)) {
							return;
						}
						if (rsp.Success) {
							getTemplates();
							renderEdmitFreightTemplate();
							commonModule.w_alert({ type: 1, content: '保存成功！' });
							return false;
						}
					}
				});
			}
		});
	}


	eventTemplatesNav = function (index) {

		$$.navActive("#edmitShippingTemplates_nav", function (index, item) {
			$(".edmitShippingTemplates-ul").hide();
			$("#edmitShippingTemplates_" + index).show();
			$(".n-mySelect").removeClass("active");

			edmitTemplateObj.forEach(function (item, i) {
				item.IsActive = false;

				if (index == i) {
					item.IsActive = true;
					warnFreightTemplate(index);
				}
			});
		});

		if (index) {
			$("#edmitShippingTemplates_nav .n-tabNav-item").each(function (i, item) {
				if (i == index) {
					$(item).trigger("click");
				}
			})
		}
	}

	//api/ListingTemplateGroup/RefreshDeliveryTemplate 铺货 - 编辑平台资料 - 新增运费模板 刷新店铺运费模板
	refreshDeliveryTemplateFun = function (ShopId) {
		var index = $("#edmitShippingTemplates_nav .n-tabNav-item.active").attr("data-index");
		var that = this;
		if ($(that).hasClass("stop")) {
			return;
		}
		var options = {};
		options.platformType = setPlatformTypeName;
		options.shopId = ShopId;
		$(that).addClass("stop");
		commonModule.Ajax({
			url: '/api/ListingTemplateGroup/GetShopDeliveryTemplate',
			type: "GET",
			loading: true,
			contentType: 'application/json',
			data: options,
			success: function (rsp) {
				$(that).removeClass("stop");
				if (!rsp.Success) {
					commonModule.w_alert({ type: 2, content: rsp.Message });
					return;
				}
				if (rsp.Success) {
					var TemplateData = rsp.Data;
					TemplateData.forEach(function () {


					});

					edmitTemplateObj.forEach(function (item, i) {
						item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {
							if (cItem.ShopId == ShopId) {
								concatTemplateData(cItem.AllExpressTemplates, TemplateData)
							}
						})
					})

					BatchEdmitTemplateObj.forEach(function (item, i) {
						item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {
							if (cItem.ShopId == ShopId) {
								concatTemplateData(cItem.AllExpressTemplates, TemplateData)
							}
						})
					})

					renderEdmitTemplateHtml();
					eventTemplatesNav(index);
					commonModule.w_alert({ type: 4, content: '已更新成功！' });


					function concatTemplateData(AllExpressTemplates, TemplateData) {
						TemplateData.forEach(function (item, i) {
							var isHas = false;
							AllExpressTemplates.forEach(function (cItem, cI) {
								if (item.Id == cItem.Id) {
									isHas = true;
								}
							})
							if (!isHas) {
								AllExpressTemplates.push(item);
							}
						})
					}


				}
			}
		});

	}

	function warnFreightTemplate(i) {


		//return;
		var isActive = true;
		var ele = "#templateName_" + i;
		var val = $(ele).val().trim();

		if (!val) {
			isActive = false;
		}
		$("#tbody_sipping_data_" + i + " tr .n-mySelect").each(function (index, item) {
			if (!$(item).hasClass("hasActive")) {
				isActive = false;
			}
		})

		if (!isActive) {
			$(".edmitShippingTemplatesSkin .layui-layer-btn1").addClass("stop");
		} else {
			$(".edmitShippingTemplatesSkin .layui-layer-btn1").removeClass("stop");
		}

	}


	selectTemplate = function (Id, Name, ShopId, pIndex) {
		var text = $(this).html();
		$(this).closest(".n-mySelect").find('.n-mySelect-showContent-ul-li').removeClass('activeItem');
		$(this).closest(".n-mySelect").addClass("hasActive").find('.n-mySelect-title-chooseItem').html(text);
		$(this).closest(".catePropsItemWrap").removeClass('formWarn');
		$(this).closest(".n-myCommonSelect").removeClass('addWarnInput');


		edmitTemplateObj.forEach(function (item, i) {
			if (pIndex == i) {
				item.SaveLisTempGroupItemModel.forEach(function (cItem, cI) {

					if (cItem.Id == ShopId) {
						cItem.PlatformTemplateId = Id;
						cItem.PlatformTemplateName = Name;
						cItem.PlatformType = setPlatformTypeName;
					}

				})
			}

		})
		warnFreightTemplate(pIndex);
	}

	changeTemplateInput = function (pIndex) {
		warnFreightTemplate(pIndex);

	}

	selectSetValTemplate = function (UniqueCode, isTrue, index) {

		if (isTrue == "false") {
			showEdmitFreightTemplate();
			eventTemplatesNav(index)

		} else {
			var selectObj = {};
			edmitTemplateObj.forEach(function (item) {
				if (item.UniqueCode == UniqueCode) {
					selectObj = item;
				}
			})
			$("#template_select").addClass("hasActive");
			$("#template_select").find(".n-mySelect-title-chooseItem").text(selectObj.TemplateGroupName);
			$(this).closest(".catePropsItemWrap").removeClass("formWarn");


			var ListingSettingValue = null;

			if (batchEdmitStepNum == 3) {
				ListingSettingValue = UserListingSetting.BatchSettingValue;
			} else {
				ListingSettingValue = UserListingSetting.ListingSettingValue;
			}

			ListingSettingValue.HonourAgreement.ExpressTemplateGroupCode = UniqueCode;

			//$("#freightTemplate_ul .n-mySelect-showContent-ul-li").each(function (index, item) {
			//	var uniquecode = $(item).attr('data-uniquecode');
			//	if (uniquecode == UniqueCode) {
			//		$(item).addClass('activeItem');
			//	}

			//})

		}

		showRemindTarData();//渲染填写提示


	}



	//编辑类目开始-------------------------------------------------------------------------------------------------------------------------------


	var remindTarData = [//-1 未知填写数量
		{ Title: '商品类目', mustNum: 3, changeMustNum: 3, needNum: 2, changeNeedNum: 2, IsActive: true, Status: 'must', tarEle: 'category' },
		{ Title: '商品信息', mustNum: 2, changeMustNum: 2, needNum: 1, changeNeedNum: 1, IsActive: false, Status: 'must', tarEle: 'productInfo' },
		{ Title: '商品规格', mustNum: -1, changeMustNum: -1, needNum: -1, changeNeedNum: -1, IsActive: false, Status: 'must', tarEle: 'productSku' },
		{ Title: '铺货设置', mustNum: 6, changeMustNum: 6, needNum: 0, changeNeedNum: 0, IsActive: false, Status: 'must', tarEle: 'setPrepareDistribution' },

	];

	//调后台数据
	function getCateProps() {



		var options = {};
		LastCategoryId = CatePathList[CatePathList.length - 1].CateId;
		options.CategoryId = LastCategoryId;

		options.PlatformType = setPlatformTypeName;  //到时要换成抖音的
		//options.ProductUid = edmitBaseproductuid;    //基础资料商品Id 
		options.SourceSku = SourceSku;

		PtProductDatas.ProductInfoSkus.forEach(function (item) {
			if (Array.isArray(item.Attributes)) {
				item.Attributes = JSON.stringify(item.Attributes);
			}
		})

		var SourcePropJson = Array.isArray(PtProductDatas.CategoryAttribute) ? JSON.stringify(PtProductDatas.CategoryAttribute) : PtProductDatas.CategoryAttribute;
		if (SourcePropJson) {
			SourcePropJson = JSON.parse(SourcePropJson);
			ExtCategoryAttributes(SourcePropJson);
		}
		options.SourcePropJson = Array.isArray(SourcePropJson) ? JSON.stringify(SourcePropJson) : SourcePropJson;



		options.DbFlag = "fff";   //supplierproduct=货盘，business=普通业务库，其他值=基础商品库
		options.SkuModeType = SkuModeType;

		commonModule.Ajax({
			url: '/api/PtProductInfo/TranAttributeAndSku',
			type: "POST",
			loading: true,
			contentType: 'application/json',
			data: JSON.stringify(options),
			success: function (rsp) {

				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					CateProps = rsp.Data.CategoryAttribute;
					if (CateProps) {
						CateProps = JSON.parse(CateProps);
					}
					var SellPropertyJson = rsp.Data.SellPropertyJson ? JSON.parse(rsp.Data.SellPropertyJson) : [];
					rsp.Data.SellPropertyJson = SellPropertyJson

					PtProductDatas.CategoryId = options.CategoryId;
					PtProductDatas = Object.assign(PtProductDatas, rsp.Data);

					// sku规格属性模板转换
					onSellPropertyJsonFormat()

					var newSellPropertyJson = [];
					PtProductDatas.AttributeTypes.forEach(function (jData, j) {
						if (jData.SellPropertyJson) {
							jData.SellPropertyJson = JSON.parse(jData.SellPropertyJson);
						}


						PtProductDatas.SellPropertyJson.forEach(function (item, i) {
							if (item.sell_property_id == jData.SellPropertyId) {
								newSellPropertyJson.push(item)
								jData.AttributeValues.forEach(function (kData, k) {

									var obj = {
										support_remark: item.support_remark,
										support_diy: item.support_diy,
										sell_property_id: item.sell_property_id || "", //销售属性id 
										sell_property_name: item.sell_property_name || "", //销售属性名称 
										sell_property_value_id: '', //值对应的id ,
										sell_property_value_name: '', //值的名称 ,
										values: kData.Value || '', //完整的值 ,
										remark: jData.remark, //备注的值 ,,
										value_modules: [],
										special_values: item.special_values || [],
									}
									if (kData.SellPropertyValues && kData.SellPropertyValues.length) {
										obj.sell_property_value_id = kData.SellPropertyValues[0].sell_property_value_id; //值对应的id ,
										obj.sell_property_value_name = kData.SellPropertyValues[0].sell_property_value_name;
										obj.value_modules = kData.SellPropertyValues[0].value_modules;
										obj.remark = kData.SellPropertyValues[0].remark || obj.remark;
										obj.template_id = kData.SellPropertyValues[0].ext1 || '';
									}
									kData.SellPropertyValues = obj
								})
							}

						})

					})
					PtProductDatas.SellPropertyJson = newSellPropertyJson.length ? newSellPropertyJson : PtProductDatas.SellPropertyJson;

					// sku规格属性值 - 分类导航数据
					var NavCateValues = rsp.Data.NavCateValues;
					if (NavCateValues && NavCateValues.length) {
						NavCateValues.forEach(function (item, index) {
							if (item.ChildNodes && item.ChildNodes.length) {
								item.ChildNodes.forEach(function (item2, index2) {
									item2.LowerLevelAttrData = JSON.parse(item2.LowerLevelAttrData)
								})
							}
						})
					}
					isLoadingSetSkuAttributes = false
					//console.log("PtProductDatas===", PtProductDatas)
					setBaseProductInfo(PtProductDatas, 2);
					//CateProps = CateProps.filter(function (item) { return item.Rule.IsRequired });  //目前只支持必填属性
					initCateProps(CateProps);//渲染数据
					//getBrandOptions();
					showRemindTarData();//渲染填写提示
					checkEdmitOpionsWarn();
				}
			}
		});
	}
	// 规格度量衡转换抖音模板
	function onSellPropertyJsonFormat() {
		var newSellPropertyJsons = [];
		
		if (PtProductDatas.SellPropertyJson) {
			// CateProp有数据，则说明有规格类型能选择，规格值不用校验
			if (PtProductDatas.SellPropertyJson.CateProp && PtProductDatas.SellPropertyJson.CateProp.length > 0) {
				PtProductDatas.SellPropertyCateProp = PtProductDatas.SellPropertyJson.CateProp;
			} else{
				// 销售属性值
				var SalePropRule = PtProductDatas.SellPropertyJson.SalePropRule ? JSON.parse(PtProductDatas.SellPropertyJson.SalePropRule) : [];
				//console.log("原数据----SalePropRule",SalePropRule)
				if (SalePropRule.length > 0) {
					
					SalePropRule.forEach(function (item, i) {
						var property_values = [];  // sku属性下拉数据
						var measure_templates = [];  // 度量衡模板
						item.value_display_style = "text";  // sku模板默认输入框
						/* required_rule_type枚举值的说明
							0=文本; 还有可能是单选框（是否为有机食品）、输入框（保质期）：比如：酱油
							1=数值，如“100”; 
							2=数值范围，如“10,20”，表示10到20之间;
							3=数值乘积-二维，如“10,10”，表示10*10;
							4=数值乘积-三维，如“10,10,10”，表示10*10*10; 
							5=单项时间选择-年月日，如“2020-05-20”;
							6=双项时间选择-年月日，如“2020-05-20,2020-06-20”;
							7=单项时间选择-年月，如“2020-05”;
							8=双项时间选择-年月，如“2020-05,2020-06”。
						*/
						switch (item.required_rule_type)
						{
							case 0:
								if (item.values && item.values.length > 0){ //下拉框
									item.value_display_style = "cascader_multi_select";
									if (item.name.indexOf('尺') > -1 || item.name == '参考分类') {
										// 特殊分类新选模板数据
										var special_values = onListFomat(item.values);
										item.special_values = special_values
									}
									// 下拉数据处理
									item.values.forEach(function (itemOptions, iOptions) {
										property_values.push({
											sell_property_value_id : itemOptions.vid,
											sell_property_value_name : itemOptions.value,
											spec_id: itemOptions.spec_id,
											pic_value : item.extend_info || '',
											parent_vids : itemOptions.parent_vids
										})
									})
									
								}else if (item.value_unit && item.value_unit.length > 0) { // 度量衡（输入框+下拉）
									item.value_display_style = "measure"
									var value_modules = [];
									var units = [];  // 度量衡 - 右侧单位下拉数据
									// 度量衡 - 右侧单位数据处理
									item.value_unit.forEach(function (itemUnit, iUnit) {
										units.push({
											unit_id: item.ref_pid+iUnit,
											unit_name: itemUnit,
										})
									})
									var unitObj = {
										module_id : item.ref_pid,
										prefix : "",
										suffix : "", // 后面的符号
										input_type : "input",
										units: units,
										validate_rule:{
											min : 1,
											max : item.input_max_num,
										}
									}
									value_modules.push(unitObj)
									var templates = {
										template_id : item.parent_spec_id,
										display_name : item.name,
										value_modules : value_modules
									}
									measure_templates.push(templates)
								
								} else { // 如果只是输入框的话，还有最大值，最小值那些吗
									item.value_display_style = "text"
								}
								break;
							case 1:
							case 2:
								// 输入框（只能输入数字）
								item.value_display_style = "text"
								break;
							case 3:
							case 4: // 数数值乘积-二维。数值乘积 - 三维 暂时不确定
								break;
							case 5:
								// dateTime（单项时间选择-年月日）
								item.value_display_style = "dateTime"
								break;
							case 7:
								// dateTime（双项时间选择-年月日）
								item.value_display_style = "dateTimeRange"
								break;
							case 6:
								// dateTimeRange（单项时间选择-年月）
								item.value_display_style = "dateTime"
								break;
							case 8:
								// dateTimeRange（双项时间选择-年月）
								item.value_display_style = "dateTimeRange"
								break;
						}
						var sellPropertyJsoObj = {
							sell_property_name : item.name,
							sell_property_id : item.ref_pid,
							support_remark : item.can_note, // 是否支持备注
							support_diy : item.input_max_num == 0 ? false : true, // 是否支持自定义,为0时代表不可自定义。
							is_required : true,
							value_display_style : item.value_display_style,
							need_paging_query_value : false,
							property_default_display : true,
							property_values: property_values,
							measure_templates: measure_templates,
							navigation_properties: [],
							special_values: item.special_values || [], //特殊分类模板
						}
						newSellPropertyJsons.push(sellPropertyJsoObj)
					})
				}
			}
		}
		PtProductDatas.SellPropertyJson = newSellPropertyJsons;
	}
	// 字段相同的数据排序
	function onListFomat (list) {
		var arr = [];
		for (var index = 0; index < list.length; index++) {
			var item = list[index];
			var obj = {
				sell_property_value_id : item.vid,
				sell_property_value_name : item.value,
				spec_id: item.spec_id,
				pic_value : item.extend_info || '',
				parent_vids : item.parent_vids
			}
			var filterArr = arr.filter(function (arrItem, i) {
				return arrItem.name === item.group.name
			})
			if (filterArr.length) {
				for(var i = 0; i< arr.length; i++){
					if(arr[i].name === item.group.name){ 
						arr[i].children.push(obj)
					}
				}
			} else {
				arr.push({
					name: item.group.name,
					id: item.group.id,
					isCheck: index == 0 ? true : false,
					children:[obj],
				});
			}
		}
		return arr
	}

	//获取品牌下拉值
	function getBrandOptions() {

		var isHasBrand = false;
		CateProps.forEach(function (item) {
			if (item.FieldType == "brandSearch") {
				isHasBrand = true;
			}
		})

		if (isHasBrand) {
			var CheckShopsDataIds = [];
			var ShopId = 0;
			ShopsData.forEach(function (item) {
				if (item.IsCheck) {
					CheckShopsDataIds.push(item.ShopId);
				}
			})
			if (CheckShopsDataIds.length == 1) {
				ShopId = CheckShopsDataIds[0];
			}

			var options = {};
			options.ShopId = ShopId;
			options.CateId = LastCategoryId;
			options.PlatformType = setPlatformTypeName;

			var GetBrandData = [];
			var noObj = { Key: '无品牌', Value: '无品牌' }
			GetBrandData.unshift(noObj);

			commonModule.Ajax({
				url: '/api/Listing/GetBrand',
				type: "POST",
				loading: true,
				contentType: 'application/json',
				data: JSON.stringify(options),
				success: function (rsp) {
					if (commonModule.IsError(rsp)) {
						var html = "";
						GetBrandData.forEach(function (item) {
							html += '<li class="n-mySelect-showContent-ul-li " onclick=\'selectCateProps.bind(this)("品牌")\'>' + item.Value + '</li>';
						})
						$(".brandSearch .n-mySelect-showContent-ul").html(html);
						return;
					}
					if (rsp.Success) {
						GetBrandData = rsp.Data || [];
						var noObj = { Key: '无品牌', Value: '无品牌' }
						GetBrandData.unshift(noObj);

						var html = "";
						GetBrandData.forEach(function (item) {
							html += '<li class="n-mySelect-showContent-ul-li " onclick=\'selectCateProps.bind(this)("品牌")\'>' + item.Value + '</li>';
						})
						$(".brandSearch .n-mySelect-showContent-ul").html(html);

					}
				}
			});
		}

	}



	function initCateProps() { //初始代类目属性

		CateProps.forEach(function (item) {
			if (!item.hasOwnProperty('Rule')) {  //判断有没有限定属性，没有话赋值一个非必填属性
				item.Rule = {};
				item.Rule.IsRequired = false;
			}
			if (item.FieldType == "checkbox" || item.FieldType == "checkboxlist" || item.FieldType == "brandSearch") {
				item.Options = [];
				if (Array.isArray(item.OptionList)) {
					item.OptionList.forEach(function (citem) {
						item.Options.push(citem.Value);
					})
				}
			}
		})

		CateProps = CateProps.filter(function (item) {
			return item.ShowCondition == 0 || !item.ShowCondition;
		})


		if (CateProps.length > 0) {
			$("#allCatePropsWrap").show();
		} else {
			$("#allCatePropsWrap").hide();
		}

		moveToLast(CateProps, "面料材质", function () { }); //面料材质放在最后
		CateProps.forEach(function (item) {
			if (item.FieldType == "multiValueMeasure") {
				$("#addCatePropsBtns").show();
			}
			if (item.FieldType == "brandSearch" && UrlFromType == 2) {
				item.Value = '无品牌';
			}
			if (item.FieldType == "brandSearch" && UrlFromType != 2) {
				item.Options = item.Options || [];
				if (item.Value == "" || item.Value == null) {
					item.Value = "无品牌";
				}
				if (item.Options.length == 0) {
					item.Options.push("无品牌");
				} else {
					var isHasBrand = false;
					item.Options.forEach(function (item) {
						if (item == "无品牌") {
							isHasBrand = true;
						}
					});
					if (!isHasBrand) {
						item.Options.unshift("无品牌");

					}
				}
			}
		})

		var tplt = $.templates("#pdd_data_list_cateProps");
		$("#catePropsTotal").text(CateProps.length);

		if (CateProps.length > 0) {
			$("#catePropsWrap").show();
		} else {
			$("#catePropsWrap").hide();
			return;
		}

		var CatePropsIsRequired = CateProps.filter(function (item) { return item.Rule.IsRequired || item.Rule.IsImportant });
		var CatePropsIsNoRequired = CateProps.filter(function (item) { return !item.Rule.IsRequired && !item.Rule.IsImportant });

		var html = tplt.render({
			CateProps: CatePropsIsRequired,
		});

		var html02 = tplt.render({
			CateProps: CatePropsIsNoRequired,
		});

		$("#catePropsWrap").html(html);
		$("#catePropsWrap02").html(html02);

		if (CatePropsIsNoRequired.length > 3) {
			$("#showMoreAddCatePropsBtnsWrap").show();
		} else {
			$("#showMoreAddCatePropsBtnsWrap").hide();
		}

		var catePropsTotalNum = 0;
		CatePropsIsRequired.forEach(function (item) {
			if (item.FieldType != "multiValueMeasure") {
				catePropsTotalNum++;
			} else {
				catePropsTotalNum = catePropsTotalNum + item.MeasureTemplates.MultiValueMeasureList.length;
			}

		})

		$("#catePropsTotal").text(catePropsTotalNum);
		$("#catePropsTotal02").text(CatePropsIsNoRequired.length);

		if (CatePropsIsRequired.length == 0) {
			$("#requiredCateProps").hide();
		} else {
			$("#requiredCateProps").show();
		}
		if (CatePropsIsNoRequired.length == 0) {
			$("#otherCateProps").hide();
		} else {
			$("#otherCateProps").show();
		}

		CateProps.forEach(function (item, i) {
			if (item.FieldType == "dateTimeRange") {
				var dateValue = '';
				if (item.MeasureTemplates.LeftValue && item.MeasureTemplates.RightValue) {
					dateValue = item.MeasureTemplates.LeftValue + " - " + item.MeasureTemplates.RightValue;
					$("#dateTimeRange").closest(".n-mySelect").addClass("hasActive");
				}
				var dateTimeRangeOptions = {
					elem: '#dateTimeRange_' + item.Id,
					type: 'date',
					range: true,
					format: 'yyyy-MM-dd',
					skin: 'dateTimeRange',
					value: dateValue,
					done: function (value, date, endDate) {
						if (value) {
							if (item.DateTimeType == 2) {
								item.MeasureTemplates.LeftValue = value.substr(0, 7);
								item.MeasureTemplates.RightValue = value.substr(10, 17);
							} else {
								item.MeasureTemplates.LeftValue = value.substr(0, 10);
								item.MeasureTemplates.RightValue = value.substr(13, 23);

							}
							item.Value = value;
							$("#dateTimeRange_" + item.Id).closest(".n-mySelect").addClass("hasActive");
							$('#dateTimeRange_' + item.Id).closest(".catePropsItemWrap").removeClass("formWarn");
						}
						showRemindTarData();//渲染填写提示
					}
				}
				if (item.DateTimeType == 2) {
					dateTimeRangeOptions.type = "month";
				}
				layui.laydate.render(dateTimeRangeOptions);
			}

			if (item.FieldType == "dateTime") {
				var dateTimeValue = '';
				if (item.Value) {
					//dateTimeValue = item.MeasureTemplates.LeftValue;
					dateTimeValue = item.Value;
					$("#cate_dateTime_" + item.Id).closest(".n-mySelect").addClass("hasActive");
				}

				var dateTimeOptions = {
					elem: '#cate_dateTime_' + item.Id,
					format: 'yyyy-MM-dd',
					value: dateTimeValue,
					done: function (value, date, endDate) {
						if (value) {
							$("#cate_dateTime_" + item.Id).closest(".n-mySelect").addClass("hasActive");
							$("#cate_dateTime_" + item.Id).closest(".catePropsItemWrap").removeClass("formWarn");
							//item.MeasureTemplates.LeftValue = value;
							item.Value = value;
						}
						showRemindTarData();//渲染填写提示
					}
				}
				if (item.DateTimeType == 2) {
					dateTimeOptions.type = "month";
				}
				layui.laydate.render(dateTimeOptions);
			}
		})






		$(".catePropsItemWrap .catePropsItem .n-mySelect-title").off();

		$(".catePropsItemWrap .catePropsItem .n-mySelect-title").on("click", function () {
			event.stopPropagation();
			$("#catePropsWrap .catePropsItem").removeClass("active");
			$("#otherCateProps .catePropsItem").removeClass("active");
			$("#setPrepareDistribution .catePropsItem").removeClass("active");

			$(this).closest(".catePropsItem").addClass("active");
			$(this).closest(".catePropsItem").find(".searchInput").val("");
			$(this).closest(".catePropsItem").find(".n-mySelect-showContent-ul-li").css({ display: 'flex' });

			if ($(this).closest(".catePropsItemWrap").hasClass("noRequired")) {
				var top = $(this).offset().top + 32;
				$(this).closest(".catePropsItemWrap").find(".n-mySelect-showContent").css({ top: top });
			}
		})
		var $this = ".catePropsItemWrap .catePropsItem";
		$($this + ' .addAttribute_addbtn').off();
		$($this + ' .closeBtn').off();
		$($this + ' .addAttribute_box .addBtn').off();
		$($this + ' .addAttribute_addbtn').css({ display: 'flex' });
		$($this + ' .addAttribute_box').css({ display: 'none' });
		// 添加规格值
		$($this + ' .addAttribute_addbtn').on('click', function () {
			$(this).hide();
			$($this + ' .addAttribute_box').css({ display: 'flex' });
		})
		$($this + ' .closeBtn').on('click', function () {
			$($this + ' .addAttribute_addbtn').css({ display: 'flex' });
			$($this + ' .addAttribute_box').css({ display: 'none' });
		})
		$($this + ' .addAttribute_box .layui-input').on("input", function () {

		})
		$($this + ' .addAttribute_box .addBtn').on('click', function () {

			var customValue = $(this).siblings('.layui-input').val();
			if (!customValue || getByteLen(customValue) > 20) {
				layer.msg('请输入1-20个字符（10个汉字）')
				return
			}
			var _name = $(this).siblings('.layui-input').data('name')
			CateProps.forEach(function (item, i) {
				if (item.Name == _name) {
					item.Options.push(customValue);
					item.Value = customValue;
					// 用户是否采用了自定义属性，采用: 1 不采用：0
					item.SetValueByDiy = 1
					initCateProps();
				}
			})
		})
		hidemultiValueMeasurLi();

	}

	function autoClickGetPtCascadeProperti(CateProps) {  //目录属性： 判断和联动下级，而且下拉已经有值的，自动获取下级数组

		CateProps.forEach(function (CatePropsItem) {
			if (CatePropsItem.hasOwnProperty('LowerIds') && Array.isArray(CatePropsItem.LowerIds)) {
				var LowerIds = CatePropsItem.LowerIds;
				var isHas = false;
				LowerIds.forEach(function (idsItem) {//过滤已经存在的联动下级
					CateProps.forEach(function (pItem) {
						if (idsItem == pItem.Id) {
							isHas = true;
						}
					})
				})
				if (!isHas) {
					if (CatePropsItem.Value) {
						if (typeof CatePropsItem.Value == "string") {
							var valueArray = [];
							valueArray[0] = CatePropsItem.Value;
							CatePropsItem.Value = valueArray;
						}
						GetPtCascadeProperti(CatePropsItem);
					}
				}
			}
		})
	}

	//隐藏下拉选项已选中项  度量衡
	function hidemultiValueMeasurLi() {
		if ($("#requiredCateProps .catePropsItemWrap.multiValueMeasure").length > 0) {
			var selectAllName = "";
			$("#requiredCateProps .catePropsItemWrap.multiValueMeasure").each(function (index, item) {
				var itemName = $(item).find(".n-mySelect-title-chooseItem").text();
				if (itemName) {
					selectAllName += itemName + '||'
				}
			});

			$("#requiredCateProps .catePropsItemWrap.multiValueMeasure").each(function (index, item) {
				var chooseName = $(item).find(".n-mySelect-title-chooseItem").text() + "||";
				$(item).find(".n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (cIndex, cItem) {
					$(cItem).removeClass("hideCateLi");
					var itemName = $(cItem).attr("data-name") + "||";
					if (selectAllName.indexOf(itemName) != -1 && chooseName != itemName) {
						$(cItem).addClass("hideCateLi");
					}
				})
			});
		}
	}

	function moveToLast(array, Name, callBack) {
		var index = 0;
		var obj = {};
		var isHas = false;
		array.forEach(function (item, i) {
			if (item.Name == Name) {
				index = i;
				obj = item;
				isHas = true;
			}
		})
		if (isHas) {
			if (index !== -1) {
				array.splice(index, 1); // 移除元素
				array.push(obj); // 添加到数组末尾
				if (typeof callBack == "function") {
					callBack();
				}
			}
		}

	}
	var isChangeCategoryData = false;

	function initCategoryMenu(categoryData) { //初始代类目
		
		var isHasMultiValueMeasure = false;
		if (CateProps) {
			CateProps.forEach(function (item) {
				if (item.FieldType == "multiValueMeasure") {
					isHasMultiValueMeasure = true;
				}
			});
		}
		
		if (isHasMultiValueMeasure) {
			$("#addCatePropsBtns").css({ display: 'flex' })
		} else {
			$("#addCatePropsBtns").css({ display: 'none' })
		}

		if (categoryData.length > 0) {
			if (categoryData[0].CateId == -1) {
				categoryData = [];
			} else {
				InitCategoryId = categoryData[categoryData.length - 1].CateId;
			}
		}

		if (OperateType == "edmit") {
			if (categoryData.length == 0) {
				$(".categoryMeuItem").addClass("formWarn");
			} else {
				$(".categoryMeuItem").removeClass("formWarn");
			}
		}


		var CheckShopsDataIds = [];
		var ShopId = 0;
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				CheckShopsDataIds.push(item.ShopId);
			}
		})
		//if (CheckShopsDataIds.length == 1) {
		//	ShopId = CheckShopsDataIds[0];
		//} 

		var PlatformType = setPlatformTypeName;
		var categoryMenu = new categoryMenuModule();
		categoryMenu.initData({
			ele: '#categoryMeu',
			defaultData: categoryData,
			getCategoryDataObj: { //点击类目接口
				url: '/api/Listing/GetCategoryList',
				data: {
					ShopId: ShopId,
					Pid: "0",
					PlatformType: PlatformType
				}
			},
			searchCategoryDataObj: {  //搜索类目接口
				url: '/api/Listing/GetCategoryByName',
				data: {
					ShopId: ShopId,
					ShopPt: PlatformType,
					ReturnCount: 50  //限制搜索出来多少条
				}
			},
			lastCallBack: function (selectData) {
				//console.log("selectData===", selectData)
				$(".categoryMeuItem").removeClass("formWarn");
				isChangeCategoryData = true;
				CatePathList = changeCateFormat(selectData);
				getCateProps();//获取类目属性
				showRemindTarData();

			}
		});

	}

	function changeCateFormat(selectData) {
		var newData = [];
		if (selectData.length > 0) {
			selectData.forEach(function (item) {
				var obj = {};
				obj.CateId = item.Id;
				obj.ParentId = item.Pid;
				obj.Level = item.Level;
				obj.Name = item.Name;
				newData.push(obj);

			})
		}
		return newData;
	}

	module.GetCategoryMenu = function () {
		initCategoryMenu([]);//初始代类目
	}

	module.addCateProps = function () {
		CateProps.forEach(function (item) {
			if (item.FieldType == "multiValueMeasure") {
				var len = item.MeasureTemplates.MultiValueMeasureList.length;
				var obj = {
					"Index": len + 1,
					"Name": "",
					"Value": ""
				}
				item.MeasureTemplates.MultiValueMeasureList.push(obj);
			}
		});
		initCateProps();
		showRemindTarData();//渲染填写提示
	}

	selectCateProps = function (name, type, index,id) {
		var that = this;
		var text = $(this).html();
		if (type != "checkboxlist" && type != "checkbox") {
			$(this).closest(".n-mySelect").find('.n-mySelect-showContent-ul-li').removeClass('activeItem');
			$(this).toggleClass('activeItem').closest(".n-mySelect").addClass("hasActive").find('.n-mySelect-title-chooseItem').html(text);
		} else {
			$(this).toggleClass('activeItem');
			event.stopPropagation();
		}
		$(this).closest(".catePropsItemWrap").removeClass('formWarn');
		if (type == "measure") {
			CateProps.forEach(function (item, i) {
				if (item.Name == name) {
					item.Value = item.MeasureTemplates.LeftValue + text;
					item.MeasureTemplates.RightValue = text;
					item.MeasureTemplates.RightOptions.forEach(function (cItem, cI) {
						cItem.IsSelect = false;
						if (cItem.Value == text) {
							cItem.IsSelect = true;
						}
					})
				}
			})
		} else if (type == "multiValueMeasure") {
			CateProps.forEach(function (item, i) {
				if (item.Name == name) {
					item.MeasureTemplates.LeftValue = text;
					item.MeasureTemplates.MultiValueMeasureList.forEach(function (cItem, cI) {
						if (cI == index) {
							cItem.Name = text;

						}
					})
				}
			})

		} else if (type == "checkboxlist" || type == "checkbox") {
			var catePropsItem = {};
			CateProps.forEach(function (item, i) {

				if (item.Id == id) {
					catePropsItem = item;
					var isMaxNum = false;

					if (item.Value == "" || item.Value == null) {
						item.Value = [];
						item.Value.push(text);
					} else {

						if (!Array.isArray(item.Value)) {
							item.Value = item.Value.split(',')
						}
						var isHas = false;
						if (Array.isArray(item.Value)) {

							item.Value.forEach(function (cItem, i) {
								if (cItem == text) {
									isHas = true;
									item.Value.splice(i, 1)
								}
							})

						}

						var maxNum = item.Rule.MaxLength - 0;
						if (maxNum && Array.isArray(item.Value)) {
							if (item.Value.length >= maxNum) {
								isMaxNum = true;
								commonModule.w_alert({ type: 2, content: '选择数量不能超过' + maxNum });
							}
						}

						if (isMaxNum) {
							$(that).removeClass('activeItem');
							return;
						}


						if (!isHas && !isMaxNum) {
							item.Value.push(text);
						}

					}

					if (item.Value.length == 0) {
						$(that).closest(".n-mySelect").removeClass("hasActive").find('.n-mySelect-title-chooseItem').html('');
					} else {

						$(that).closest(".n-mySelect").addClass("hasActive").find('.n-mySelect-title-chooseItem').html(item.Value.join(','));
					}
				}
			})
			GetPtCascadeProperti(catePropsItem);

		}
		else {
			CateProps.forEach(function (item, i) {
				if (item.Name == name) {
					item.Value = text;
				}
			})
		}
		hidemultiValueMeasurLi();
		showRemindTarData();//渲染填写提示
	}

	//获取平台目录级联属性
	function GetPtCascadeProperti(CatePropsItem) {

		if (CatePropsItem.hasOwnProperty('LowerIds') && Array.isArray(CatePropsItem.LowerIds)) {

			if (CatePropsItem.LowerIds.length > 0) {
				if (!CatePropsItem.Value || CatePropsItem.Value.length == 0) {//去除已选择联动项

					delChildrenCateProp(CatePropsItem.Id);

					function delChildrenCateProp(id) {   //删除联动下面的属性
						for (var i = 0; i < CateProps.length; i++) {
							var item = CateProps[i];
							if (item.hasOwnProperty("Pids")) {
								for (var j = item.Pids.length - 1; j >= 0; j--) {
									var cItem = item.Pids[j];
									if (id == cItem) {
										CateProps.splice(i, 1);
										delChildrenCateProp(item.Id);
									}
								}
							}
						}
					}
					initCateProps();
					return;
				}

				var SelectOptionList = [];
				CatePropsItem.Value.forEach(function (item) {
					CatePropsItem.OptionList.forEach(function (cItem) {
						if (item == cItem.Value) {
							SelectOptionList.push(cItem);
						}
					})
				})

				var options = {
					"CurrentCateId": LastCategoryId ? LastCategoryId : InitCategoryId,
					"CurrentPropId": CatePropsItem.Id,
					"SelectOptionList": SelectOptionList,
					"LowerIds": CatePropsItem.LowerIds,
					"PlatformType": setPlatformTypeName
				}
				commonModule.Ajax({
					url: "/api/PtProductInfo/PtCascadeProperti",
					type: "POST",
					contentType: 'application/json',
					data: JSON.stringify(options),
					success: function (rsp) {
						if (commonModule.IsError(rsp)) {
							return;
						}
						if (rsp.Success) {
							var getCatePropsItem = rsp.Data;

							if (getCatePropsItem == "" || getCatePropsItem == null) {
								return;
							}

							//var getRule = getCatePropsItem.Rule;
							//var SelectRule = CatePropsItem.Rule;
							//if (!getRule.IsImportant && !getRule.IsRequired) {//如果获取属性 IsImportant和IsRequired都为false 就按级联属性设置
							//	getRule.IsImportant = SelectRule.IsImportant;
							//	getRule.IsRequired = SelectRule.IsRequired;
							//}
							var pId = getCatePropsItem.ShowCondition[0];
							var tarId = getCatePropsItem.Id;
							var tarIndex = 0;
							getCatePropsItem.Pids = getCatePropsItem.ShowCondition;
							delete getCatePropsItem.ShowCondition;

							CateProps.forEach(function (item, i) {
								if (item.Id == tarId) {
									CateProps.splice(i, 1);
								}
							})

							CateProps.forEach(function (item, i) {
								if (item.Id == pId) {
									tarIndex = i;
								}
							})
							CateProps.splice(tarIndex + 1, 0, getCatePropsItem);
							initCateProps();
							showRemindTarData();//渲染填写提示

						}
					}
				});
			}
		}
	}

	inputCateProps = function (name, type, index) {
		var that = this;
		var text = $(this).val().trim();
		if (type == "measure") {
			CateProps.forEach(function (item, i) {
				if (item.Name == name) {
					item.Value = text + item.MeasureTemplates.RightValue;
					item.MeasureTemplates.LeftValue = text;
				}
			})
		} else if (type == "multiValueMeasure") {
			$(this).val(text);
			var re = /^\d+$/;
			if (re.test(text) === false || text > 100) {
				commonModule.w_alert({ type: 2, content: '请输入100内，正整数' });
				$(this).val("");
				text = "";
			}

			CateProps.forEach(function (item, i) {
				if (item.Name == name) {
					item.MeasureTemplates.RightValue = text;
					item.MeasureTemplates.MultiValueMeasureList.forEach(function (cItem, cI) {
						if (cI == index) {
							cItem.Value = text;
						}
					})
				}
			})
		}
		else {
			CateProps.forEach(function (item, i) {
				if (item.Name == name) {
					item.Value = text;
				}
			})
		}
		if (text == "") {
			$(that).closest(".n-mySelect").removeClass("hasActive");

		} else {

			$(that).closest(".n-mySelect").addClass("hasActive");
			$(that).closest(".catePropsItemWrap").removeClass("formWarn");

		}

		showRemindTarData();//渲染填写提示
	}


	selectDistributionSet = function (name, type, index) {

		var text = $(this).html();
		$(this).closest(".n-mySelect").find('.n-mySelect-showContent-ul-li').removeClass('activeItem').removeClass('active');
		$(this).closest(".n-mySelect").find('.n-mySelect-showContent-ul-li').removeClass('activeItem');
		$(this).addClass('activeItem').closest(".n-mySelect").addClass("hasActive").find('.n-mySelect-title-chooseItem').html(text);
		$(this).closest(".catePropsItemWrap").removeClass('formWarn');

	}


	module.searchCateProps = function () {
		var val = $(this).val().trim();
		var $lis = $(this).closest('.n-mySelect-showContent').find('.n-mySelect-showContent-ul-li');
		if (val == "") {
			$lis.each(function (i, item) {
				$(item).css({ display: 'flex' })
			})

		} else {
			$lis.each(function (i, item) {
				if ($(item).text().indexOf(val) != -1) {
					$(item).css({ display: 'flex' })
				} else {
					$(item).css({ display: 'none' })
				}
			})
		}
	}

	searchBrand = function (id) {
		var $val = $("#searchInputEle").val().trim();
		var options = {};
		options.PropId = id
		options.Name = $val;
		options.CateId = LastCategoryId ? LastCategoryId:InitCategoryId;
		options.PlatformType = setPlatformTypeName;
		options.ShopId = 0;

		var that = this;
		if ($val == "") {

			//CateProps.forEach(function (item) {
			//	if (item.FieldType == "brandSearch") {
			//		var rHtml = "";						
			//		item.Options.forEach(function (item, i) {
			//			rHtml += '<li class="n-mySelect-showContent-ul-li" onclick=\'selectCateProps.bind(this)("品牌")\'>' + item + '</li>';
			//		});
			//		$(that).closest(".n-mySelect-showContent").find(".n-mySelect-showContent-ul").html(rHtml);
			//	}
			//});
			//getBrandOptions();
			commonModule.w_alert({ type: 3, content: '请输入品牌' });

			return;
		}
		commonModule.Ajax({

			url: '/api/Listing/GetBrand',
			type: "POST",
			loading: true,
			contentType: 'application/json',
			data: JSON.stringify(options),
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					$(that).removeClass("active");
					return;
				}
				if (rsp.Success) {
					var Brands = rsp.Data
					var html = "";
					if (Brands.length == 0) {
						html = '<li class="n-mySelect-showContent-ul-li noData">暂无数据</li>';

					} else {
						Brands.forEach(function (item, i) {
							html += '<li class="n-mySelect-showContent-ul-li" onclick=\'selectCateProps.bind(this)("品牌")\'>' + item.Value + '</li>';
						});
					}
					$(that).closest(".n-mySelect-showContent").find(".n-mySelect-showContent-ul").html(html);
				}
			}
		});
	}

	delCateProps = function (name, index) {

		CateProps.forEach(function (item, i) {
			if (item.Name == name) {
				item.MeasureTemplates.MultiValueMeasureList.forEach(function (cItem, cI) {
					if (cI == index) {
						item.MeasureTemplates.MultiValueMeasureList.splice(cI, 1);
					}
				})
			}
		})
		initCateProps();
		showRemindTarData();//渲染填写提示
	}

	//加载自定义运费模板
	function GetShippingFeeTemplate() {
		commonModule.Ajax({
			url: "/ShippingFeeTemplate/LoadList",
			type: "POST",
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				var templates = rsp.Data || [];
				var html = '<li class="n-mySelect-showContent-ul-li noData" onclick="selectCommonOptions.bind(this)()">暂无数据</li>'
				if (templates.length > 0) {
					html = '<li class="n-mySelect-showContent-ul-li" data-type="" onclick=\'selectCommonOptions.bind(this)("TemplateType")\'>请选择</li>';
					templates.forEach(function (item) {
						var TemplateTypeName = item.TemplateType == "Regular" ? '按固定运费计算' : '按订单商品数量计算';
						html += '<li class="n-mySelect-showContent-ul-li" data-type="' + item.TemplateType + '" onclick=\'selectCommonOptions.bind(this)("TemplateType")\'>' + TemplateTypeName + '</li>'
					});
				}

				$("#ShippingFeeTemplate").html(html);

			}
		});
	}



	//编辑类目结束------------------------------------------------------------------------------------------------------------------------------------



	module.getProductDetailsPic = function (resultData, isSubtr) {

		var maxPic = 50;
		if (resultData.length + productDetailsPics.length > maxPic) {
			layer.msg('详情图不能超过' + maxPic + '张');
		}
		if (resultData.length + productDetailsPics.length == maxPic) {
			$("#productPics").hide();
		} else {
			$("#productPics").show();
		}

		productDetailsPics = productDetailsPics.concat(resultData);
		productDetailsPics = productDetailsPics.slice(0, 50);
		$("#readDetailsPicsNumShow").html(productDetailsPics.length);
		$('.decorateWrap-inputWarnTitle').hide();
		renderProductDetailsPics();
	}


	function renderProductDetailsPics() {

		var html = "";
		var tplt = $.templates("#detailsPics_temp");
		var html = tplt.render({
			productDetailsPics: productDetailsPics
		});
		$("#productDetailsPicWrap").html(html);
		$(".decorateWrap").css({ borderColor: '#dcdee1' });
		$(".detailsPicsWrap .input-warnTitle").css({ display: 'none' });

		dragSortModule.initSortMenu("detailsPicsShow_ul", productDetailsPics, function (newArry) {
			productDetailsPics = newArry;
		});
		showRemindTarData();


	}

	//初始化主图
	renderProductImages();
	function renderProductImages() {
		showRemindTarData();
		var tplt = $.templates("#newProductPicShow_temp");
		var html = tplt.render({
			productImages: productImages
		});
		$("#newProductPicShow").html(html);

		var productPicsOptions = {};
		productPicsOptions.isMultiple = true;
		productPicsOptions.callBack = getProductPic;
		productPicsOptions.limit = 10;
		productPicsOptions.isMultiple = true;
		productPicsOptions.businessType = businessType;
		productPicsOptions.typeFlag = 1;

		$(".addnewProductPicShow_item").each(function (i, item) {
			productPicsOptions.ele = "#" + $(item).attr("id");
			var upProductPicsOptionsMenu = new upPicFileModule();
			upProductPicsOptionsMenu.initData(productPicsOptions);
		})
		dragSortModule.initSortMenu("newProductPicShow_ul", productImages, function (newArry) {
			productImages = newArry;
			lsReSortlmage = true;
		})

		function getProductPic(resultData) {
			if (resultData.length + productImages.length > 10) {
				layer.msg("主图最多只能上传10张！")
				return;
			}
			for (var i = 0; i < resultData.length; i++) {
				resultData[i].ImageUrl = resultData[i].TransitUrl
				productImages.push(resultData[i]);
			}
			$('.productPicWrap-inputWarnTitle').hide();
			renderProductImages();
		}



	}

	module.delProductPic = function (index) {

		for (var i = 0; i < productImages.length; i++) {
			if (index == i) {
				if (!productImages[i].ClassName) {
					DeleteObjectIdStr += productImages[i].getId + ",";
				}
				productImages.splice(i, 1);
			}
		}

		renderProductImages();
	}

	module.delDetailsPic = function (index) {
		for (var i = 0; i < productDetailsPics.length; i++) {
			if (i == index) {
				productDetailsPics.splice(i, 1);
				break;
			}

		}
		$(this).closest('.productDetailsPicWrap').remove();
		$("#productPics").show();
		$("#readDetailsPicsNumShow").html(productDetailsPics.length);
		renderProductDetailsPics();
	}


	// 以下库存商品规格js-----------------------------------------------------------------------


	// SKU规格属性名
	var WareHouseSkuAttributeNameData = [{
		Id: 1,
		AttributeName: "color",
		Name: '颜色',
		isCheck: false,
		Value: ''
	},
	{
		Id: 2,
		AttributeName: "size",
		Name: '尺寸',
		isCheck: false,
		Value: ''
	},
	{
		Id: 3,
		AttributeName: "custom",
		Name: '+ 新建规格名',
		isCheck: false,
		Value: ''
	},

	];

	var tableTdData = [{
		Name: "规格简称",
		AttributeName: "ShortTitle",
		minWidth: "100px",
		Value: ''
	},
	{
		Name: "成本价",
		AttributeName: "CostPrice",
		minWidth: "100px",
		Value: ''
	},
	{
		Name: "供货方式",
		AttributeName: "BaseProductSkuSupplierConfig",
		minWidth: "100px",
		Value: []
	},
	{
		Name: "对厂家结算价",
		AttributeName: "SettlePrice",
		minWidth: "100px",
		Value: ''
	},
	{
		Name: "默认分销价",
		AttributeName: "DistributePrice",
		minWidth: "100px",
		Value: ''
	},
	{
		Name: "库存",
		AttributeName: "StockCount",
		minWidth: "150px",
		Value: ''
	},
	{
		Name: "规格编码",
		AttributeName: "SkuCode",
		minWidth: "100px",
		Value: ''
	}
	]
	var skuNameObj = {
		Title: "规格名",
		Name: "SkuName",
		minWidth: "100px"
	};
	var changeTableTdData = JSON.parse(JSON.stringify(tableTdData));
	changeTableTdData.unshift(skuNameObj);


	//定义Sku表格列 
	var skuColumns = [
		{
			id: 1,
			field: "SkuName",
			name: "规格",
			display: true,
			order: 1,
			width: "240px",
			headerFormattter: skuHeaderFormattter,
			contentFormatter: skuContentFormatter
		},
		{
			id: 3,
			field: "SettlePrice",
			name: "采购价",
			display: true,
			order: 3,
			width: "100px",
			headerFormattter: null,
			contentFormatter: null
		},
		{
			id: 4,
			field: "DistributePrice",
			name: "分销价",
			display: true,
			order: 4,
			width: "100px",
			headerFormattter: null,
			contentFormatter: null
		},
		{
			id: 5,
			field: "SinglePrice",
			name: "单买价",
			display: true,
			order: 5,
			width: "130px",
			headerFormattter: null,
			contentFormatter: null
		},
		{
			id: 6,
			field: "SalePrice",
			name: "拼单价",
			display: true,
			order: 6,
			width: "100px",
			headerFormattter: null,
			contentFormatter: null
		},
		{
			id: 7,
			field: "StockCount",
			name: "库存",
			display: true,
			order: 7,
			width: "100px",
			headerFormattter: null,
			contentFormatter: null
		},
		{
			id: 8,
			field: "SkuCode",
			name: "SKU 编码",
			display: true,
			order: 8,
			width: "150px",
			headerFormattter: null,
			contentFormatter: null
		}

	];

	//if (isBatchPuhuo) {
	//	var SettlePriceObj = {
	//		id: 3,
	//		field: "SettlePrice",
	//		name: "采购价",
	//		display: true,
	//		order: 3,
	//		width: "100px",
	//		headerFormattter: null,
	//		contentFormatter: null
	//	};
	//	skuColumns.splice(1, 0, SettlePriceObj);
	//}


	//自定义表格式内容
	function skuHeaderFormattter(col) {
		var html = '';
		html += '<div style="display:flex;">';
		html += '<span class="n-newCheckbox" id="allCheckSkubox" onclick="PrepareDistributionModule.allCheckSkubox.bind(this)()"></span>';
		html += '<span id="skuNameWrap">规格</span>';
		html += '<div>';
		return html;
	}

	function skuContentFormatter(row, col, index, cIndex, isHasRawResultSkus) {
		//console.log("row===",row)
		var className = row.SkuObj.length == 1 ? 'tdskubodyS' : 'tdskubody';
		var html = "";
		if (row.isNew && OperateType == "edmit") {
			html += '<span class="newStatusWrap">';
			html += '<span class="n-tarTxt n-tarTxt01 newStatus">新</span>';
			html += '</span>';

		}
		html += '<div class="' + className + '">';
		html += '<span id="chx_' + index + cIndex + '" data-index="' + index + '" class="n-newCheckbox c-chx" onclick="PrepareDistributionModule.singleCheckSkubox.bind(this)(' + index + ',' + cIndex + ')"></span>';
		var values = '';
		if (row.SkuObj.length == 1) {
			values = row.SkuObj[0].Value + (row.SkuObj[0].Remark ? "(" + row.SkuObj[0].Remark + ")" : '');
			// 有单独sku图片，规格类型图片不显示
			// var imgSrc = '/Content/images/nopic.gif';
			// if (row.fristSkuObj.Img && isShowImg) {
			// 	if (row.fristSkuObj.Img.indexOf("http") == 0) {
			// 		imgSrc = row.fristSkuObj.Img;
			// 	} else {
			// 		imgSrc = commonModule.transformNewImgSrc(row.fristSkuObj.Img);
			// 	}
			// }
			// html += '<div class="n-productInfo-img"><img src="' + imgSrc + '" alt=""></div>'
		} else {
			for (var i = 1; i < row.SkuObj.length; i++) {
				values += row.SkuObj[i].Value + (row.SkuObj[i].Remark ? "(" + row.SkuObj[i].Remark + ")" : '') + "·";
			}
			values = values.substr(0, values.length - 1);

		}
		// sku图片
		if (row.ImageUrl == "" || row.ImageUrl == null) {
			html += '<div class="newProductPicShow-itembox">';
			html += '<div class="n-productInfo-img-add" data-index="' + index + '" data-cindex="' + cIndex + '" id="addPic_' + index+cIndex + '">';
			html += '<i class="iconfont icon-a-image-add1x1 upPic-icon"></i>';
			html += '</div>';
			html += '</div>';
		} else {
			html += '<div class="newProductPicShow-itembox">';
			html += '<div class="newProductPicShow-item" >';
			html += '<img src="' + commonModule.newTransformImgSrc(row.ImageUrl) + '" />';
			html += '<span class="iconfont icon-icon_shanchu-" onclick="PrepareDistributionModule.delCommonSkuProductPic(' + index + ',' + cIndex + ')"></span>';
			html += '</div>';
			html += '</div>';
		}
		if (isHasRawResultSkus) {
			html += '<div class="n-productInfo-right">';
			html += '<span class="moreSku-title">' + values + '</span>';

			if (isRawResultSkus) {
				html += '<div class="matchedMoreSku" onclick="PrepareDistributionModule.matchedMoreSku.bind(this)(' + index + ',' + cIndex + ')">';
				html += '<span class="matchedMoreSku-title-wrap">';
				html += '<span class="matchedMoreSku-title">匹配原规格</span>';
				html += '<span class="iconfont icon-down"></span>';
				html += '</span>';

				html += '<span class="matchedMoreSku-title2">匹配原规格</span>';
				html += '<span class="iconfont icon-a-close-circle-filled1x"  onclick="PrepareDistributionModule.delMatchedMoreSku.bind(this)(' + index + ',' + cIndex + ')"></span>';
			}

			html += '</div>';
			html += '</div>';

		} else {
			html += '<span  class="moreSku-title">' + values + '</span>';
		}

		html += '</div>';
		return html;
	}


	// 库存商品规格	
	var WareHouseSkuLists = []
	var changeSkuResults = [];
	var changeSkuResult = [];
	var WareHouseSkus = []; //规格明细表
	var oldSkuName = "";
	var meuResultData = []; //规格类型数据结构
	var newWareHouseSkuLists = [];
	var oldWareHouseSkuLists = [];//添加前的老数据
	var isShowImg = false;  //是否添加规格图片


	function initSkuSelect(type, setObj, i) {
		var obj = {};
		if (setObj) {
			obj.Id = new Date().getTime() + i;
			obj.SelectItem = setObj.SelectItem;
			obj.IsSelectSku = true;
			obj.IsAllow = false;//是否 添加规格图片
			obj.sort = new Date().getTime() + i;
			obj.IsEdit = true;//是否展开编辑

		} else {
			obj.Id = new Date().getTime();
			obj.SelectItem = {};
			obj.IsSelectSku = false;
			obj.IsAllow = false;//是否 添加规格图片
			obj.sort = new Date().getTime();
			obj.IsEdit = true;//是否展开编辑
		}

		obj.newWareHouseSkuAttributeNameData = WareHouseSkuAttributeNameData;
		if (type == 1) {
			meuResultData.push(obj);
		}
		renderSkuMeu(meuResultData);
	}

	function renderSkuMeu(meuResultData) {  //规格类型 模块渲染

		meuResultData = meuResultData.sort(function (a, b) { return a.sort - b.sort; });
		var tplt = $.templates("#addSkuWrap_Skus");
		var html = tplt.render({
			meuResultData: meuResultData,
			isShowImg: isShowImg,
			operateType: OperateType,
			attribute: PtProductDatas.SellPropertyJson,
			isSelectSkuType: PtProductDatas.SellPropertyCateProp && PtProductDatas.SellPropertyCateProp.length > 0 ? true : false,
		});
		$("#addSkuWrap").html(html);
		// 拼多多平台商品规格图片不显示
		$('#addSkuWrap_Skus_ul li:eq(0) .addSkuWrap-Skus-main-spuImg').hide();
		initSkuMeuPic();

		if ($("#addSkuWrap_Skus_ul>.addSkuWrap-Skus").length > 1) {

			dragSortModule.initSortMenu("addSkuWrap_Skus_ul", meuResultData, function (newArry, sortArry) {
				//console.log("newArry", newArry)
				//console.log("sortArry", sortArry)

				//排序
				var $sort = [];
				sortArry.forEach(function (item, index) {
					$sort.push(meuResultData[item].sort);
				})
				meuResultData.forEach(function (item, i) {
					item.sort = $sort[i];
				})
				renderSkuMeu(meuResultData);
				combinationTable(meuResultData);
				initAddSkuTable();
				initSkuMeuPic();
				setSkuTableValue();
			})

		}
		// sku值 - 特殊分类模板单选事件
		meuResultData.forEach(function (item, index) {
			if (item.SelectItem.special_values && item.SelectItem.special_values.length > 0) {
				item.SelectItem.special_values.forEach(function (item2, index2) {
					if (item2.isCheck) {
						var domName = "#addSkuWrap_Skus_ul > li:eq("+index+") .addSkuWrap-Skus-main-radioBox input[type='radio']";
						//console.log("$(domName)===",$(domName))
						$(domName).eq(index2).prop("checked", true);
						$(domName).off();
						$(domName).on("click", function(){
							var value = $(this).val();
							item.SelectItem.special_values.forEach(function (item3, index3){
								item3.isCheck = false;
								if (item3.id == value) {
									item3.isCheck = true;
								}
							});
							// 切换模板时，清空sku值
							if (item.SelectItem.AttributeData){
								item.SelectItem.AttributeData.forEach(function (item3, index3){
									item3.Value = '';
									PtProductDatas.AttributeTypes[index].AttributeValues[index3].Value = '';
									PtProductDatas.AttributeTypes[index].AttributeValues[index3].SellPropertyValues.sell_property_value_name = '';
									PtProductDatas.AttributeTypes[index].AttributeValues[index3].SellPropertyValues.ext2 = value;  //保存特殊分类模板选中的值
								})
							}
							
							renderSkuMeu(meuResultData);
						})
					}
				})
				

			}
		})
		// 规格类型下拉
		$("#addSkuWrap_Skus_ul .catePropsItem .n-mySelect-title").off();

		$("#addSkuWrap_Skus_ul .catePropsItem .n-mySelect-title").on("click", function () {
			event.stopPropagation();
			$("#addSkuWrap_Skus_ul .catePropsItem").removeClass("active");
			$("#otherCateProps .catePropsItem").removeClass("active");

			$(this).closest(".catePropsItem").addClass("active");
			$(this).closest(".catePropsItem").find(".searchInput").val("");
			$(this).closest(".catePropsItem").find(".n-mySelect-showContent-ul-li").css({ display: 'flex' });
			var that = $(this);
			$(this).closest(".catePropsItem").find(".n-mySelect-showContent-ul-li").off();
			$(this).closest(".catePropsItem").find(".n-mySelect-showContent-ul-li").on("click", function(){
				var name = $(this).html();
				var checkfindIndex = meuResultData.findIndex(function (item,index) {return item.SelectItem.Name == name});
				if(checkfindIndex > -1) {
					commonModule.w_alert({ type: 1, content: '请不要重复选择规格类型', times: 500 });
					return false;
				}
				$(that).find('.n-mySelect-title-left-title').html(name);	
				var val = $(this).attr("value");
				var datapId = $(this).attr("data-pid");
				$(this).closest(".catePropsItem").removeClass("active");
				//CreateBasePlatformProductModule.edmitSkuValue(datapId,name,name,val);
				meuResultData.forEach(function (item,index) {
					if (item.Id == datapId) {
						item.SelectItem.AttributeName = name;
						item.SelectItem.Name = name;
						item.SelectItem.oldName = name;
						PtProductDatas.AttributeTypes[index].AttributeName = name;
					} else {
						item.newWareHouseSkuAttributeNameData.forEach(function(item2,index2){
							isSelect = false;
							if(item2.Name == name){
								isSelect = true;
							}
						})
					}
				})
				combinationTable(meuResultData);
				initAddSkuTable();
				setSkuTableValue();
				renderSkuMeu(meuResultData);
			})
			
			$("#productSku").off()
			$("#productSku").on("click", function (){
				$(that).closest(".catePropsItem").removeClass("active");
			})
		})
	}

	var activeAttributeName = "";
	var activeAttributeId = "";

	module.clickSelectChange = function (_this, id) {
		activeAttributeName = $(_this).val();
		activeAttributeId = id;
	}

	var activeOptionVal = null;
	module.mappingSelectChange = function (_this, id) { //选择规格名下拉框事件
		isConcatOld = false;
		$("#addSkuWrap select").removeClass("active");
		$(_this).addClass("active");
		var selectedVal = $(_this).val();
		activeOptionVal = selectedVal;
		var mappedVal = $(_this).attr("data-mapped");

		if (selectedVal == "" || selectedVal != "custom") {
			meuResultData.forEach(function (item, i) {
				item.newWareHouseSkuAttributeNameData.forEach(function (cItem, cI) {
					if (cItem.AttributeName == activeAttributeName) {
						cItem.isCheck = false;
					}
				})
			})
		}

		if (selectedVal !== "" && selectedVal != "custom") {
			$(_this).attr("data-mapped", selectedVal).closest(".addSkuWrap-Skus").attr("data-mapped",
				selectedVal);
			// $(_this).find("option[value=" + val + "]").prop("selected", true);
			var selectObj = {};
			for (var i = 0; i < WareHouseSkuAttributeNameData.length; i++) {
				if (WareHouseSkuAttributeNameData[i].AttributeName == selectedVal) {
					WareHouseSkuAttributeNameData[i].isCheck = true;
					selectObj = WareHouseSkuAttributeNameData[i];
					selectObj.AttributeData = [];
					break;
				}
			}

			meuResultData.forEach(function (item, i) {
				if (item.Id == id) {
					item.SelectItem = selectObj;
					item.IsSelectSku = true;
				}
			})


			$("#addSkuWrap select").not(".active").each(function (index, item) {
				$(item).find("option[value=" + selectedVal + "]").remove();
			})
			$(_this).removeClass("notMapped");

			$(_this).closest(".addSkuWrap-Skus").find(".addSkuValue").css({
				display: "inline-block"
			})
			initAddSkuTable();

		} else if (selectedVal == "custom") {

			var html = '<input class="layui-input n-layui-input" type="text" id="createCustonSku" placeholder="请输入自定义规格名">';
			layer.open({
				type: 1,
				title: '创建自定义规格类型', //不显示标题
				content: html,
				skin: 'n-skin custonSkuskin',
				area: '430px', //宽高
				btn: ['取消', '保存'],
				yes: function (index) {
					layer.close(index);
				},
				btn2: function (index) {

					var val = $('#createCustonSku').val().trim();
					var isHas = false;
					var title = "";
					if (val == "") {
						title = '请输入值';
						isHas = true;
					}

					WareHouseSkuAttributeNameData.forEach(function (item, i) {
						if (item.Name == val) {
							isHas = true;
							title = "您输入的值已存在";
						}
					})

					tableTdData.forEach(function (item, i) {
						if (item.Name == val) {
							isHas = true;
							title = "您输入的值已在SKU列表上了";
						}
					})


					if (isHas) {
						commonModule.w_alert({ type: 3, content: title });
						return false;
					}



					var setId = new Date().getTime();
					var obj = {
						Id: id,
						AttributeName: "custom_" + setId,
						Name: val,
						isCheck: false
					};

					WareHouseSkuAttributeNameData.splice(WareHouseSkuAttributeNameData.length -
						1, 0, obj);
					layer.close(index);


					meuResultData.forEach(function (item, i) {
						item.newWareHouseSkuAttributeNameData =
							WareHouseSkuAttributeNameData;
					})
					renderSkuMeu(meuResultData);


					if (!$("#mapped_" + activeAttributeId).val()) {
						$("#mapped_" + activeAttributeId + ' option').each(function (i, item) {
							if ($(item).attr('name') == val) {
								$(item).prop("selected", true);
							}
						})
						$("#mapped_" + activeAttributeId).trigger('change');
						setSkuTableValue();


					} else {
						commonModule.w_alert({ type: 1, content: '已添加，请在下拉框选择！', times: 5000 });
					}


				}
			});

		} else {
			meuResultData.forEach(function (item, i) {

				if (item.Id == id) {
					item.SelectItem = {};
					item.IsSelectSku = false;
				}

			})

			for (var i = 0; i < WareHouseSkuAttributeNameData.length; i++) {
				if (WareHouseSkuAttributeNameData[i].AttributeName == mappedVal) {
					WareHouseSkuAttributeNameData[i].isCheck = false;
					break;
				}
			}
			$(_this).attr("data-mapped", "").closest(".addSkuWrap-Skus").attr("data-mapped", "");
			$(_this).addClass("notMapped");
			$(_this).closest(".addSkuWrap-Skus").find(".addSkuValue").hide();
		}
		renderSkuMeu(meuResultData);
		combinationTable(meuResultData);
		initAddSkuTable();
		initSkuMeuPic();
	}


	function addSkuBtnHideOrShow() { //添加规格信息按钮显示或隐藏
		var getSelectLenghth = $("#addSkuWrap .addSkuWrap-Skus").length;
		if (getSelectLenghth > 0) {
			$(".addSkuWrap").show();
		} else {
			$(".addSkuWrap").hide();

		}
		if (getSelectLenghth >= 3) { //规格名最多三个，超过3个隐藏添加按钮
			$("#addSkuBtnWrap").hide();
		} else {
			$("#addSkuBtnWrap").show();
		}
	}

	module.addSkuAttributes = function () { //添加规格信息
		$("#addSkuBtn .noInputData").remove(); //删除警告框和提示语
		$(".addSkuWrap").removeClass("activeBorderColor"); //删除警告框和提示语		
		initSkuSelect(1);
		addSkuBtnHideOrShow();
		sortMenuSkuAttribute(); //规格值排序 监听事件
	}

	module.sureDelSkuAttributes = function (_this, id, tdName) {

		if (tdName) {

			layer.open({
				type: 1,
				title: '是否删除该规格类型？', //不显示标题
				content: '<div style="font-size:14px;" class="c06">删除后，其他规格数据也将清空，可根据原规格重新手动匹配。</div>',
				area: '560px', //宽高
				btn: ['取消', '确定删除'],
				skin: 'n-skin goBackskin',
				shade: false,
				yes: function (index) {
					layer.close(index);
				},
				btn2: function () {
					module.delSkuAttributes(_this, id, tdName);
				}
			});
		} else {
			module.delSkuAttributes(_this, id, tdName);
		}



	}

	module.delSkuAttributes = function (_this, id, tdName) { //删除规格信息

		var $select = $(_this).closest('.addSkuWrap-Skus').find(".n-select");
		$select.find("option[value='']").attr("selected", true);
		var getAttributeName = $(_this).closest(".addSkuWrap-Skus").attr("data-mapped");
		module.mappingSelectChange($select.get(0), id); //主要是删除后，SKU规格属性名选中 释放出来
		if (getAttributeName) {
			for (var i = 0; i < WareHouseSkuAttributeNameData.length; i++) {
				if (getAttributeName == WareHouseSkuAttributeNameData[i]
					.AttributeName) { //删除后 SKU规格属性名恢复没有选中 isCheck=false
					WareHouseSkuAttributeNameData[i].isCheck = false;
				}
			}
			for (var j = 0; j < WareHouseSkuLists.length; j++) {
				if (getAttributeName == WareHouseSkuLists[j].AttributeName) { //删除  库存商品规格
					WareHouseSkuLists.splice(j, 1)
				}
			}
		}

		meuResultData.forEach(function (item, i) {
			if (item.Id == id) {
				meuResultData.splice(i, 1);
			}
		})
		renderSkuMeu(meuResultData);
		if (tdName) {
			changeTableTdData.forEach(function (item, i) {
				if (item.Title == tdName) {
					changeTableTdData.splice(i, 1)
				}
			})
		}

		//console.log("meuResultData", meuResultData)

		combinationTable(meuResultData);
		initAddSkuTable();
		addSkuBtnHideOrShow();
		initSkuMeuPic();
		setSkuTableValue();

		//if (OperateType != "edmit") {
		//	setOldAddSkuTableValue();
		//}
	}


	module.addAttributeValue = function (_this, index, id) { //添加sku属性值

		var oldNewWareHouseSkuLists = JSON.parse(JSON.stringify(newWareHouseSkuLists));
		//console.log("oldNewWareHouseSkuLists", oldNewWareHouseSkuLists)

		var val = $(_this).val().trim();
		var obj = {};
		obj.Value = val;
		obj.Id = "attr" + new Date().getTime();
		obj.Img = "";
		obj.ImgObj = null;

		if (obj.Value == "") {
			//layer.msg("请输入规格值");
			return;
		}
		var selectIndex = "";
		var isHas = false;

		var AttributeData = [];

		meuResultData.forEach(function (item, i) {
			if (item.Id == id) {
				selectIndex = i;
				AttributeData = item.SelectItem.AttributeData ? item.SelectItem.AttributeData : [];
				AttributeData.forEach(function (cItem, ii) {
					if (cItem.Value == val) {
						isHas = true;
					}
				});
			}
		})

		if (isHas) {
			layer.msg("您的值已存在");
			return;
		}

		meuResultData[selectIndex].SelectItem.AttributeData.push(obj);
		renderSkuMeu(meuResultData);
		combinationTable(meuResultData);
		//oldNewWareHouseSkuListsSetValue(oldNewWareHouseSkuLists);
		initAddSkuTable();
		initSkuMeuPic();

		if (OperateType == "edmit") {  //来自基础商品编辑
			setSkuTableValue();
			rawResultSkus.forEach(function (item, i) {
				item.isCheck = false;
			});
		} else {
			setOldAddSkuTableValue();
		}
		showRemindTarData();
	}

	//initAddSkuTable();


	function sortMenuSkuAttribute() {  //规格值排序

		$("#addSkuWrap_Skus_ul>.addSkuWrap-Skus").each(function (i, item) {
			var ele = $(item).find(".showSkuValue").attr('id');
			var id = $(item).find(".showSkuValue").attr('data-id');
			var AttributeData = [];
			meuResultData.forEach(function (item, i) {
				if (item.Id == id) {
					AttributeData = item.SelectItem.AttributeData ? item.SelectItem.AttributeData : [];
				}
			})
			if (ele) {
				dragSortModule.initSortMenu(ele, AttributeData, function (newArry, sortArry) {
					meuResultData.forEach(function (item, i) {
						if (item.Id == id) {
							item.SelectItem.AttributeData = newArry;
						}
					})
					renderSkuMeu(meuResultData);
					combinationTable(meuResultData);
					initAddSkuTable();
					initSkuMeuPic();
					setSkuTableValue();


				}, '.cHandle');
			}



		})
	}

	var skuTable = {};
	skuTable.rows = [{ Id: 1, Index: 0, SkuName: '', SettlePrice: 10, CostPrice: 50, DistributePrice: 10, StockCount: 100, SkuCode: 'sbe122', ShortTitle: '' }];
	skuTable.skuColumns = skuColumns;
	module.edmitSkuValue = function (id, name, oldName) {
		var val = $(this).val().trim();
		if (val == "") {
			$(this).val(name);
			layer.msg('请输入值');
			return;
		}
		var isHas = false;
		meuResultData.forEach(function (item) {
			var SelectItem = item.SelectItem;
			if (SelectItem.Name == val) {
				isHas = true;
			}
		})
		if (isHas) {
			return;
		}
		meuResultData.forEach(function (item) {

			if (item.Id == id) {
				item.SelectItem.Name = val;
				item.SelectItem.oldName = oldName;
			}
		})
		combinationTable(meuResultData);
		initAddSkuTable();
		setSkuTableValue();

	}
	function initNewAddSkuTable(element, rows, isHasRawResultSkus) {

		if (!isRawResultSkus) {
			try {
				if (rewAttributeTypes.length != newWareHouseSkuLists[0].datas[0].SkuObj.length) {
					isRawResultSkus = true;
				}
			} catch (error) {
				isRawResultSkus = false;
			}
		};

		var rowsLen = rows.length;
		if (rowsLen > 0) {

			var batchSelectData = [
				// { Name: 'SettlePrice', Title: '采购价' },
				// { Name: 'CostPrice', Title: '成本价' },
				{ Name: 'SalePrice', Title: '拼单价' },
				{ Name: 'SinglePrice', Title: '单买价' },
				{ Name: 'StockCount', Title: '库存' },
				//{ Name: 'SkuCode', Title: 'SKU 编码' },

				// { Name: 'ShortTitle', Title: '简称' },

			];
			var html = '';
			html += '<div class="skuTableWrap-title" style="position:relative;z-index:100">';
			html += '<div>';
			html += '<span class="skuTableWrap-title-left">价格与库存</span>';

			html += '<div class="n-mySelect n-single" id="selectBatchWrap" style="width:96px;z-index:10;" onclick="selectBatchFun.bind(this)()">';
			html += '<div class="n-mySelect-title" >';
			html += '<div class="n-mySelect-title-left">';
			html += '<span class="n-mySelect-title-placeholder" style="color:rgba(0,0,0,0.9);">批量编辑</span>';
			html += '</div>';
			html += '<span class="iconfont icon-a-chevron-down1x"></span>';
			html += '</div>';
			html += '<div class="n-mySelect-showContent">';
			html += '<ul class="n-mySelect-showContent-ul">';
			batchSelectData.forEach(function (item, i) {
				//if (item.Name == 'SalePrice') {
				//	html += '<li class="n-mySelect-showContent-ul-li" onclick=\'PrepareDistributionModule.onDistributionSet("Price",true)\'>' + item.Title + '</li> ';
				//} else if (item.Name == 'StockCount') { 
				//	html += '<li class="n-mySelect-showContent-ul-li" onclick=\'PrepareDistributionModule.onDistributionSet("Stock",true)\'>' + item.Title + '</li> ';
				//} else {
				//	html += '<li class="n-mySelect-showContent-ul-li" onclick=\'PrepareDistributionModule.batchSkus("' + item.Name + '")\'>' + item.Title + '</li> ';
				//}
				html += '<li class="n-mySelect-showContent-ul-li" onclick=\'PrepareDistributionModule.batchSkus("' + item.Name + '")\'>' + item.Title + '</li> ';
			})

			html += '</ul>';
			html += '</div>';
			html += '</div>';

			html += '</div>';

			//html += '<span class="n-dColor hover" onclick="PrepareDistributionModule.fullScreen()">';
			//html += '<i class="iconfont icon-a-fullsreen1x"></i>';
			//html += '<i class="iconfont icon-a-fullscreen-exit1x" style="display:none"></i> ';
			//html += '<span id="fullscreenText">编辑更多信息</span>';
			//html += '</span>';
			html += '</div>';
			html += '<div id="skuTable">';

			html += '<table class="n-table edmitTable" id="custom_Table_Wrap"><thead><tr>';
			var cols = skuTable.skuColumns;
			$(cols).each(function (index, col) {
				var style = "";
				if (col.width)
					style += "width:" + col.width + ";";
				if (col.field === "DistributePrice" && !isFxUser) {
					col.display = false
				}
				if (!col.display)
					style += "display:none";

				// 通过别名字段动态更新字段值
				html += '<th class="order-column-header-' + col.field + '" ' + ' style="' + style + '">' +
					defaultHeaderFormatter(col) + '</th>';
			});
			html += "</tr></thead>";
			html += '<tbody class="table_content_tbody" id="sku_tbody">';
			//生成表格内容
			$(rows).each(function (index, row) {
				html += renderSkuRow(index, cols, row, isHasRawResultSkus);
			})
			html += "</tbody></table>";
			html += '</div>';
			element.innerHTML = html;

			var isMoreOperate = rows[0].datas[0].SkuObj.length > 1 ? true : false;  // thead th 上是否显示批量操作icon
			if (isMoreOperate) {
				$("#skuTable th").addClass("active");
			} else {
				$("#skuTable th").removeClass("active");
			}
			showCheckBoxStatus();
			listenCustomAddPic();
			//if (OperateType == "edmit" ) {  //来自基础商品编辑
			//	$("#skuTable .n-table").addClass("edmitTable");
			//} 


		} else {
			$("#newSkuTableWrap").html("");
		}
	}

	function renderSkuRow(i, cols, row, isHasRawResultSkus) {
		var html = '';
		if (row.datas[0].SkuObj.length > 1) {  //批量操作tr 
			var trClassName = row.datas[0].isCheck ? 'chx chxActive' : 'chx'

			html += '<tr id="tr_' + i + '" data-index="' + i + '"  class="' + trClassName + '">';
			$(cols).each(function (index, col) {
				var style = "";
				if (!col.display)
					style += "display:none";
				if (col.field == "SkuName") {
					var imgSrc = '/Content/images/nopic.gif';

					if (row.fristSkuObj.Img.indexOf('http') == 0) {
						imgSrc = row.fristSkuObj.Img;

					} else {
						if (row.fristSkuObj.Img != '//.' && isShowImg && row.fristSkuObj.Img != '') {
							imgSrc = commonModule.transformNewImgSrc(row.fristSkuObj.Img);
						}
					}



					html += '<td style="' + style + '"  class="' + col.field + '">';
					html += '<div class="n-productInfo" style="position:relative;">';
					//if (row.isNew) {
					//	html += '<span class="n-tarTxt n-tarTxt01 newStatus">新</span>'
					//}
					html += '<span  data-index="' + i + '" class="n-newCheckbox f-chx" onclick="PrepareDistributionModule.partCheckSkubox.bind(this)(' + i + ')"></span>';
					html += '<div class="n-productInfo-img">';
					html += '<img src="' + imgSrc + '" alt="">';
					html += '</div>';
					html += '<ul class="n-productInfo-texts">';
					html += '<li class="c09 n-productInfo-title" style="margin-bottom: 5px;font-size:14px;"><span class="n-productInfo-left">' + row.fristSkuObj.Value + (row.fristSkuObj.Remark ? "(" + row.fristSkuObj.Remark + ")" : '') + '· </span>';

					html += '<span class="c06" style="font-size:14px;">' + row.datas.length + '个规格</span>';
					html += '<span class="iconfont icon-down active" onclick="PrepareDistributionModule.showMoreSku.bind(this)(' + i + ')"></span>';
					html += '</li>';
					html += '</ul>';
					html += '</div>';
					html += '</td>';
				} else {
					if (col.field == 'StockCount' || col.field == 'SkuCode' || col.field == 'ShortTitle') {
						html += '<td style="' + style + '"  class="' + col.field + '"><div style="display:none;" value="-" class="n-layui-input" type="text" disabled></div></td>';
					} else {
						html += '<td style="' + style + '"  class="' + col.field + '"><div style="display:none;" class="priceInputWrap"><div  onchange=\'PrepareDistributionModule.batchSkuItems.bind(this)(' + i + ',"' + col.field + '")\' class="n-layui-input priceInput" type="text" placeholder="0.00"></div><i class="priceIcon">￥</i></div></td>';
					}
				}
			})
			html += '</tr>';
		}
		$(row.datas).each(function (cI, cRow) {
			var trClassName = cRow.isCheck ? 'chx itemChx chxActive' : 'chx itemChx'
			html += '<tr data-index="' + i + '" class="' + trClassName + '">'


			$(cols).each(function (index, col) {


				var style = "";
				if (!col.display) {
					style += "display:none";
				}
				if (cRow.IsDefaultPadding) {
					if (col.field == "SkuName") {
						html += '<td ' + style + '  class="' + col.field + '" colspan="20"><div class="defaultPaddingTdWrap">' + defaultContentFormatter(cRow, col, i, cI, isHasRawResultSkus);
						html += '<span class="f14 defaultPaddingTdWrap-right">不存在此sku，如未补充，则铺货时系统自动补全。</span>';
						html += '</div>';
						html += '</td>';
					}

				} else {
					html += '<td style="' + style + '"  class="' + col.field + ' activeSkuName">' + defaultContentFormatter(cRow, col, i, cI, isHasRawResultSkus); + '</td>';
				}



			})


			html += '</tr>'
		})
		return html;
	}

	//生成表格 th里面内容函数
	function defaultHeaderFormatter(col) {
		if (col.headerFormattter) {
			return col.headerFormattter(col);
		} else {
			var html = '';
			html += '<div class="headerTr">' + col.name + '';
			var helpText = '';
			if (col.field == "SettlePrice") {
				helpText = '采购价应用于财务结算对厂家设置的规格单品结算价';
				//0: 根据基础商品Uid、1：根据平台资料code、2：根据自己货盘、3：根据厂商货盘 4：铺货任务
				if (isFxUser) {
					helpText = '取商品基础资料里面的采购价';
				} else {
					helpText = '取厂家设置的价格';
				}
			} else if (col.field == "CostPrice") {
				helpText = '成本价应用于店铺对账时使用的规格单品成本价';
			} else if (col.field == "DistributePrice") {
				helpText = '分销价应用于财务结算对下游分销商设置的规格单品结算价';
				if (isFxUser) {
					helpText = '取商品基础资料里面的分销价';
				}
			} else if (col.field == "SinglePrice") {
				helpText = '单买价至少比拼单价高1元';
			}
			if (col.field != 'SalePrice') {
				if (OperateType == "edmit") {
					html += '<i class="iconfont icon-a-help-circle1x active" onmouseenter="mouseHelpShowText.bind(this)()">';
					html += '<span class="n-tooltip n-leftdown th-tooltip">' + helpText + '</span>';
					html += '</i> ';
					if (col.field != 'StockCount') {
						html += '<i style="display:none;" class="iconfont icon-a-edit1x" onclick=\'PrepareDistributionModule.batchSkus("' + col.field + '")\'></i>';
					}
				} else {
					html += '<i class="iconfont icon-a-help-circle1x" onmouseenter="mouseHelpShowText.bind(this)()">';
					html += '<span class="n-tooltip n-leftdown th-tooltip">' + helpText + '</span>';
					html += '</i> ';
					html += '<i style="display:none;" class="iconfont icon-a-edit1x" onclick=\'PrepareDistributionModule.batchSkus("' + col.field + '")\'></i>';
				}
			}

			html += '</div>';
			return html;
		}
	};
	function defaultContentFormatter(row, col, index, cIndex, isHasRawResultSkus) {
		if (col.contentFormatter) {
			return col.contentFormatter(row, col, index, cIndex, isHasRawResultSkus);
		} else if (row && col.field) {
			var content = row[col.field];
			var html = '';

			if (col.field == "StockCount") {

				html += '<div class="priceInputWrap">'
				html += '<div class="priceInputWrap n-inputWrap">';
				html += '<input class="n-layui-input" type="text" value="' + content + '"  name="' + col.field + '"  oninput=\'PrepareDistributionModule.checkWareHouseStatus.bind(this)("' + col.field + '")\' onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\'>';
				html += '</div>';

				//if (OperateType == "edmit") {
				//	html += '<span class="edmitStockCountText">' + content + '</span>';
				//	html += '<div class="priceInputWrap-left">';
				//	html += '<input class="changeStockCountInput"  placeholder="加库存" style="padding-left:35px;" class="n-layui-input" type="text" value=""  name="' + col.field + '"  oninput="PrepareDistributionModule.checkWareHouseStatus.bind(this)()" onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"ChangeStockCount")\'>';
				//	//html += '<span class="stockCountText">加库存</span>';
				//	html += '<span class="stockCountIcons"><i class="active" data-status="true" onclick=\'PrepareDistributionModule.InOrOutStockCount.bind(this)(true,' + index + ',' + cIndex + ')\'>+</i><i data-status="false" onclick=\'PrepareDistributionModule.InOrOutStockCount.bind(this)(false,' + index + ',' + cIndex + ')\'>-</i></span>';
				//	html += '</div>';
				//} else {
				//	html += '<div class="priceInputWrap n-inputWrap">';
				//	html += '<input class="n-layui-input" type="text" value="' + content + '"  name="' + col.field + '"  oninput=\'PrepareDistributionModule.checkWareHouseStatus.bind(this)("' + col.field + '")\' onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\'>';
				//	html += '</div>';

				//}

				html += '</div>';
			} else if (col.field == "SkuCode") {
				if (OperateType == "edmit") {
					html += '<input class="n-layui-input inputSkuCode" style="display:none"  type="text" value="' + content + '"  name="' + col.field + '" >';
					html += '<span>' + content + '</span>';
				} else {
					html += '<div class="priceInputWrap">'
					html += '<input class="n-layui-input inputSkuCode"  type="text" value="' + content + '"  name="' + col.field + '" oninput=\'PrepareDistributionModule.changeReSkuForm.bind(this)("' + col.field + '")\'  onclick="PrepareDistributionModule.blurSkuCode.bind(this)(event)"  onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\' placeholder="作为唯一识别编码">';
					html += '<div class="n-toast skuCode-toast n-font5"><i class="hover n-dColor clickSkuCode" onclick="PrepareDistributionModule.createSingleSkuId.bind(this)(' + index + ',' + cIndex + ')"><i class="iconfont icon-a-code1x" style="margin-right:3px;"></i>自动生成编码</i></div>';
					html += '</div>';
				}
			} else if (col.field == "ShortTitle") {
				var ShortTitlecontent = content ? content : '';
				html += '<div class="priceInputWrap">';
				html += '<input class="n-layui-input" type="text" value="' + ShortTitlecontent + '"  name="' + col.field + '"  onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\' placeholder="请输入" maxlength="20" oninput="PrepareDistributionModule.checkShortTitle.bind(this)()">';
				html += '<span class="input-num"><i class="titleLength">' + getByteLen(ShortTitlecontent) + '</i>/20</span>';
				html += '</div>';
			} else if (col.field == "SalePrice") {
				html += '<div class="priceInputWrap n-inputWrap">';
				html += '<input class="n-layui-input priceInput"  type="text" value="' + content + '" name="' + col.field + '" onfocus=\'PrepareDistributionModule.focusReSkuForm.bind(this)("' + col.field + '")\'   oninput=\'PrepareDistributionModule.changeReSkuForm.bind(this)("' + col.field + '")\'  onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\' placeholder="0.00">';
				html += '<i class="priceIcon">￥</i>';
				html += '</div>';
			} else {
				if (content || content === 0 || content === '0') {
					html += '<div class="priceInputWrap n-inputWrap">';
					html += '<input class="n-layui-input priceInput"  type="text" value="' + content + '"  name="' + col.field + '" onfocus=\'PrepareDistributionModule.focusReSkuForm.bind(this)("' + col.field + '")\'   oninput=\'PrepareDistributionModule.changeReSkuForm.bind(this)("' + col.field + '")\'  onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\' placeholder="0.00">';
					html += '<i class="priceIcon">￥</i>';
					html += '</div>';
				} else {
					content = '-'
					html += '<div class="priceInputWrap n-inputWrap">';
					html += '<input class="n-layui-input priceInput"  type="text" value="' + content + '"  name="' + col.field + '" onfocus=\'PrepareDistributionModule.focusReSkuForm.bind(this)("' + col.field + '")\'   oninput=\'PrepareDistributionModule.changeReSkuForm.bind(this)("' + col.field + '")\'  onchange=\'PrepareDistributionModule.changeSkuForm.bind(this)(' + index + ',' + cIndex + ',"' + col.field + '")\' placeholder="0.00">';
					html += '<i class="priceIcon"></i>';
					html += '</div>';
				}

			}

			return html;
		}
		else
			return "";
	};

	module.InOrOutStockCount = function (isTrue, i, cI) {

		$(this).closest(".stockCountIcons").find('i').removeClass("active");
		$(this).addClass("active");
		$(this).closest(".priceInputWrap-left").find(".changeStockCountInput").val("");
		if (isTrue) {
			$(this).closest(".priceInputWrap").find(".changeStockCountInput").attr("placeholder", "加库存");
		} else {
			$(this).closest(".priceInputWrap").find(".changeStockCountInput").attr("placeholder", "减库存");
		}
		newWareHouseSkuLists.forEach(function (item, index) {
			if (i == index) {
				item.datas.forEach(function (cItem, cIndex) {
					if (cI == cIndex) {
						cItem.InOrOut = isTrue;
					}
				})
			}
		})


	}

	module.blurSkuCode = function (event) {

		event.stopPropagation();
		$(".priceInputWrap").find(".skuCode-toast").hide();
		$(this).closest(".priceInputWrap").find(".skuCode-toast").show();
	}

	module.focusReSkuForm = function (field) {
		if (field == "SettlePrice" || field == "CostPrice" || field == "DistributePrice" || field == "SalePrice" || field == "StockCount" || field == "SinglePrice" || field == "ReferencePrice") {
			$(this).closest(".priceInputWrap").removeClass("warnInput");
			$(this).closest(".priceInputWrap").find('.input-warnTitle').remove();
		}
		// if (field == "SalePrice" && $(this).val().trim() == "") {
		// 	$(this).closest(".priceInputWrap").addClass("warnInput");
		// }

	}

	module.showMoreSku = function (index) {
		$(this).toggleClass("active");
		$("#sku_tbody .chx.itemChx").each(function (i, item) {
			if ($(item).attr("data-index") == index) {
				$(item).toggleClass("hide");
			}
		});

	}

	function initAddSkuTable() {
		//for (var i = meuResultData.length - 1; i >= 0; i--) {
		//	var item = meuResultData[i];
		//	if (item.IsSelectSku && item.SelectItem.AttributeData.length > 0) {
		//		changeTableTdData.forEach(function (tbItem, tbIndex) {
		//			if (tbItem.Title == item.SelectItem.AttributeName) {
		//				changeTableTdData.splice(tbIndex, 1)
		//			}
		//		})
		//		obj = {
		//			Title: item.SelectItem.AttributeName,
		//			Name: item.SelectItem.Name,
		//			minWidth: "100px"
		//		}
		//		changeTableTdData.unshift(obj);
		//	} else {


		//	}
		//}

		//var tplt = $.templates("#table_data_temp");
		//var html = tplt.render({
		//	tableTdData: tableTdData,
		//	changeTableTdData: changeTableTdData,
		//	newWareHouseSkuLists: newWareHouseSkuLists,
		//	isShowImg: isShowImg,
		//	meuResultData: meuResultData,
		//	operateType: OperateType,

		//});
		//$("#skuTableWrap").html(html);


		//if (OperateType == "edmit") {
		//	$(".BaseProductSkuSupplierConfig").hide();
		//	$(".systemCreateCode").hide();
		//	$(".stockup_table_content th.SkuCode").append('<span style="color:#999;">(不建议修改)</span>');
		//}

		sortMenuSkuAttribute(); //规格值排序 监听事件

	}

	function combinationTable(meuResultData) {
		var combinationSkuData = [];
		meuResultData.forEach(function (item, i) {
			if (item.IsSelectSku) {
				var AttributeData = item.SelectItem.AttributeData;
				if (AttributeData.length > 0) {
					var obj = {};
					obj.AttributeName = item.SelectItem.AttributeName;
					obj.Name = item.SelectItem.Name;
					obj.AttributeValues = AttributeData;

					combinationSkuData.push(obj);
				}
			}
		})

		changeSkuResult = [];
		changeSkuResults = [];
		newWareHouseSkuLists = doSkuFormatChange(combinationSkuData, 0) //规格明细格式转化
		newWareHouseSkuLists = changeWareHouseSkuLists(newWareHouseSkuLists);  //其它字段加进去
		newWareHouseSkuLists = checkSameData(newWareHouseSkuLists, 'fristSkuName');  //其它字段加进去
		initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists);


	}

	function initSkuMeuPic() { //绑定sku添加图片事件

		var skuPicsOptions = {};
		skuPicsOptions.isMultiple = true;
		skuPicsOptions.callBack = getSkuPic;
		skuPicsOptions.limit = 1;
		skuPicsOptions.isMultiple = false;
		skuPicsOptions.businessType = businessType;
		skuPicsOptions.showLoading = true;
		skuPicsOptions.typeFlag = 3;

		$(".showSkuValue-item-down-upimg").each(function (i, item) {
			skuPicsOptions.ele = "#" + $(item).attr("id");
			var upSkuPicsOptionsMenu = new upPicFileModule();
			upSkuPicsOptionsMenu.initData(skuPicsOptions);
		})

		function getSkuPic(data) {
			var id = data[0].ele.substr(1);
			meuResultData.forEach(function (item, i) {
				if (item.SelectItem.AttributeData) {
					item.SelectItem.AttributeData.forEach(function (cItem, cI) {
						if (cItem.Id == id) {
							cItem.Img = data[0].TransitUrl;
							cItem.ImgObj = module.changePicObj(data[0]);

						}
					})
				}
			})

			renderSkuMeu(meuResultData);
			combinationTable(meuResultData);
			//initAddSkuTable();
			//initSkuMeuPic();
			setSkuTableValue();
			if (OperateType != "edmit") {
				setOldAddSkuTableValue();
			}
		}
	}

	function changeWareHouseSkuLists(newWareHouseSkuLists) {

		for (var i = 0; i < newWareHouseSkuLists.length; i++) {
			for (var j = 0; j < skuColumns.length; j++) {
				newWareHouseSkuLists[i][skuColumns[j].field] = ""
			}
		}
		return newWareHouseSkuLists;
	}



	function checkSameData(list, name) {
		var tempArr = [];
		var Data = [];
		for (var i = 0; i < list.length; i++) {
			if (tempArr.indexOf(list[i][name]) === -1) {
				Data.push({
					[name]: list[i][name],
					datas: [list[i]],
					fristSkuObj: list[i].fristSkuObj
				});
				tempArr.push(list[i][name]);
			} else {
				for (var j = 0; j < Data.length; j++) {
					if (Data[j][name] == list[i][name]) {
						Data[j].datas.push(list[i]);
						break;
					}
				}
			}
		}
		return Data;
	}


	function doSkuFormatChange(arr, index) { //转换规格明细数组格式：

		if (arr.length > 0) {
			for (var i = 0; i < arr[index].AttributeValues.length; i++) {
				changeSkuResult[index] = arr[index].AttributeValues[i];
				if (index != arr.length - 1) {
					doSkuFormatChange(arr, index + 1);
				} else {

					var newResult = [];
					for (var j = 0; j < changeSkuResult.length; j++) {
						var obj = {};
						obj.AttributeName = arr[j].AttributeName;
						obj.Name = arr[j].Name;
						obj.Value = changeSkuResult[j].Value;
						obj.Remark = changeSkuResult[j].Remark ? changeSkuResult[j].Remark : '';
						obj.OldValue = changeSkuResult[j].OldValue ? changeSkuResult[j].OldValue : '';
						obj.Img = changeSkuResult[j].Img;
						obj.ImgObj = changeSkuResult[j].ImgObj ? changeSkuResult[j].ImgObj : null;
						obj.isCustom = true;
						newResult.push(obj);
					}
					var obj = {};
					obj.SkuObj = newResult;
					obj.fristSkuObj = newResult[0];
					obj.fristSkuName = newResult[0].Value ? newResult[0].Value : newResult[0].Remark ? newResult[0].Remark : '';
					changeSkuResults.push(obj);
				}
			}
			return changeSkuResults;

		} else {
			return [];
		}
	}

	module.delAttributeValue = function (_this) { //删除规格值
		var $AttributeName = $(_this).attr("data-AttributeName");
		var $AttributeValue = $(_this).attr("data-AttributeValue");
		for (var i = 0; i < WareHouseSkuLists.length; i++) {
			for (var j = 0; j < WareHouseSkuLists[i].AttributeValues.length; j++) {
				if (WareHouseSkuLists[i].AttributeName == $AttributeName) {
					if (WareHouseSkuLists[i].AttributeValues[j] == $AttributeValue) {
						WareHouseSkuLists[i].AttributeValues.splice(j, 1); //删除规格值
						if (WareHouseSkuLists[i].AttributeValues.length == 0) {
							WareHouseSkuLists.splice(i, 1); //如果规格值为0时，该规格也删除
						}
						break;
					}
				}
			}
		}
		$(_this).parent().remove();
		initSkuTable(WareHouseSkuLists); //重新渲染sku明细表格						
	}


	function initSkuTable(WareHouseSkuLists) { //渲染sku明细表格	
		changeSkuResults = [];
		changeSkuResult = [];
		var newWareHouseSkuLists = doSkuFormatChange(WareHouseSkuLists, 0) //规格明细格式转化
		//createSkuList(newWareHouseSkuLists);
	}

	module.clickSkuPic = function (_this, Id) {
		$(_this).next().click();
	}

	module.changeSkuPic = function (_this, Id) { //添加sku图片

		var fileRead = new FileReader(); //      新建一个文件对象 读取文件信息  主要作显示用的 把flies图片转为base64位
		fileRead.readAsDataURL(_this.files[0]);
		fileRead.addEventListener("load", function () {
			var param = {};
			var imgBase64 = this.result;
			$("#pic" + Id).attr("src", imgBase64);
			$("#picWrap" + Id).show();
			$("#addSkuPic" + Id).hide();
			for (var i = 0; i < WareHouseSkus.length; i++) { //把图片放进提交数组里
				if (WareHouseSkus[i].Id == Id) {
					WareHouseSkus[i].ImageUrl = imgBase64;
					break;
				}
			}

		})
	}
	module.delSkuPic = function (Id) { //删除规格明细规格图片
		for (var i = 0; i < WareHouseSkus.length; i++) {
			if (WareHouseSkus[i].Id == Id) {
				WareHouseSkus[i].ImageUrl = "";
				$("#picWrap" + Id).hide();
				$("#addSkuPic" + Id).show();
				break;
			}

		}
	}

	module.delSkuProductPic = function (id) { //删除规格明细规格图片


		meuResultData.forEach(function (item, i) {

			item.SelectItem.AttributeData.forEach(function (cItem, cI) {
				if (cItem.Id == id) {
					cItem.Img = '';
					isConcatOld = false;
					cItem.ImgObj = null;

				}
			})

		});
		renderSkuMeu(meuResultData);
		combinationTable(meuResultData);
		initAddSkuTable();
		initSkuMeuPic();

		setSkuTableValue();


	}

	function bindInputValue() { //规格明细每个input输入框绑定事件
		$("#table_tbody_orderList input[type=text]").on("blur", function () {
			var Id = $(this).attr("data-id");
			var $fileName = $(this).attr("name");
			var $val = $(this).val().trim();
			for (var i = 0; i < WareHouseSkus.length; i++) {
				if (WareHouseSkus[i].Id == Id) {
					if (WareHouseSkus[i].Id == Id) {
						if ($fileName == "CostPrice") {
							var toValue = module.ToFixed($val, 2);
							WareHouseSkus[i][$fileName] = toValue;
							$(this).val(toValue);

						} else {
							WareHouseSkus[i][$fileName] = $val;
						}
					}
				}
			}
		})
	}

	module.switchBtn = function () {
		var isHasActive = $(this).hasClass("active");
		meuResultData.forEach(function (item, i) {
			var IsAllow = !item.IsAllow;
			item.IsAllow = IsAllow;
			if (i == 0) {
				isShowImg = !isHasActive;
			}
		})

		if (changPicMun == 1 && OperateType != "edmit") {
			isShowImg = true;
		}
		changPicMun++;

		renderSkuMeu(meuResultData);
		combinationTable(meuResultData);
		initAddSkuTable();
		initSkuMeuPic();
		setSkuTableValue();
		if (OperateType != "edmit") {
			setOldAddSkuTableValue();
		}
	}

	module.batchOperateSkuData = function (_this, fileName) {

		var $offset = $(_this).offset();
		var $SkusName = $(_this).closest(".addSkuWrap-Skus").attr("data-mapped");
		var $srollTop = document.documentElement.scrollTop;
		layer.open({
			type: 1,
			shadeClose: true,
			title: false,
			fixed: false,
			shade: false,
			anim: -1,
			isOutAnim: false,
			offset: [$offset.top - 105 - $srollTop, $offset.left],
			closeBtn: 0,
			content: '<div class="inputSkuValu-wrap"><input type="text" id="batch' + fileName +
				'"></div>',
			btn: ['确定', '取消'],
			yes: function () {
				var $value = $("#batch" + fileName).val().replace(/(^\s*) | (\s*$)/g, '');
				if ($value) {
					for (var i = 0; i < WareHouseSkus.length; i++) {
						WareHouseSkus[i][fileName] = module.ToFixed($value, 2);
					}
					initSkuTable(WareHouseSkuLists); //重新渲染sku明细表格s						
				}
				layer.closeAll();
			},
			canncle: function () { }
		});
	}

	module.edmitSkuAttributes = function (_this, Pid, id, index, oldValue, isUnity) {
		meuResultData.forEach(function (item) {
			if (item.Id == Pid) {
				item.SelectItem.AttributeData.forEach(function (cItem, cI) {
					if (cItem.Id == id) {
						cItem.OldValue = cItem.OldValue ? cItem.OldValue : cItem.Value ? cItem.Value : cItem.Remark ? cItem.Remark : '';
					}
				});
			}
		})
		var val = $(_this).val().trim();
		if (val == "") {
			layer.msg('请输入值');
			return;
		}
		var isHas = false;
		meuResultData.forEach(function (item) {
			var AttributeData = item.SelectItem.AttributeData ? item.SelectItem.AttributeData : [];
			AttributeData.forEach(function (cItem, cI) {
				if (cItem.Value == val && cI != index) {
					isHas = true;
				}
			});

		})
		if (isHas) {
			layer.msg('您输入值已存在');
			$(_this).val('')
			$(_this).closest('.n-layui-input-disabled').removeClass('n-layui-input-disabled')
			return;
		}
		meuResultData.forEach(function (item) {
			if (item.Id == Pid) {
				item.SelectItem.AttributeData.forEach(function (cItem, cI) {
					if (cItem.Id == id) {
						cItem.OldValue = cItem.OldValue ? cItem.OldValue : oldValue;
						if (!isUnity) {
							cItem.Value = val;
						} else {
							cItem.Remark = val;
						}
					}
				});
			}
		})

		renderSkuMeu(meuResultData);
		combinationTable(meuResultData);
		initAddSkuTable();
		if (OperateType == "edmit") {



		}

		//console.log("meuResultData", meuResultData)
		//console.log("newWareHouseSkuLists",newWareHouseSkuLists)
		//console.log("resultSkusresultSkus",resultSkus)

		setSkuTableValue();
	}
	var isLoadingSetSkuAttributes = false;
	module.setSkuAttributes = function (_this, Pid, id, index, sIndex) {
		if (isLoadingSetSkuAttributes) {

			return false
		}
		meuResultData.forEach(function (item) {
			if (item.Id == Pid) {
				item.SelectItem.AttributeData.forEach(function (cItem, cI) {
					if (cItem.Id == id) {
						cItem.OldValue = cItem.OldValue ? cItem.OldValue : cItem.Value;
					}
				});
			}
		})


		if (!PtProductDatas.SellPropertyJson || PtProductDatas.SellPropertyJson.length == 0) { return }
		var $this = "#" + id + 'attributes';

		var tplt = $.templates("#data_list_skuAttributes");
		index = Number(index);
		var attribute = PtProductDatas.SellPropertyJson[index];

		if (!attribute) return


		sIndex = Number(sIndex);
		// sku映射数据回显
		var isEdit = false;
		var newAttribute = PtProductDatas.AttributeTypes[index].AttributeValues[sIndex].SellPropertyValues;
		if (Array.isArray(newAttribute)) {
			newAttribute = newAttribute[0];
		}
		if (newAttribute.template_id && attribute.measure_templates.length) {
			attribute.measure_templates.forEach(function (item) {
				if (item.template_id == newAttribute.template_id) {
					newAttribute.measure_templates = attribute.measure_templates;
					if (newAttribute.value_modules && newAttribute.value_modules.length == 0) {
						newAttribute.value_modules = item.value_modules;
					}
				}
			})
		}
		if (!newAttribute.template_id && attribute.measure_templates.length) {
			attribute.measure_templates.forEach(function (item, i) {
				if (i == 0) {
					newAttribute.measure_templates = attribute.measure_templates;
					if (!newAttribute.value_modules || newAttribute.value_modules.length == 0) {
						newAttribute.value_modules = item.value_modules;
						newAttribute.template_id = item.template_id;
					}
				}
			})
		}

		//newAttribute = JSON.parse(JSON.stringify(newAttribute));
		if (newAttribute && newAttribute.value_modules && newAttribute.value_modules.length) {
			isEdit = true;
			tplt = $.templates("#data_list_skuAttributesEdit");
			if (attribute.sell_property_id == newAttribute.sell_property_id) {
				newAttribute.value_display_style = attribute.value_display_style;
				newAttribute.value_modules.forEach(function (jData, j) {
					var attributeItem = attribute.measure_templates.length && attribute.measure_templates[0].value_modules[j];
					// 多度量衡模板映射选中模板
					if (newAttribute.template_id && attribute.measure_templates.length) {
						attribute.measure_templates.forEach(function (item) {
							if (item.template_id == newAttribute.template_id) {
								attributeItem = item.value_modules[j]
							}
						})
					}
					if (attributeItem) {
						jData.units = attributeItem.units || '';
						jData.input_type = attributeItem.input_type || '';
						jData.suffix = attributeItem.suffix || '';
					}
					if (attributeItem && jData.units.length) {
						jData.units.forEach(function (kData, k) {
							if (kData.unit_id == jData.unit) {
								jData.unit_index = k;
								jData.unit_name = kData.unit_name;
								PtProductDatas.SellPropertyJson.forEach(function (item, i) {
									if (item.value_display_style == "measure" && item.measure_templates && item.measure_templates.length) {
										//newAttribute.measure_templates = item.measure_templates
										if (item.sell_property_id == newAttribute.sell_property_id) {
											newAttribute.measure_templates = item.measure_templates
										}
										item.measure_templates.forEach(function (item2, i2) {
											item2.value_modules.forEach(function (item1, i1) {
												if (item1.module_id == jData.module_id) {
													item1.unitid = kData.unit_id;
													item1.unitIdx = k;
													item1.unitName = kData.unit_name;
												}
											})
										})
									}
								})
							}
						});
					}
				})
			}
		}
		// 度量衡 - 下拉模板
		if (attribute.property_values && attribute.property_values.length) {
			isEdit = true;
			tplt = $.templates("#data_list_skuAttributesEdit");
			if (attribute.sell_property_id == newAttribute.sell_property_id) {
				newAttribute.value_display_style = attribute.value_display_style;
				newAttribute.property_values = attribute.property_values;
			}
			// 特殊分类模板：如尺码等，需要显示特殊模板下拉数据跟据单选框选择
			if (attribute.special_values && attribute.special_values.length) {
				var specialValueId = $('#addSkuWrap_Skus_ul input[name="IsSpecialSelect"]:checked').val();
				if (specialValueId) {
					var specialValue = attribute.special_values.filter(function (item) {
						return item.id == specialValueId;
					})[0];
					newAttribute.property_values = specialValue.children;
					newAttribute.special_values = attribute.special_values;
				}
			}
		}

		// 输入框是否有空值
		var inputEmpty = false;
		//if (attribute.measure_templates.length) {
		//var arr = [attribute.measure_templates[0]]
		//attribute.measure_templates = arr
		//}
		// sku二级导航数据
		var NavCateData = '';
		// 已选sku二级导航数据
		var selectNavCateData = null;
		var _newAttributes = isEdit ? newAttribute : attribute;
		if (PtProductDatas.NavCateValues && PtProductDatas.NavCateValues.length && (!_newAttributes.special_values || !_newAttributes.special_values.length)) {
			var property_id = _newAttributes.sell_property_id;
			// 度量衡下拉模板存在二级联动，需要显示二级联动数据
			PtProductDatas.NavCateValues.forEach(function (item, index) {
				if (item.PropertyId == property_id) {
					NavCateData = item.ChildNodes;
				}
			})
			NavCateData.forEach(function (item, index) {
				item.isCheck = false;
			})
		}
		
		var html = tplt.render({
			attribute: _newAttributes,
			index: index,
			navCateData: NavCateData,
		});


		meuResultData.forEach(function (item, i) {
			item.SelectItem.AttributeData.forEach(function (item1, i1) {
				if (item1.Id == id) {
					$($this).css({ display: 'block' })
				} else {
					var _this = "#" + item1.Id + 'attributes';
					$(_this).css({ display: 'none' })
				}
			})
			//console.log("-----------", 'attr' + item.Id, id, $($this))

		})
		var value_display_style = isEdit ? newAttribute.value_display_style : attribute.value_display_style;
		// 非度量衡属性的text文本输入框，不需要展示额外输入栏
		if (value_display_style !== "text") {
			var measureTemplatesHtml = ''
			if (attribute.measure_templates && attribute.measure_templates.length && attribute.measure_templates.length > 1) {

				var measureTemplatesTplt = $.templates("#data_list_skuAttributesEdit_measure_templates");
				measureTemplatesHtml = measureTemplatesTplt.render({
					attribute: isEdit ? newAttribute : attribute,
					index: index,
				});
			}
			html = measureTemplatesHtml + html
			$($this).html(html);
			$("#" + id + "input-box").addClass('n-layui-input-disabled');
			$("#" + id + "input").blur();
		} else {
			$($this).css({ display: 'none' })
		}
		//console.log("attribute")
		var unitName = null;
		var unitid = null;
		var unitids = [];
		var unitIdx = 0;
		var moduleid = '';
		$($this + " .templatesPropsItemWrap").on("click", function (event) {
			event.stopPropagation();
			$("#catePropsWrap .catePropsItem").removeClass("active");
			$(this).closest($this).addClass("active");
			$(this).closest($this).find(".n-mySelect-showContent-templates").slideToggle();
			//if ($(this).closest(".catePropsItemWrap").hasClass("noRequired")) {
			//var top = $(this).offset().top + 32;
			//$(this).closest(".catePropsItemWrap").find(".n-mySelect-showContent").css({ top: top });

			$(this).find(".n-mySelect-showContent-ul").css({ display: 'flex' });
			$("#showSkuValue_" + Pid).css({ overflowY: 'unset' });
			if (attribute.value_display_style == "measure") {
				$($this + " .n-mySelect-showContent-templates .n-mySelect-showContent-ul-li").on("click", function (event) {
					var selectValue = $(this).data('value') || '';
					var selectIndex = $(this).data('idx') || 0;

					$($this + " .templatesPropsItemWrap .n-mySelect-title").html(selectValue)
					$(this).closest($this).find(".n-mySelect-showContent-templates").hide();

					if (newAttribute.value_display_style == "measure" && newAttribute.measure_templates && newAttribute.measure_templates[selectIndex]) {
						newAttribute.value_modules = newAttribute.measure_templates[selectIndex].value_modules;
						newAttribute.template_id = newAttribute.measure_templates[selectIndex].template_id;
						newAttribute.display_name = newAttribute.measure_templates[selectIndex].display_name;
						if (PtProductDatas.SellPropertyJson[index]) {
							PtProductDatas.SellPropertyJson[index].template_id = newAttribute.measure_templates[selectIndex].template_id;
							PtProductDatas.SellPropertyJson[index].display_name = newAttribute.measure_templates[selectIndex].display_name;
						}
						$($this + " .templatesPropsItemWrap").html('<span class="n-mySelect-title-left-title ">选择规则</span><span class="n-mySelect-title-chooseItem">' + newAttribute.display_name + '</span>')
						tplt = $.templates("#data_list_skuAttributesEdit");
						var html = tplt.render({
							attribute: isEdit ? newAttribute : attribute,
							index: index,
						});
						$($this + ' .skuAttributes-warp').remove();
						$($this).append(html);
						unitsProps(selectIndex);

					}

				})
			}
		})
		// 绑定模板单位下拉
		unitsProps();
		if (attribute.value_display_style != "measure") {
			if (value_display_style !== "text") {
				$("#showSkuValue_" + Pid).css({ overflowY: 'unset' });
			}
			$($this + " .n-mySelect-property").on("click", function (event) {
				event.stopPropagation();
				$(this).closest($this).find(".n-mySelect-showContent").css({ display: 'flex' });
				var text = $($this + " .n-mySelect-title-placeholder").text();
				inputEmpty = text ? false : true;

			})
			$($this + " .n-mySelect-showContent .n-mySelect-showContent-ul-li").on("click", function (event) {
				event.stopPropagation();
				unitName = $(this).data('value') || '';
				unitid = $(this).data('unitid') || '';
				unitIdx = $(this).data('idx') || 0;

				$($this + " .n-mySelect-title-chooseItem").html(unitName)

				$($this + " .n-mySelect-showContent").css({ display: 'none' });
				inputEmpty = false;
				document.removeEventListener('click', domEvent);
				setInputFn(function (resFlag) {
					if (resFlag) {
						$("#showSkuValue_" + Pid).css({ overflowY: 'auto' })
						$($this).css({ display: 'none' });
						isLoadingSetSkuAttributes = false;
					}
				});
			})
		}
		// 添加规格值
		$($this + ' .addAttribute_addbtn').on('click', function () {
			$(this).hide();
			$($this + ' .addAttribute_box').css({ display: 'flex' });
		})
		$($this + ' .closeBtn').on('click', function () {
			$($this + ' .addAttribute_addbtn').css({ display: 'flex' });
			$($this + ' .addAttribute_box').css({ display: 'none' });
		})
		// 自定义规格值
		var customValue = '';
		$($this + ' .addAttribute_box .addBtn').on('click', function () {
			customValue = $($this + ' .addAttribute_box .layui-input').val();
			$($this + ' .addAttribute_box').css({ display: 'none' });
			document.removeEventListener('click', domEvent);
			setInputFn(function (resFlag) {
				if (resFlag) {
					$("#showSkuValue_" + Pid).css({ overflowY: 'auto' })
					$($this).css({ display: 'none' });
					isLoadingSetSkuAttributes = false;
					if (value_display_style !== "text") {
						$("#" + id + "input-box").removeClass('n-layui-input-disabled');
					}

				}
			});
		})
		var documentClick = true;

		function domEvent(e) {
			var element = document.getElementById(id + 'attributes');
			var elementInput = document.getElementById(id + 'input');
			if (!element || element.contains(e.target) || (e.target == elementInput) || !documentClick) return;

			setInputFn(function (resFlag) {
				documentClick = false
				if (resFlag) {
					$("#showSkuValue_" + Pid).css({ overflowY: 'auto' })
					isLoadingSetSkuAttributes = false;
					document.removeEventListener('click', domEvent);
					documentClick = true
					element = null;
					elementInput = null;
					$($this).css({ display: 'none' });
					if (value_display_style !== "text") {
						$("#" + id + "input-box").removeClass('n-layui-input-disabled');
					}
				}
			});
		}
		// sku映射弹窗关闭
		document.addEventListener('click', domEvent);

		function setInputFn(callback) {
			var SellPropertyValues = [];
			var isClose = true;
			var subIndex = Number(sIndex) || 0;
			var _newAttribute = isEdit ? newAttribute : attribute;
			PtProductDatas.SellPropertyJson.forEach(function (item, i) {
				if (index == i) {
					item = _newAttribute;
					var value_modules = [];
					// 完整值
					var completeValue = '';
					var obj = {
						sell_property_id: item.sell_property_id, //销售属性id 
						sell_property_name: item.sell_property_name, //销售属性名称 
						sell_property_value_id: attribute.value_display_style != "measure" ? unitid : '', //值对应的id ,
						sell_property_value_name: attribute.value_display_style != "measure" ? unitName : '', //值的名称 ,
						values: completeValue, //完整的值 ,
						remark: '', //备注的值 ,
						ext1: item.template_id || '', //扩展1 (template_id多度量衡模板),
						ext2: '', //扩展2 ,（用于保存特殊分类模板id）,
						value_modules: value_modules,
					}
					if (item.special_values && item.special_values.length > 0) {
						obj.ext2 = $('#addSkuWrap_Skus_ul input[name="IsSpecialSelect"]:checked').val();  //特殊分类radio返回值
					}
					if (item.value_display_style == "measure") {
						item.measure_templates.forEach(function (item1, i1) {
							var flag = false;
							if ((item.template_id && item1.template_id == item.template_id) || (!item.template_id && i1 == 0)) {
								var valueStr = '';
								item1.value_modules.forEach(function (item2, i2) {
									var inputIndex = 0;
									$($this + ' .n-mySelect-input-' + item2.module_id).each(function (e) {
										inputIndex = $(this).data('index');
										var val = $(this).val();
										var floatTest = /^d*(?:.d{0,2})?$/;
										var intTest = /[^\d]/;
										var valFlag = item2.validate_rule == "float" ? floatTest.test(val) : intTest.test(val);

										if (!val || val == '' || valFlag) {
											isClose = valFlag
											if (!val || val == '') {
												isClose = true;
											}

											flag = true;
											// 存在兄弟节点则对父元素添加未填提示
											if ($(this).siblings().length) {
												$(this).parent().css({ border: '1px solid #fe6f4f' })
											} else {
												$(this).css({ border: '1px solid #fe6f4f' })
											}
										} else {
											if (inputIndex === i2) {
												item2.value = val;
											}
										}

									})
									inputEmpty = flag
									valueStr += item2.value;
									var unitsItem = item2.units.length ? item2.unit_index ? item2.units[item2.unit_index] : item2.units[0] : null;
									if (item2.units.length) {
										item2.units.forEach(function (item3, i3) {
											if (unitid || (!unitid && item2.unitid)) {
												var arr = [];
												if (PtProductDatas.AttributeTypes && PtProductDatas.AttributeTypes.length && PtProductDatas.AttributeTypes[index].AttributeValues && PtProductDatas.AttributeTypes[index].AttributeValues.length) {
													arr = PtProductDatas.AttributeTypes[index].AttributeValues[sIndex].SellPropertyValues.value_modules;
													if (arr && arr.length) {
														arr.forEach(function (arrItme, arrIndex) {
															if (arrItme.module_id == item2.module_id) {
																if (item3.unit_id == item2.unitid) {
																	unitsItem = item3;
																	item2.unit_index = i3
																}
															}
														})
													} else {
														if (item.template_id && item3.unit_id == item2.unitid) {
															unitsItem = item3;
															item2.unit_index = i3
														}

													}
												} else {
													if (item3.unit_id == item2.unitid) {
														unitsItem = item3;
														item2.unit_index = i3
													}

												}
											} else {
												unitsItem = item2.units[0];
												item2.unit_index = 0
											}

										})
										valueStr += unitsItem.unit_name;
									}

									var itemObj = {
										module_id: item2.module_id,
										values: !inputEmpty ? item2.value : '',
										unit: !inputEmpty ? unitsItem ? unitsItem.unit_id : '' : '', //单位
									}
									valueStr += item2.suffix;
									value_modules.push(itemObj);
								})

								if (index == i) {
									if (!inputEmpty || customValue) {
										meuResultData.forEach(function (meuResultDataItem) {
											if (meuResultDataItem.Id == Pid) {
												meuResultDataItem.SelectItem.AttributeData.forEach(function (cItem, cI) {
													if (cItem.Id == id) {
														cItem.IsRequiredTip = false;
													}
												});
											}
										})
									}
									if (!inputEmpty && !customValue) {
										completeValue = valueStr;
										$("#" + id + 'input').val(valueStr)
										module.edmitSkuAttributes(_this, Pid, id, subIndex, completeValue);
									}
									if (customValue) {
										completeValue = customValue;
										$("#" + id + 'input').val(completeValue);
										module.edmitSkuAttributes(_this, Pid, id, subIndex, customValue);
									}

									//if (item.support_diy && inputEmpty) {
									//	var _val = $("#" + id + 'input').val();
									//	completeValue = _val;
									//	module.edmitSkuAttributes(_this, Pid, id, subIndex, _val)
									//}
								}
							}
						})
					} else if (item.value_display_style == "text") {
						//var valueStr = '';
						//$($this + ' .n-mySelect-input').each(function (e) {
						//	inputIndex = $(this).data('index');
						//	var val = $(this).val();
						//	if (!val || val == '') {
						//		inputEmpty = true;
						//	}
						//	valueStr += val;
						//})
						//var itemObj = {
						//	values: valueStr,
						//}
						//obj.value_modules.push(itemObj);

						var valueStr = $("#" + id + 'input').val();

						completeValue = valueStr;

						module.edmitSkuAttributes(_this, Pid, id, subIndex, valueStr)
					} else if (item.value_display_style == "cascader_multi_select") {
						if (index == i) {
							var isHis = false;
							if (selectNavCateData) {
								isHis = true;
								completeValue = selectNavCateData.property_value_name;
								obj.sell_property_value_id = selectNavCateData.property_value_id;
								obj.sell_property_value_name = selectNavCateData.property_value_name;
								$("#" + id + 'input').val(selectNavCateData.property_value_name);
								meuResultData.forEach(function (meuResultDataItem) {
									if (meuResultDataItem.Id == Pid) {
										meuResultDataItem.SelectItem.AttributeData.forEach(function (cItem, cI) {
											if (cItem.Id == id) {
												cItem.IsRequiredTip = false;
											}
										});
									}
								})
								module.edmitSkuAttributes(_this, Pid, id, subIndex, completeValue);
							} else {
								item.property_values.forEach(function (item1, i1) {

									if (unitid === item1.sell_property_value_id) {
										isHis = true;
										completeValue = item1.sell_property_value_name;
										obj.sell_property_value_id = item1.sell_property_value_id;
										obj.sell_property_value_name = item1.sell_property_value_name;
										$("#" + id + 'input').val(item1.sell_property_value_name);
										meuResultData.forEach(function (meuResultDataItem) {
											if (meuResultDataItem.Id == Pid) {
												meuResultDataItem.SelectItem.AttributeData.forEach(function (cItem, cI) {
													if (cItem.Id == id) {
														cItem.IsRequiredTip = false;
													}
												});
											}
										})
										module.edmitSkuAttributes(_this, Pid, id, subIndex, completeValue);

									}

								})
							}

							if (!isHis) {
								obj.sell_property_value_id = newAttribute.sell_property_value_id;
								obj.sell_property_value_name = newAttribute.sell_property_value_name;
								$("#" + id + 'input').val();
							}
							value_modules = [];
							// 输入自定义规格值,下拉属性清空
							if (customValue) {
								obj.sell_property_value_id = '';
								obj.sell_property_value_name = '';
								$("#" + id + 'input').val(customValue);
								module.edmitSkuAttributes(_this, Pid, id, subIndex, customValue);
							}
						}
					} else {
						if (index == i) {
							var isHis = false;
							item.property_values.forEach(function (item1, i1) {

								if (unitid === item1.sell_property_value_id) {
									isHis = true;
									completeValue = item1.sell_property_value_name;
									obj.sell_property_value_id = item1.sell_property_value_id;
									obj.sell_property_value_name = item1.sell_property_value_name;
									$("#" + id + 'input').val(item1.sell_property_value_name);

									meuResultData.forEach(function (meuResultDataItem) {
										if (meuResultDataItem.Id == Pid) {
											meuResultDataItem.SelectItem.AttributeData.forEach(function (cItem, cI) {
												if (cItem.Id == id) {
													cItem.IsRequiredTip = false;
												}
											});
										}
									})
									module.edmitSkuAttributes(_this, Pid, id, subIndex, completeValue);
								}

							})
							if (!isHis) {
								obj.sell_property_value_id = newAttribute.sell_property_value_id;
								obj.sell_property_value_name = newAttribute.sell_property_value_name;
								$("#" + id + 'input').val();
							}
							value_modules = [];
							// 输入自定义规格值,下拉属性清空
							if (customValue) {
								obj.sell_property_value_id = '';
								obj.sell_property_value_name = '';
								$("#" + id + 'input').val(customValue);
								module.edmitSkuAttributes(_this, Pid, id, subIndex, customValue);
							}
						}
					}


					obj.value_modules = value_modules;
					obj.values = customValue || completeValue; //完整的值 ,


					SellPropertyValues.push(obj)


					PtProductDatas.AttributeTypes.forEach(function (jData, j) {
						if (item.sell_property_id == jData.SellPropertyId && index == j) {
							jData.AttributeValues.forEach(function (kData, k) {
								var _sIndex = Number(sIndex) || 0;
								if (_sIndex == k) {
									obj.remark = $("#" + id + 'inputRemark').val() || '';
									var _SellPropertyValues = Array.isArray(jData.AttributeValues[_sIndex].SellPropertyValues) ? jData.AttributeValues[_sIndex].SellPropertyValues[0] : jData.AttributeValues[_sIndex].SellPropertyValues;

									if (SellPropertyValues) {
										_SellPropertyValues.property_values = _SellPropertyValues.property_values || ''
										jData.AttributeValues[_sIndex].SellPropertyValues = Object.assign(_SellPropertyValues, obj);
										jData.AttributeValues[_sIndex].Value = completeValue || _SellPropertyValues.values || jData.AttributeValues[_sIndex].Value;
									}

								}
							})


						}
					})
				}
			})

			if (PtProductDatas.AttributeTypes[index]) {
				//PtProductDatas.AttributeTypes[index].Attribute.SellPropertyValues = SellPropertyValues;
			}
			if (!inputEmpty || customValue) {
				if ($("#" + id + 'input').hasClass('addWarnInput')) {
					$("#" + id + 'input').removeClass('addWarnInput')
				}
			}

			showRemindTarData();
			//console.log(documentClick, "PtProductDatas===", PtProductDatas);
			callback(isClose)
		}
		isLoadingSetSkuAttributes = true;
		function unitsProps(selectIndex) {
			$($this + " .unitsPropsItemWrap").off();
			$($this + " .unitsPropsItemWrap").on("click", function (event) {
				event.stopPropagation();
				// 多度衡量
				if (selectIndex && selectIndex > 2) {
					if (_newAttributes.measure_templates && _newAttributes.measure_templates.length > 1) {
						$($this).css({ height: '450px', overflowX: 'auto' })
					} else {
						$($this).css({ height: 'auto', overflowX: 'unset' })
					}
				}
				moduleid = $(this).data('moduleid');
				$("#catePropsWrap .catePropsItem").removeClass("active");
				$(this).closest($this).addClass("active");
				$(this).closest($this).find(".n-mySelect-showContent-" + moduleid).slideToggle();
				//if ($(this).closest(".catePropsItemWrap").hasClass("noRequired")) {
				//var top = $(this).offset().top + 32;
				//$(this).closest(".catePropsItemWrap").find(".n-mySelect-showContent").css({ top: top });

				$(this).find(".n-mySelect-showContent-ul").css({ display: 'flex' });

				$("#showSkuValue_" + Pid).css({ overflow: 'unset' });
				$("#showSkuValue_" + Pid + ' .skuAttributes-warp').css({ overflow: 'unset' });


				if (attribute.value_display_style == "measure") {
					$($this + " .n-mySelect-showContent-" + moduleid + " .n-mySelect-showContent-ul-li").on("click", function (event) {
						$($this).css({ height: 'auto' });
						unitName = $(this).data('value') || '';
						unitid = $(this).data('unitid') || '';
						unitIdx = $(this).data('idx') || 0;
						unitids.push({
							unitid: unitid,
							unitIdx: unitIdx,
							unitName: unitName,
							moduleid: moduleid,
						})
						$($this + " #n-mySelect-" + moduleid + " .n-mySelect-title").html(unitName)

						$($this + " #n-mySelect-" + moduleid + " .n-mySelect-showContent").css({ display: 'none' });
						var _newAttribute = isEdit ? newAttribute : attribute;
						PtProductDatas.SellPropertyJson.forEach(function (item, i) {
							if (item.value_display_style == "measure" && item.measure_templates) {
								if (item.measure_templates && item.measure_templates.length) {
									item.measure_templates.forEach(function (templatesItem, templatesIndex) {
										if ((item.template_id && item.template_id == templatesItem.template_id) || (_newAttribute.template_id && _newAttribute.template_id == templatesItem.template_id)) {
											templatesItem.value_modules.forEach(function (item1, i1) {
												if (item1.module_id == moduleid) {
													item1.unitid = unitid;
													item1.unitIdx = i1;
													item1.unitName = unitName;
												}
											})
										}
										if (!item.template_id && templatesIndex == 0) {
											item.measure_templates[0].value_modules.forEach(function (item1, i1) {
												if (item1.module_id == moduleid) {
													item1.unitid = unitid;
													item1.unitIdx = i1;
													item1.unitName = unitName;
												}
											})
										}
									})
								}
								//item.measure_templates[0].value_modules.forEach(function (item1, i1) {
								//	if (item1.module_id == moduleid) {
								//		item1.unitid = unitid;
								//		item1.unitIdx = i1;
								//		item1.unitName = unitName;
								//	}
								//})
							}
						})
					})
				}
			})
		}
		// sku导航分类

		if (NavCateData) {
			$($this + ' #categoryMeuWrap').on('click', function () {
				document.removeEventListener('click', domEvent);
				setInputFn(function (resFlag) {
					if (resFlag) {
						$($this).css({ display: 'none' });
						isLoadingSetSkuAttributes = false;
					}
				});
			})

			$($this + ' #categoryMeuWrap .selectCategoryWrap>ul>li').on('click', function () {

				event.stopPropagation()
				var idx = $(this).data('idx') || 0;
				NavCateData.forEach(function (item, index) {
					if (idx == index) {
						item.isCheck = true;
					}
					item.isCheck = false;
				})
				$($this + ' #categoryMeuWrap .selectCategoryWrap>ul').closest("ul").find("li").removeClass("active");
				$(this).addClass('active')
				var categoryTplt = $.templates("#data_list_skuAttributesEdit_categoryMeuWrap");

				var categoryMeuWrapHtml = categoryTplt.render({ navCateData: NavCateData[idx].LowerLevelAttrData });
				$($this + ' #selectCategory_level').html(categoryMeuWrapHtml);
				$($this + ' #selectCategory_level .selectCategoryWrap>ul>li').off();
				$($this + ' #selectCategory_level .selectCategoryWrap>ul>li').on('click', function () {
					event.stopPropagation()
					var idx_level = $(this).data('idx') || 0;
					selectNavCateData = NavCateData[idx].LowerLevelAttrData[idx_level];

					document.removeEventListener('click', domEvent);

					setInputFn(function (resFlag) {
						if (resFlag) {
							$($this).css({ display: 'none' });
							isLoadingSetSkuAttributes = false;
						}
					});
				})
			})
		}

		showRemindTarData();
	}
	module.inputSkuAttributes = function (_this, Pid, id, index, sIndex) {

		PtProductDatas.SellPropertyJson.forEach(function (item, i) {
			PtProductDatas.AttributeTypes.forEach(function (jData, j) {
				if (index == i) {
					var remark = $("#" + id + 'inputRemark').val() || '';
					if (remark) {
						meuResultData.forEach(function (item) {
							if (item.Id == Pid) {
								item.SelectItem.AttributeData.forEach(function (cItem, cI) {

									if (cItem.Id == id) {

										cItem.Remark = remark;

									}
								});
							}
						})
						renderSkuMeu(meuResultData);
						combinationTable(meuResultData);
						initAddSkuTable();
						setSkuTableValue();

					} else {
						meuResultData.forEach(function (item) {
							if (item.Id == Pid) {
								item.SelectItem.AttributeData.forEach(function (cItem, cI) {
									if (cItem.Id == id) {

										cItem.Remark = '';

									}
								});
							}
						})
					}
					var _sIndex = Number(sIndex) || 0;
					if (item.sell_property_id == jData.SellPropertyId) {
						jData.AttributeValues.forEach(function (kData, k) {
							if (k == _sIndex)
								if (jData.AttributeValues[_sIndex].length && !jData.AttributeValues[_sIndex].SellPropertyValues) {
									jData.AttributeValues[_sIndex].SellPropertyValues = {}
								}

							jData.AttributeValues[_sIndex].SellPropertyValues.remark = remark;



						})
					}

				}

			})

		})


		//showRemindTarData();//渲染填写提示
	}
	module.inputSkuValue = function (_this, value, IsRequiredTip, index) {
		//console.log("PtProductDatas.SellPropertyJson===", PtProductDatas.SellPropertyJson[index])
		var value_display_style = '';
		if (PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length && PtProductDatas.SellPropertyJson[index]) {
			value_display_style = PtProductDatas.SellPropertyJson[index].value_display_style;
		}
		var _value = IsRequiredTip == 'true' ? '' : value;
		if (value_display_style == 'text') {
			_value = $(_this).val();
		}
		$(_this).val(_value)
	}

	module.delSkuAttributesValue = function (_this, Pid, id) {
		meuResultData.forEach(function (item) {
			if (item.Id == Pid) {
				item.SelectItem.AttributeData.forEach(function (cItem, cI) {
					if (cItem.Id == id) {
						item.SelectItem.AttributeData.splice(cI, 1);
					}
				});
			}
		})
		renderSkuMeu(meuResultData);
		combinationTable(meuResultData);
		setSkuTableValue(resultSkus);
		if (OperateType != "edmit") {
			setOldAddSkuTableValue();
		} else {
			rawResultSkus.forEach(function (item, i) {
				item.isCheck = false;
			});
		}

	}


	function checkSubmitData() {
		var isTrue = true;
		if (!checkSubmitFormat("ProductSubject")) {
			isTrue = false;
		}
		if (!checkSubmitFormat("productPic", "选择商品主图")) {
			isTrue = false;
		}
		if (!checkSubmitFormat("ProductCargoNumber")) {
			isTrue = false;
		}
		if (!checkSubmitFormat("ProductSku", "请添加规格")) {
			isTrue = false;
		}
		return isTrue;
	}

	function checkSubmitFormat(name, titleTar) { //查询提交数据是否符合				
		var isTrue = true;
		var titleTar = titleTar ? titleTar : "请输入";
		var $this = $("input[name='" + name + "']");
		var spanEle = '<span class="noInputData warnTar">' + titleTar + '</span>';

		if (name == "ProductSubject" || name == "ProductCargoNumber") {
			var $val = $this.val().replace(/(^\s*) | (\s*$)/g, '');
			if (!$val) {
				$this.nextAll(".noInputData").remove();
				$this.addClass("activeBorderColor");
				$this.after(spanEle);
				isTrue = false;
			}
		} else if (name == "productPic") {
			if ($("#productPicShow .productPic-list").length < 1) {
				$this.nextAll(".noInputData").remove();
				$this.after(spanEle);
				isTrue = false;
			}
		} else if (name == "ProductSku") {
			if ($("#table_tbody_orderList>tr").length < 1) {
				$(".addSkuBtn").children(".noInputData").remove();
				var spanEleSku = '<span class="noInputData warnTar" id="noSkuTar">' + titleTar + '</span>';
				$(".addSkuWrap").addClass("activeBorderColor");
				$(".addSkuBtn").append(spanEleSku);
				isTrue = false;
			} else {
				$("#table_tbody_orderList input[name=SkuCargoNumber]").each(function (index,
					item) { //检查是否有SKU编码值
					var $value = $(item).val();
					if (!$value.replace(/(^\s*) | (\s*$)/g, '')) {
						$(item).after(spanEle);
						$(item).addClass("activeBorderColor");
						isTrue = false;
					}
				})

			}

		}
		return isTrue;
	}

	module.clearWarnTitle = function () { //绑定所有input[type=text] 输入事件  去除警告框和文字 
		$(".layui-mywrap input[type=text]").each(function (index, item) {
			$(item).on("input", function () { //监听输入框输入文字时，警告框和提示语删除
				$(this).removeClass("activeBorderColor").nextAll(".noInputData").remove();
			})

		})

	}
	// 删除单独sku图片
	module.delCommonSkuProductPic = function (index,cIndex) {

		newWareHouseSkuLists[index].datas.forEach(function (item, i) {
			if (cIndex == i) {
				item.ImageUrl = "";
				item.ImageUrlStr = "";
				item.ImageId = 0;
			}
		})
		var isHasRawResultSkus = rawResultSkus.length > 0 ? true : false;
		initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists, isHasRawResultSkus);
	}

	module.ToFixed = function (val, count) { //转化价格的格式
		var val = val;
		if (val != 0) {
			val = parseFloat(val) ? parseFloat(val) : "";
		}
		if (val) {
			var changenum = (parseInt(val * Math.pow(10, count) + 0.5) / Math.pow(10, count)).toString();
			index = changenum.indexOf(".");
			if (index < 0 && count > 0) {
				changenum = changenum + ".";
				for (i = 0; i < count; i++) {
					changenum = changenum + "0";
				}

			} else {
				index = changenum.length - index;
				for (i = 0; i < (count - index) + 1; i++) {
					changenum = changenum + "0";
				}
			}
			return changenum;
		} else {
			return val;
		}
	}
	var timeFn = null;


	module.createProductOuterId = function () {

		commonModule.Ajax({
			url: "/BaseProduct/CodeGenerate",
			type: "GET",
			loading: true,
			async: true,
			data: {},
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					$("input[name=productOuterId]").val(rsp.Data.Code);
					module.checkSpuCode();
					showRemindTarData();

				}
			}
		});
	}

	module.createSingleSkuId = function (index, cIndex) {
		var that = this;
		commonModule.Ajax({
			url: "/BaseProduct/CodeGenerate",
			type: "GET",
			loading: true,
			async: true,
			data: {},
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					$(that).closest(".priceInputWrap").find('input[name=SkuCode]').val(rsp.Data.Code);

					newWareHouseSkuLists.forEach(function (item, i) {
						if (i == index) {
							item.datas.forEach(function (cItem, cI) {
								if (cI == cIndex) {
									cItem.SkuCode = rsp.Data.Code;
								}
							})
						}
					})

					showRemindTarData();
				}
			}
		});


	}

	module.createSkuOuterId = function (isNull) {

		var $newWareHouseSkuLists = JSON.parse(JSON.stringify(newWareHouseSkuLists))
		if ($newWareHouseSkuLists.length == 0) {
			layer.msg("请先设置SKU");
			return;
		}
		var promiseSelectPicArry = [];
		for (var i = 0; i < newWareHouseSkuLists.length; i++) {
			for (var j = 0; j < $newWareHouseSkuLists[i].length; j++) {
				if ($newWareHouseSkuLists[i][j].AttributeName == "SkuCode") {
					var PromiseObj = new Promise(function (resolve, reject) {
						commonModule.Ajax({
							url: "/BaseProduct/CodeGenerate",
							type: "GET",
							loading: true,
							async: true,
							data: {},
							success: function (rsp) {
								if (commonModule.IsError(rsp)) {
									reject(commonModule.IsError(rsp));
									return;
								}
								if (rsp.Success) {
									//$("input[name=productOuterId]").val(rsp.Data.Code);
									if (isNull) {
										resolve('');

									} else {
										resolve(rsp.Data.Code);

									}
								}
							}
						});
					})
					promiseSelectPicArry.push(PromiseObj)
					break;
				}
			}
		}
		Promise.all(promiseSelectPicArry).then(function (resultData) {
			for (var ii = 0; ii < newWareHouseSkuLists.length; ii++) {
				for (var jj = 0; jj < $newWareHouseSkuLists[ii].length; jj++) {
					if ($newWareHouseSkuLists[ii][jj].AttributeName == "SkuCode") {
						$newWareHouseSkuLists[ii][jj].Value = resultData[ii]
						break;
					}
				}
			}
			newWareHouseSkuLists = $newWareHouseSkuLists;
			initAddSkuTable();
		})

	}

	module.changeSkuForm = function (index, cIndex, attributeName) {

		var val = $(this).val().trim();
		var that = this;
		if (attributeName == "StockCount" || attributeName == "ChangeStockCount") {
			var re = /^\d+$/;
			if (re.test(val) === false) {
				$(this).closest(".priceInputWrap").addClass("warnInput");
				$(this).after('<div class="input-warnTitle">请输入</div>');
				$(this).val("");
				val = "";
			}
		}

		if (attributeName == "ChangeStockCount") {


			var status = $(this).closest(".priceInputWrap-left").find(".stockCountIcons i.active").data("status");
			if (!status) {
				var stockCount = $(this).closest(".priceInputWrap").find(".edmitStockCountText").text();
				if ((val - 0) > (stockCount - 0)) {
					commonModule.w_alert({ type: 2, content: '库存数量不能小于0!' });
					$(this).val("");
					val = "";
				}

			}

		}

		if (attributeName == "CostPrice" || attributeName == "SettlePrice" || attributeName == "DistributePrice" || attributeName == "ReferencePrice") {
			var moneyreg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/; //金额正则
			if (!moneyreg.test(val) && val != "") {
				//commonModule.w_alert({ type: 2, content: '价格格式错误!' });
				$(this).closest(".priceInputWrap").addClass("warnInput");
				$(this).after('<div class="input-warnTitle">格式不对</div>');
				$(this).val("");
				val = "";
			}
		}

		if (attributeName == "SalePrice" || attributeName == "SinglePrice") {
			var moneyreg02 = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/; //金额正则
			var isTip = false;
			val = parseFloat(val); 
			if (!moneyreg02.test(val) && val != "") {
				//commonModule.w_alert({ type: 2, content: '价格格式错误!' });
				$(this).closest(".priceInputWrap").addClass("warnInput");
				isTip = true;
				$(this).val("");
				val = "";
			}
			if (val == 0) {
				$(this).closest(".priceInputWrap").addClass("warnInput");
				isTip = true;
				$(this).val("");
				val = "";
			}
			if (isTip) {
				$(this).after('<div class="input-warnTitle">格式不对,大于0</div>');
			}

		}


		if (attributeName == "SkuCode") {
			$(this).removeClass("addWarnInput");
		}

		if (OperateType == "edmit") {  //修改数据
			newWareHouseSkuLists.forEach(function (item, i) {
				if (i == index) {
					item.datas.forEach(function (cItem, cI) {
						if (cI == cIndex) {
							if (attributeName == "SkuCode") {
								cItem.IsNewSkuCode = false;
							}
						}
					})
				}
			})
		}

		if (attributeName == "ShortTitle") {

			if (getByteLen(val) > 20) {
				val = val.substr(0, 10);
				$(this).val(val);
				$(this).closest(".priceInputWrap").find(".titleLength").text(getByteLen(val));
			}
		}
		var activeAttributesName = "";
		var $that = $(this);
		// 最大单买价
		var maxSinglePrice = 0;
		newWareHouseSkuLists.forEach(function (item, i) {
			if (i == index) {
				item.datas.forEach(function (cItem, cI) {
					if (cI == cIndex) {
						cItem[attributeName] = val;
						if (attributeName == "ChangeStockCount") {
							var stockStatus = $(that).closest(".priceInputWrap").find("i.active").data("status");
							cItem.InOrOut = stockStatus;
						}
						if (attributeName == "SinglePrice") {
							if (!cItem.SinglePrice || val - cItem.SalePrice < 1) {
								$that.closest(".priceInputWrap").find('.input-warnTitle').remove();
								$that.closest(".priceInputWrap").addClass("warnInput");
								$that.val("");
								cItem[attributeName] = "";
								$that.after('<div class="input-warnTitle">至少比拼单价高1元</div>');
							}
						}
						activeAttributesName = cItem.AttributesName
					}
				})
			}
			item.datas.forEach(function (cItem, cI) {
				if (cItem.SinglePrice > maxSinglePrice) {
					maxSinglePrice = cItem.SinglePrice;
				}
			})
		})
		var referencePrice = $('.referencePriceInputWrap input[name="ReferencePrice"]').val();
		// 默认自动填充：按单sku最高单买价+1自动填充。用户可修改。
		if (referencePrice && attributeName == "ReferencePrice") {
			referencePrice = parseFloat(referencePrice);
			var referencePriceTip = ''; //参考价提示
			if ( referencePrice > maxSinglePrice * 5) {
				referencePriceTip = '参考价不得高于最高规格单买价的5倍: '+maxSinglePrice * 5+'元';
			}
			if (!referencePrice ||referencePrice <= maxSinglePrice) {
				referencePriceTip ='参考价应大于商品最大单买价'
			}
			if (referencePriceTip) {
				$('.referencePriceInputWrap .priceInputWrap.n-inputWrap').addClass("warnInput");
				$('.referencePriceInputWrap input[name="ReferencePrice"]').val();
				$('.referencePriceInputWrap input[name="ReferencePrice"]').after('<div class="input-warnTitle">'+referencePriceTip+'</div>');
			}
		} else {
			if (!referencePrice || referencePrice <= maxSinglePrice) {
				var newReferencePrice = maxSinglePrice + 1;
				$('.referencePriceInputWrap input[name="ReferencePrice"]').val(newReferencePrice);
				$('input[name="ReferencePrice"]').closest(".priceInputWrap").find('.input-warnTitle').remove();
				$('input[name="ReferencePrice"]').closest(".priceInputWrap").removeClass("warnInput");
			}
		}
		showRemindTarData();
		oldWareHouseSkuLists = JSON.parse(JSON.stringify(newWareHouseSkuLists));

		resultSkus.forEach(function (item, index) {
			if (item.AttributesName == activeAttributesName) {
				item[attributeName] = val;
			}
		})

	}
	module.changeReSkuForm = function (attributeName) {

		if (attributeName == "CostPrice" || attributeName == "SettlePrice" || attributeName == "DistributePrice" || attributeName == "StockCount"|| attributeName == "SinglePrice") {

			$(this).closest(".priceInputWrap").removeClass("warnInput");
			$(this).closest(".priceInputWrap").find('.input-warnTitle').remove();
		}

	}
	module.changeSkuCodeForm = function (index, attributeName) {
		var val = $(this).val().trim();
		var oldskucode = $(this).attr('data-oldskucode');
		var type = $(this).attr("data-type");
		var that = this;
		if (val == "") {
			layer.msg("请输入值！");
			return;
		}
		if (type == "edmit" && index == 0) {

			var html = '';
			html += '<div class="checkCodeWarn">';
			html += '<div class="checkCodeWarn-item">.SKU编码已和其他同款商品建立关联关系，是否使用新编码覆盖原编码，继续关联关系?</div>';
			html += '<div class="checkCodeWarn-item">·选择不覆盖仅会新生成新SKU商品，原编码继续使用。</div>';
			html += '</div>';
			var checkCodeDailog = layer.open({
				type: 1,
				title: '重要提示', //不显示标题
				content: html,
				area: '650px', //宽高
				closeBtn: 1,
				btn: ['不覆盖，生成新商品编码', '覆盖原编码，使用新编码关联'],
				skin: 'checkCodeWarnSkin',
				yes: function () {
					IsNewSkuCode = true;
					var addObj = null;
					newWareHouseSkuLists.forEach(function (item, i) {
						if (i == index) {
							addObj = JSON.parse(JSON.stringify(item));
							addObj.forEach(function (cItem, cI) {
								if (cItem.AttributeName == attributeName) {
									cItem.Value = val;
									cItem.IsNewSkuCode = IsNewSkuCode;
								}
							})
						}
					})

					//if (newWareHouseSkuLists.length == 2) {
					//	newWareHouseSkuLists.splice(1,1)
					//}

					newWareHouseSkuLists.push(addObj);
					initAddSkuTable();
					layer.close(checkCodeDailog);

				},
				btn2: function () {
					IsNewSkuCode = false;
					newWareHouseSkuLists.forEach(function (item, i) {
						if (i == index) {
							item.forEach(function (cItem, cI) {
								if (cItem.AttributeName == attributeName) {
									cItem.Value = val;
									cItem.IsNewSkuCode = IsNewSkuCode;
								}
							})
						}
					})
				},
				cancel: function () {
					$(that).val(oldskucode);
				}
			});

		} else {
			newWareHouseSkuLists.forEach(function (item, i) {
				if (i == index) {
					item.forEach(function (cItem, cI) {
						if (cItem.AttributeName == attributeName) {
							cItem.Value = val;

						}
					})
				}
			})
		}

	}


	module.changePicObj = function (obj, isUrl, id) {

		var resultObj = {};

		if (!isUrl) {   //新添加规格图片
			var ImageUrl = obj.ImageUrl;
			var picName = ImageUrl.substr(ImageUrl.lastIndexOf("/") + 1, ImageUrl.length);
			picName = picName.substr(0, picName.lastIndexOf("."));
			var Domain = getDomainWithProtocol(ImageUrl);
			resultObj.Domain = Domain;
			var url = ImageUrl.substr(ImageUrl.indexOf(Domain) + Domain.length + 1);
			resultObj.Url = url.substr(0, url.lastIndexOf("/"));
			resultObj.Suffix = obj.ImageUrl.substr(ImageUrl.lastIndexOf(".") + 1);
			resultObj.ImageObjectId = 0;
			resultObj.Name = picName;
			resultObj.ShowUrl = ImageUrl;
		} else {


			if (obj) {

				/*				var Domain = getDomainWithProtocol(obj);*/
				resultObj.Domain = obj;

				//var url = obj.substr(obj.indexOf(Domain) + Domain.length + 1);
				//resultObj.Url = url.substr(0, url.lastIndexOf("/"));

				resultObj.Suffix = obj.substr(obj.lastIndexOf(".") + 1);
				resultObj.ImageObjectId = id ? id : 0;
				resultObj.ShowUrl = obj;
				var picName = obj.substr(obj.lastIndexOf("/") + 1, obj.length);
				picName = picName.substr(0, picName.lastIndexOf("."));
				resultObj.Name = picName;

			}

		}



		return resultObj;
	}
	//获取域名带协议的
	function getDomainWithProtocol(url) {
		var regex = /^(?:https?:\/\/)?(?:www\.)?([^\/:\n\r]+)/;
		var match = url.match(regex);
		return match ? match[0] : null;
	}


	//绑定厂家
	var selectDailogSupplierName = '';
	var initSupplierSelectBox = function (ele, isRadio, selectData) {
		selectDailogSupplierName = '';
		var selectboxArr = [];
		for (var i = 0; i < Suppliers.length; i++) {
			var obj = {};
			obj.Value = Suppliers[i].FxUserId;
			obj.Text = Suppliers[i].UserName;
			if (Suppliers[i].IsTop != undefined && Suppliers[i].IsTop) {
				obj.Text = '<i class="iconfont icon-zhuangtai zhidingIcon"></i>' + Suppliers[i].UserName;
			}
			//只取IsFilter=false
			if (Suppliers[i].IsFilter == undefined || Suppliers[i].IsFilter == false) {
				selectboxArr.push(obj);
			}
		}

		var selectInit = {
			eles: '#' + ele,
			emptyTitle: '请选择厂家', //设置没有选择属性时，出现的标题
			data: selectboxArr,
			searchType: 1, //1出现搜索框，不设置不出现搜索框
			showWidth: '250px', //显示下拉的宽
			isRadio: isRadio, //有设置，下拉框改为单选
			allSelect: false,
			selectData: selectData ? selectData : []  //初始化数据
		};

		var selectBox = new selectBoxModule2();
		selectBox.initData(selectInit);
		selectBox.selectCallBack = function (obj) {
			selectDailogSupplierName = obj[0].Text;
		}

	}

	module.BatchMappingSupplier = function (index) {

		var BatchMappingSupplierDailog = layer.open({
			type: 1,
			title: "绑定厂家", //不显示标题
			content: $('#batchMappingSupplier'),
			area: '580', //宽高
			maxHeight: 500,
			btn: ['保存', '取消'],
			skin: 'batchMappingSupplier-skin',
			success: function () {
				initSupplierSelectBox("supplier_name_select", true);
				$("#select_supplier #selectMore_choose").on("click", function () {
					$(".batchMappingSupplier-skin .layui-layer-content").animate({ scrollTop: 1000 }, 100);
				});

				$("input[name=supplier-type-rdo]").prop('checked', false);
				$("input[name=self-config-rdo]").prop('checked', false);
				$("input[name=supplier-config-rdo]").prop('checked', false);

				var baseProductSkuSupplierConfig = [];
				newWareHouseSkuLists.forEach(function (item, i) {
					if (i == index) {
						item.forEach(function (cItem, cI) {
							if (cItem.AttributeName == 'BaseProductSkuSupplierConfig') {
								baseProductSkuSupplierConfig = cItem.Value;
							}
						})
					}
				})
				if (baseProductSkuSupplierConfig.length > 0) {
					if (baseProductSkuSupplierConfig[0].IsSelf) {
						$("input[name=supplier-type-rdo][value=self]").trigger("click");
						$("input[name=self-config-rdo][value=" + baseProductSkuSupplierConfig[0].ApplyScope + "]").prop("checked", true);

					} else {
						$("input[name=supplier-type-rdo][value=supplier]").trigger("click");
						$("input[name=supplier-config-rdo][value=" + baseProductSkuSupplierConfig[0].ApplyScope + "]").prop("checked", true);

						var selectData = [{ Value: baseProductSkuSupplierConfig[0].ApplyScope, Text: baseProductSkuSupplierConfig[0].SupplierFxUserName }];
						initSupplierSelectBox("supplier_name_select", true, selectData);

					}
				}
			},
			yes: function () {

				var supplyType = $("input[name=supplier-type-rdo]:checked").val();
				var applyScope = "";
				var supplierFxUserId = "";
				var SupplierFxUserName = "";
				if (supplyType == undefined || supplyType == "") {
					layer.msg("请选择供货类型");
					return;
				}

				if (supplyType == 'self') {
					applyScope = $("input[name=self-config-rdo]:checked").val();
				} else if (supplyType == 'supplier') {
					applyScope = $("input[name=supplier-config-rdo]:checked").val();
					supplierFxUserId = $(".batchMappingSupplier-skin #supplier_name_select").attr("data-values");
					SupplierFxUserName = $(".batchMappingSupplier-skin #supplier_name_select .selectMore-choose-title").text();

					if (!supplierFxUserId) {
						layer.msg("请选择厂家");
						return;
					}

				}

				if (applyScope == undefined || applyScope == "") {
					layer.msg("请选择已关联的同款商品代发订单推送规则");
					return;
				}

				var BaseProductSkuSupplierConfig = [];
				var configObj = {};
				configObj.SupplierFxUserId = supplierFxUserId;
				configObj.SupplierFxUserName = SupplierFxUserName;
				//configObj.Config = "";
				configObj.ConfigType = 0;
				configObj.ApplyScope = applyScope;
				configObj.IsSelf = (supplyType == 'self' ? true : false);
				BaseProductSkuSupplierConfig.push(configObj);

				newWareHouseSkuLists.forEach(function (item, i) {
					if (i == index) {
						item.forEach(function (cItem, cI) {
							if (cItem.AttributeName == 'BaseProductSkuSupplierConfig') {
								cItem.Value = BaseProductSkuSupplierConfig;
							}
						})
					}
				})
				initAddSkuTable();
				layer.close(BatchMappingSupplierDailog);
				initAddSkuTable();
			},
			btn2: function () { },
			cancel: function () { }
		});

	}

	module.ChangeSupplierType = function () {

		var val = $('input[name=supplier-type-rdo]:checked').val();
		$(".bindsupplier-main-ul").hide();
		$("#ul_" + val).css({
			display: 'flex'
		});
		$(".select-supplier").hide();
		$("#select_" + val).css({
			display: 'flex'
		});
	}

	// 操作从已创建商品选择--------------------------------------------------------

	var baseProductsOptions = {
		PageIndex: 1,
		PageSize: 10,
		ShortTitle: '',
		SpuCode: '',
		Subject: '',
	};
	var baseProductsRows = [];
	var baseProductsRowsHtml = "";
	var baseProductsSelectItem = {}; //从已创建商品选择编辑商品
	module.GetBaseProducts = function (isPaging) {

		baseProductsOptions.Subject = $('input[name=inputProductNameId]').val().trim();
		baseProductsOptions.ShortTitle = $('input[name=shortTitleName]').val().trim();
		baseProductsOptions.SpuCode = $('input[name=productCargoNumber]').val().trim();
		if (!isPaging) {
			baseProductsOptions.PageIndex = 1;
			baseProductsOptions.PageSize = 10;
		}

		commonModule.Ajax({
			url: "/BaseProduct/GetBaseProducts",
			type: "POST",
			loading: true,
			data: { model: baseProductsOptions },
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					baseProductsRows = rsp.Data.Rows;
					var count = rsp.Data.Total;
					var tplt = $.templates("#tb_data_list_getproductlist")
					baseProductsRowsHtml = tplt.render({
						products: baseProductsRows,
					});
					$("#table_tbody_productList").html(baseProductsRowsHtml);

					if (isPaging == true) {
						return;
					}
					layui.laypage.render({
						elem: 'paging',
						count: count,
						limit: baseProductsOptions.PageSize,
						curr: baseProductsOptions.PageIndex,
						limits: [10, 100, 200, 300, 400, 500],
						layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
						jump: function (obj, first) {
							if (!first) {
								baseProductsOptions.PageIndex = obj.curr;
								module.GetBaseProducts(true);
							}
						}
					});
				}
			}
		});
	}



	module.ResetCondition = function () {

		$('input[name=inputProductNameId]').val("");
		$('input[name=shortTitleName]').val("");
		$('input[name=productCargoNumber]').val("");
	}

	module.GetBaseProductDetail = function (baseProductUid, FxUserId, FromCode, PlatformType, FromType) {
		UrlFromType = FromType;
		var obj = {
			ProductUid: baseProductUid ? baseProductUid : '',
			PlatformType: PlatformType || setPlatformTypeName,
			FromType: FromType,  //0: 根据基础商品Uid、1：根据平台资料code、2：根据自己货盘、3：根据厂商货盘 4：铺货任务
			FxUserId: FxUserId || '',  //当前用户id  fromType 3：根据厂商货盘传入
			FromCode: FromCode,    // 平台资料code 草稿返回
			//uid: baseProductUid

		};
		var ShopsIds = [];
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				ShopsIds.push(item.ShopId);
			}
		})
		obj.ShopIds = ShopsIds;

		//if (baseProductSkuUid) {
		//	obj.BaseProductSkuUid = baseProductSkuUid;
		//	obj.Type = true;
		//}
		var url = "/api/PtProductInfo/PtProductDatas";//到时要换掉 
		commonModule.Ajax({
			url: url,
			type: "POST",
			loading: true,
			data: obj,
			success: function (rsp) {

				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					SourceSku = JSON.parse(JSON.stringify(rsp.Data.ProductInfoSkus));
					PtProductDatas = JSON.parse(JSON.stringify(rsp.Data));
					if (PtProductDatas.SellPropertyJson) {
						PtProductDatas.SellPropertyJson = JSON.parse(PtProductDatas.SellPropertyJson);

						// sku规格属性模板转换为抖音通用结构
						onSellPropertyJsonFormat();

						var newSellPropertyJson = [];
						// sku规格属性值回显
						PtProductDatas.AttributeTypes.forEach(function (jData, j) {
							if (jData.SellPropertyJson) {
								jData.SellPropertyJson = JSON.parse(jData.SellPropertyJson);
							}
							PtProductDatas.SellPropertyJson.forEach(function (item, i) {
								if (item.sell_property_id == jData.SellPropertyId) {
									newSellPropertyJson.push(item);
									var SellPropertyValuesId = null;
									jData.AttributeValues.forEach(function (kData, k) {

										var obj = {
											support_remark: item.support_remark,
											support_diy: item.support_diy,
											sell_property_id: item.sell_property_id || "", //销售属性id 
											sell_property_name: item.sell_property_name || "", //销售属性名称 
											sell_property_value_id: '', //值对应的id ,
											sell_property_value_name: '', //值的名称 ,
											values: kData.Value || '', //完整的值 ,
											sourceValues: kData.OldValue || '', // 源sku值
											remark: item.remark, //备注的值 ,
											value_modules: [],
											template_id: '',
										}
										if (kData.SellPropertyValues && kData.SellPropertyValues.length) {
											obj.sell_property_value_id = kData.SellPropertyValues[0].sell_property_value_id; //值对应的id ,
											obj.sell_property_value_name = kData.SellPropertyValues[0].sell_property_value_name;
											obj.value_modules = kData.SellPropertyValues[0].value_modules;
											obj.remark = kData.SellPropertyValues[0].remark;
											obj.template_id = item.measure_templates && item.measure_templates.length == 1 ? item.measure_templates[0].template_id : (kData.SellPropertyValues[0].ext1 || '');
											SellPropertyValuesId = kData.SellPropertyValues[0].ext2;
										}
										kData.SellPropertyValues = obj
									})
									// 特殊分类模板 - radio选中
									if (SellPropertyValuesId) {
										item.special_values.forEach(function (specialItem,specialIndex){
											specialItem.isCheck = false;
											if (specialItem.id == SellPropertyValuesId){
												specialItem.isCheck = true;
											}
										})
									}
								}

							})

						})
						PtProductDatas.SellPropertyJson = newSellPropertyJson.length ? newSellPropertyJson : PtProductDatas.SellPropertyJson;
						rsp.Data.SellPropertyJson = PtProductDatas.SellPropertyJson;
					}

					// sku规格属性值 - 分类导航数据
					var NavCateValues = PtProductDatas.NavCateValues;
					if (NavCateValues && NavCateValues.length) {
						NavCateValues.forEach(function (item, index) {
							if (item.ChildNodes && item.ChildNodes.length) {
								item.ChildNodes.forEach(function (item2, index2) {
									item2.LowerLevelAttrData = JSON.parse(item2.LowerLevelAttrData)
								})
							}
						})
					}


					setBaseProductInfo(rsp.Data);
					// console.log("PtProductDatas===", PtProductDatas)
					//console.log("rsprsp", rsp)
				}
			}
		});

	}
	module.GetTranBaseProductSku = function () {
		if (!PtProductDatas.CategoryId) {
			layer.msg("请选择分类")
			return false;
		}
		var obj = {
			productUid: PtProductDatas.FromBaseProductUid,
			categoryId: PtProductDatas.CategoryId,
			platformTtype: setPlatformTypeName,
		};
		commonModule.Ajax({
			url: '/api/PtProductInfo/GetTranBaseProductSku',
			type: "GET",
			loading: true,
			data: obj,
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {
					$('.isUpdateTip').hide();
					PtProductDatas = Object.assign(PtProductDatas, rsp.Data);
					PtProductDatas.IsWaitSyncBaseProduct = false;
					if (PtProductDatas.SellPropertyJson) {
						PtProductDatas.SellPropertyJson = JSON.parse(PtProductDatas.SellPropertyJson);
						var newSellPropertyJson = [];
						PtProductDatas.AttributeTypes.forEach(function (jData, j) {
							if (jData.SellPropertyJson) {
								jData.SellPropertyJson = JSON.parse(jData.SellPropertyJson);
							}
							PtProductDatas.SellPropertyJson.forEach(function (item, i) {

								if (item.sell_property_id == jData.SellPropertyId) {
									newSellPropertyJson.push(item)
									jData.AttributeValues.forEach(function (kData, k) {

										var obj = {
											support_remark: item.support_remark,
											support_diy: item.support_diy,
											sell_property_id: item.sell_property_id || "", //销售属性id 
											sell_property_name: item.sell_property_name || "", //销售属性名称 
											sell_property_value_id: '', //值对应的id ,
											sell_property_value_name: '', //值的名称 ,
											values: kData.Value || '', //完整的值 ,
											remark: jData.remark, //备注的值 ,
											value_modules: [],
										}
										if (kData.SellPropertyValues && kData.SellPropertyValues.length) {
											obj.sell_property_value_id = kData.SellPropertyValues[0].sell_property_value_id; //值对应的id ,
											obj.sell_property_value_name = kData.SellPropertyValues[0].sell_property_value_name;
											obj.value_modules = kData.SellPropertyValues[0].value_modules;
											obj.remark = kData.SellPropertyValues[0].remark || obj.remark;
										}
										kData.SellPropertyValues = obj
									})
								}
							})

						})
						PtProductDatas.SellPropertyJson = newSellPropertyJson.length ? newSellPropertyJson : PtProductDatas.SellPropertyJson;
					}
					PtProductDatas.IsWaitSyncBaseProduct = false;
					isChangeCategoryData = true;
					isLoadingSetSkuAttributes = false;
					setBaseProductInfo(PtProductDatas, 2)
					//console.log("rsprsp", rsp)
				}
			}
		});
	}

	setBaseProductInfo = function (resultObj, type) {
		SkuModeType = resultObj.SkuModeType ? resultObj.SkuModeType : 0;

		UniqueCode = resultObj.UniqueCode;
		SupplierProductUid = resultObj.SupplierProductUid;
		SupplierProductUidStr = resultObj.SupplierProductUidStr;
		SpuShortTitle = resultObj.ShortTitle;
		SpuAttributeTypes = JSON.parse(JSON.stringify(resultObj.AttributeTypes));
		BaseProductUid = resultObj.BaseProductUid;
		GetCreateFrom = resultObj.CreateFrom;
		GetFromFxUserId = resultObj.FromFxUserId;
		FromBaseProductUid = resultObj.FromBaseProductUid;
		FromSupplierProductUid = resultObj.FromSupplierProductUid;
		GetSpuCode = resultObj.SpuCode;
		GetCategoryId = resultObj.CategoryId;
		IsPublic = resultObj.IsPublic;
		RootNodeFxUserId = resultObj.RootNodeFxUserId;
		SharePathCode = resultObj.SharePathCode;
		PathNodeDeep = resultObj.PathNodeDeep;
		GetFxUserId = resultObj.FxUserId;
		GetFromType = resultObj.FromType;
		GetFromCode = resultObj.FromCode;
		IsWaitSyncBaseProduct = resultObj.IsWaitSyncBaseProduct;
		IsSkuChange = resultObj.IsSkuChange;
		getAttributeTypes = resultObj.AttributeTypes;
		ListingConfig = resultObj.ListingConfig;

		if (type != 2) {
			UserListingSetting = resultObj.UserListingSetting;
			if (!UserListingSetting || UserListingSetting == null || JSON.stringify(UserListingSetting) == "{}") {
				UserListingSetting = {
					Id: 1,
					IsDefault: false,
					PlatformType: setPlatformTypeName,
					ListingSettingValue: {
						ProductStatus: 2,
						Delivery: {
							DeliveryMode: 1,
							DeliveryTime: 3
						},
						HonourAgreement: {
							ExpressTemplateGroupCode: "",
							AftersalesPolicy: 1
						},
						WarehouseCalcRule: 1
					},
					ListingLogisticsTemplateGroupCode: null,
					ServiceMobile: null
				}

			}

			setUserListingSetting(UserListingSetting);//渲染铺货设置
		}

		if (TargetShopId) {
			ListingShopIds = resultObj.ListingShopIds ? resultObj.ListingShopIds : [];
		}

		if (isBatchPuhuo) {
			IsSyncPtProductInfo = resultObj.IsSyncPtProductInfo;
			BatchShopCount = resultObj.ShopCount;
			IsSyncPtProductInfoButton = resultObj.IsSyncPtProductInfoButton;;
		}

		getTemplates();


		var IsPublic = 0;
		if (IsPublic == resultObj.IsPublic == 0 ? resultObj.IsPublic : 1) {
			$("input[name=IsPublic]:eq(1)").attr("checked", 'checked');
		} else {
			$("input[name=IsPublic]:eq(0)").attr("checked", 'checked');
		}



		$("#productNameShow").text(resultObj.Subject);
		$("input[name=productFullName]").val(resultObj.Subject);
		$("input[name=productOuterId]").val(resultObj.SpuCode);
		$("input[name=productShortName]").val(resultObj.ShortTitle);
		$("#productShortNameLen").text(getByteLen(resultObj.ShortTitle));
		$("#productFullNameLen").text(getByteLen(resultObj.Subject));
		OldproductOuterId = resultObj.SpuCode;
		if (OperateType == "copy") {  //来自基础商品编辑
			$("#productNameShow").text("创建商品");
			$("#productOuterId").val("");
		}


		initCategoryMenu(resultObj.CategoryInfoList);//初始代类目
		CatePathList = resultObj.CategoryInfoList;
		AutoCategoryInfos = resultObj.AutoCategoryInfos
		if (AutoCategoryInfos && AutoCategoryInfos.length) {
			var _AutoCategoryInfos = newFormatCategoryMenu(AutoCategoryInfos);
			$('#intelligence-categoryMeu .intelligence-categoryMeuInfo').html(_AutoCategoryInfos);
			if (!_AutoCategoryInfos) {
				$('#intelligence-categoryMeu').hide();
			} else {
				$('#intelligence-categoryMeu').css({ display: 'flex' });
			}
			var AutoCategoryInfosCategoryId = AutoCategoryInfos[AutoCategoryInfos.length - 1].CateId;
			if (resultObj.CategoryId == AutoCategoryInfosCategoryId) {
				$('#intelligence-categoryMeu').hide();
			}
		}

		if (resultObj.ShopCount > 0) {
			$('#categoryMeu-warnTitle').css({ display: 'flex' })
			$('#categoryMeu-warnTitle #categoryMeu-warnTitle-count').text(resultObj.ShopCount)
		}
		if (OperateType == "edmit") {
			rewAttributeTypes = JSON.parse(JSON.stringify(resultObj.AttributeTypes));
			//CateProps = JSON.parse(CateProps);	
		}


		//console.log("resultObj.CategoryAttribute===", resultObj.CategoryAttribute)
		if (resultObj.CategoryAttribute && !Array.isArray(resultObj.CategoryAttribute)) {  //渲染目录属性
			resultObj.CategoryAttribute = JSON.parse(resultObj.CategoryAttribute);

			getCategoryInfoList = resultObj.CategoryInfoList || [];
			CateProps = resultObj.CategoryAttribute;
			if (getCategoryInfoList.length > 0) {
				initCateProps();
			}
			autoClickGetPtCascadeProperti(CateProps);//自动获取属性联动下级


		}
		// 参考价赋值
		if (resultObj.ReferencePrice) {
			$('input[name="ReferencePrice"]').val(resultObj.ReferencePrice);
		}
		productImages = [];
		productDetailsPics = [];

		//var ImagesStr = resultObj.ImagesStr ? resultObj.ImagesStr:[];
		//var ImagesKeyStr = resultObj.ImagesKeyStr ? resultObj.ImagesKeyStr : [];

		var Images = resultObj.ProductImages ? resultObj.ProductImages : [];



		if (Images != null || Images.length != 0) {
			Images.forEach(function (item, i) {
				var obj = {};
				item.TransitUrl = item.ImageUrl;
				productImages.push(item);
			})
		}

		//var indexImg = null;
		//productImages.forEach(function (item, i) {
		//	if (item.IsMain) {
		//		indexImg = i
		//	}
		//});
		//if (indexImg != null) {
		//	var changImgObj = productImages.splice(indexImg, 1)[0]; // 从数组中移除该项并获得它
		//	productImages.unshift(changImgObj); // 将该项插入到数组的开头
		//}

		showRemindTarData();//渲染填写提示
		renderProductImages();//渲染主图

		var DescriptionsStr = resultObj.DescriptionStr;
		//var DescriptionsKeyStr = resultObj.DescriptionsKeyStr;

		if (DescriptionsStr != null) {
			DescriptionsStr.forEach(function (item, i) {
				var obj = {};
				if (item.indexOf('/Common/GetImageFile?objectKey=') == -1) {
					obj.TransitUrl = '/Common/GetImageFile?objectKey=' + item;
				} else {
					obj.TransitUrl = item;
				}
				obj.ImageUrl = item;
				obj.Id = new Date().getTime();
				productDetailsPics.push(obj);
			})
		}

		module.getProductDetailsPic([], true);//渲染详情图
		OldSpuCode = resultObj.SpuCode;
		setWareHouseSkuAttributeNameData(resultObj.AttributeTypes);

		function setWareHouseSkuAttributeNameData(AttributeTypes) {
			var newWareHouseSkuAttributeNameData = [];
			var _WareHouseSkuAttributeNameData = [];  // 规格类型下拉框数据
			if (AttributeTypes.length > 0) {
				AttributeTypes.forEach(function (item, i) {
					var AttributeName = item.AttributeName;
					var AttributeData = [];
					var special_values = []; //特殊模版数据
					if (PtProductDatas.SellPropertyJson) {
						PtProductDatas.SellPropertyJson.forEach(function (cItem, cI){
							// 特殊单选tab联级分类
							if (cItem.special_values && cItem.special_values.length && item.SellPropertyId == cItem.sell_property_id)  {
								special_values = cItem.special_values;
							}
						})
					}
					item.AttributeValues.forEach(function (cItem, cI) {

						if (i == 0 && cItem.ValueUrl != '') {
							isShowImg = true;
						}
						var ValueUrl = cItem.ValueUrl ? cItem.ValueUrl : '';
						var id = "attr" + new Date().getTime() + Math.floor(Math.random() * (1000)) + cI;
						var IsRequiredTip = cItem.IsRequiredTip;
						var Remark = cItem.SellPropertyValues ? Array.isArray(cItem.SellPropertyValues) ? cItem.SellPropertyValues[0].remark : cItem.SellPropertyValues.remark : '';
						var cObj = {
							Id: id,
							Img: i == 0 ? ValueUrl : '',
							ImgObj: i == 0 ? module.changePicObj(cItem.ValueUrlKey, true, cItem.ImageObjectId) : null,
							//Value: !isChangeCategoryData ? cItem.Value : IsRequiredTip ? '' : cItem.Value,
							Value: cItem.Value,
							sourceValues: cItem.OldValue,
							ImageObjectId: cItem.ImageObjectId,
							support_diy: item.SupportDiy,
							support_remark: item.SupportRemark,
							Remark: item.Remark || Remark,
							IsRequiredTip: IsRequiredTip
						}
						AttributeData.push(cObj);

					})
					var newAttributeName = AttributeName == "颜色" ? "color" : AttributeName == "尺寸" ? "size" : "cuscustom" + i;
					// 拼多多规格下拉
					if (PtProductDatas.SellPropertyCateProp && PtProductDatas.SellPropertyCateProp.length) {
						PtProductDatas.SellPropertyCateProp.forEach(function (item, i){
							var CatePropobj = {
								AttributeData: AttributeData,
								AttributeName: item.parent_spec_name,
								Id: item.parent_spec_id,
								Name: AttributeName,
								Value: "",
								cat_id: item.cat_id,
								isCheck: true,
								isSelect:false,
								isUnity: PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length ? true : false,
								special_values: special_values,
							}
							_WareHouseSkuAttributeNameData.push(CatePropobj);
						})
						newAttributeName = item.AttributeName
					}
					var obj = {
						AttributeData: AttributeData,
						AttributeName: newAttributeName,
						Id: i + 1,
						Name: AttributeName,
						Value: "",
						isCheck: true,
						isUnity: PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length ? true : false,
						special_values: special_values,
					}
					item.SelectItem = obj;
					newWareHouseSkuAttributeNameData.push(obj);
				})
			}

			var customObj = {
				Id: new Date().getTime(),
				AttributeName: "custom",
				Name: '自定义',
				isCheck: false,
				Value: '',
				AttributeData: []
			}
			newWareHouseSkuAttributeNameData.push(customObj);
			WareHouseSkuAttributeNameData = _WareHouseSkuAttributeNameData && _WareHouseSkuAttributeNameData.length ? _WareHouseSkuAttributeNameData : newWareHouseSkuAttributeNameData;
			meuResultData = [];
			AttributeTypes.forEach(function (item, i) {
				initSkuSelect(1, item, i)
			})

			addSkuBtnHideOrShow();
			renderSkuMeu(meuResultData);
			combinationTable(meuResultData);
			resultSkus = resultObj.ProductInfoSkus;

			setSkuTableValue();
			initSkuMeuPic();

			if (OperateType != "edmit") {  //不是编辑商品，重新生成
				module.createSkuOuterId(true);
				$("input[name=productOuterId]").val("");
				//module.createProductOuterId();
			}

			showRemindTarData();
			sortMenuSkuAttribute();

		}
		EdmitRewValueSorts = getEdmitRewValueSorts();
	}
	module.useCategory = function () {
		$(".categoryMeuItem").removeClass("formWarn");
		isChangeCategoryData = true;
		initCategoryMenu(AutoCategoryInfos);//初始代类目
		CatePathList = AutoCategoryInfos
		getCateProps();//获取类目属性
		showRemindTarData();
		$('#intelligence-categoryMeu').hide();
	}
	function newFormatCategoryMenu(data) {

		var title = '';
		for (var i = 0; i < data.length; i++) {

			if (i == 0) {
				title += data[i].Name;
			} else {
				title += " > " + data[i].Name;
			}
		}
		return title;
	}

	function newFormatSearchTitle(obj, newArray) {
		if (obj == null) {
			return;
		}
		//var newObj = {};
		//newObj.Id = obj.Id;
		//newObj.Name = obj.Name;
		newArray.push(obj);
		if (obj.Child == null || obj.Child.length == 0) {
			return;
		}
		for (var i = 0; i < obj.Child.length; i++) {
			newFormatSearchTitle(obj.Child[i], newArray);
		}

	}

	function setUserListingSetting(UserListingSetting) {   //铺货设置赋值

		if (!UserListingSetting || UserListingSetting == null || JSON.stringify(UserListingSetting) == "{}") {

			UserListingSetting = {
				Id: 1,
				IsDefault: false,
				PlatformType: setPlatformTypeName,
				ListingSettingValue: {
					ProductStatus: 2,
					Delivery: {
						DeliveryMode: 1,
						DeliveryTime: 3
					},
					HonourAgreement: {
						ExpressTemplateGroupCode: "",  //"c08356ddbd1d6b04"
						AftersalesPolicy: 1
					},
					WarehouseCalcRule: 1
				},
				ListingLogisticsTemplateGroupCode: null,
				ServiceMobile: null
			}

		}


		if (!UserListingSetting.IsDefault && UserListingSetting.ListingSettingValue == null) {

			UserListingSetting.ListingSettingValue = {
				ProductStatus: 2,
				Delivery: {
					DeliveryMode: 1,
					DeliveryTime: 3
				},
				HonourAgreement: {
					ExpressTemplateGroupCode: "",  //"c08356ddbd1d6b04"
					AftersalesPolicy: 1
				},
				WarehouseCalcRule: 1
			}
		}


		if (batchEdmitStepNum == 3) {
			if (!UserListingSetting.BatchSettingValue || UserListingSetting.BatchSettingValue == null) {
				UserListingSetting.BatchSettingValue = {
					ProductStatus: 2,
					Delivery: {
						DeliveryMode: 1,
						DeliveryTime: 3
					},
					HonourAgreement: {
						ExpressTemplateGroupCode: "",  //"c08356ddbd1d6b04"
						AftersalesPolicy: 1
					},
					WarehouseCalcRule: 1
				};
			}
		}


		//完成铺货的商品状态
		var ListingSettingValue = null;
		if (batchEdmitStepNum == 3) {

			ListingSettingValue = UserListingSetting.BatchSettingValue;

		} else {
			ListingSettingValue = UserListingSetting.ListingSettingValue;
		}

		var ProductStatus = ListingSettingValue.ProductStatus;
		if (ProductStatus) {
			var productStatusTitle = ProductStatus == 1 ? '立即上架' : ProductStatus == 2 ? '放入仓库' : ProductStatus == 3 ? '放入草稿箱' : '';
			$("#productStatus_set").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(productStatusTitle);
			$("#productStatus_set .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
				var value = $(item).attr("data-value");
				$(item).removeClass("activeItem");
				if (value == ProductStatus) {
					$(item).addClass("activeItem");
				}
			});
		}

		//发货
		var Delivery = ListingSettingValue.Delivery;
		var DeliveryTime = Delivery.DeliveryTime;
		if (Delivery.DeliveryTime) {
			var DeliveryTimeTitle = DeliveryTime == 1 ? '当日发' : DeliveryTime == 2 ? '次日发' : DeliveryTime == 3 ? '48小时内发货' : '';
			$("#deliveryTime_set").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(DeliveryTimeTitle);
			$("#deliveryTime_set .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
				var value = $(item).attr("data-value");
				$(item).removeClass("activeItem");
				if (value == DeliveryTime) {
					$(item).addClass("activeItem");
				}
			});
		}


		//库存计数
		var WarehouseCalcRule = ListingSettingValue.WarehouseCalcRule;
		if (WarehouseCalcRule) {
			var WarehouseCalcRuleTitle = WarehouseCalcRule == 1 ? '下单减库存' : WarehouseCalcRule == 2 ? '付款减库存' : '';
			$("#warehouseCalcRule_set").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(WarehouseCalcRuleTitle);
			$("#warehouseCalcRule_set .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
				var value = $(item).attr("data-value");
				$(item).removeClass("activeItem");
				if (value == WarehouseCalcRule) {
					$(item).addClass("activeItem");
				}
			});
		}
		// 承诺
		var BatchSettingValue = UserListingSetting.BatchSettingValue
        if (BatchSettingValue && BatchSettingValue.ReturnPolicyDays) {
            var checkboxArr = ['ReturnPolicyDays','FakeOneCompensateTen','DamageCoverage'];
            for (var i = 0; i < checkboxArr.length; i++) {
                var isCheck = BatchSettingValue[checkboxArr[i]];
                $("#setPrepareDistribution span[name="+checkboxArr[i]+"]").removeClass("activeF");
                if (isCheck) {
                    $("#setPrepareDistribution span[name="+checkboxArr[i]+"]").addClass("activeF");
                }
            }
        }


		//履约
		var HonourAgreement = ListingSettingValue.HonourAgreement;
		var ExpressTemplateGroupCode = HonourAgreement.ExpressTemplateGroupCode;

		var isHasExpressTemplateGroupCode = false;
		if (ExpressTemplateGroupCode) {

			edmitTemplateObj.forEach(function (item) {
				if (ExpressTemplateGroupCode == item.UniqueCode && item.IsSelect) {
					$("#freightTemplate_ul .n-mySelect-showContent-ul-li").each(function (index, item) {

						var uniquecode = $(item).attr("data-uniquecode");
						if (uniquecode == ExpressTemplateGroupCode) {

							$(item).trigger("click");
							$(item).addClass("activeItem");

						}
					})
					isHasExpressTemplateGroupCode = true;
				}
			})
		}
		if (!isHasExpressTemplateGroupCode) {
			ExpressTemplateGroupCode = "";
		}

		if (UserListingSetting.IsDefault) {
			$("#IsDefault_set").addClass("activeF")
		} else {
			$("#IsDefault_set").removeClass("activeF")
		}

		showRemindTarData();
	}

	changeDefaultSet = function () {
		$(this).toggleClass("activeF");
		var isDefault = $(this).hasClass("activeF");
		UserListingSetting.IsDefault = isDefault;
	}

	selectPrepareDistribution = function (type, value) {

		var text = $(this).html();
		$(this).closest(".n-mySelect").find('.n-mySelect-showContent-ul-li').removeClass('activeItem');
		$(this).addClass('activeItem').closest(".n-mySelect").addClass("hasActive").find('.n-mySelect-title-chooseItem').html(text);

		var ListingSettingValue = null;
		if (batchEdmitStepNum == 3) {
			ListingSettingValue = UserListingSetting.BatchSettingValue;
		} else {
			ListingSettingValue = UserListingSetting.ListingSettingValue;
		}


		if (type == "ProductStatus" || type == "WarehouseCalcRule") {
			ListingSettingValue[type] = value;
		}
		if (type == "DeliveryTime") {
			ListingSettingValue.Delivery.DeliveryTime = value;
		}
		if (type == "DeliveryTime") {
			ListingSettingValue.Delivery.DeliveryTime = value;
		}
	}

	function setSkuTableValue() {  //编辑给sku赋值

		resultSkus.forEach(function (item, i) {
			if (!item.AttributesName) {
				var $Attributes = JSON.parse(item.Attributes);
				if (Array.isArray($Attributes)) {
					item.Attributes = $Attributes
				} else {
					var newArray = [];
					newArray.push($Attributes);
					item.Attributes = newArray;
				}

				item.AttributesName = "";

				$Attributes.forEach(function (cItem, cI) {
					item.AttributesName += cItem.v + "·"
				})

			}
		})

		newWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				cItem.AttributesName = "";
				cItem.OriginalStockCount = "";
				cItem.SkuObj.forEach(function (ccItem, ccI) {
					if (ccItem.OldValue) {
						cItem.AttributesName += ccItem.OldValue + "·";
					} else {

						if (ccItem.Value) {
							cItem.AttributesName += ccItem.Value + "·";
						} else if (ccItem.Remark) {

							cItem.AttributesName += ccItem.Remark + "·";
						}


					}
				})
			})
		})

		newWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				//cItem.isNew = true;
				resultSkus.forEach(function (rItem, rI) {
					if (cItem.AttributesName == rItem.AttributesName) {
						cItem.isNew = false;
						cItem.SkuUid = rItem.UidStr ? rItem.UidStr : '';
						cItem.OldSkuCode = rItem.SkuCode ? rItem.SkuCode : '';
						cItem.ChangeStockCount = rItem.ChangeStockCount ? rItem.ChangeStockCount : '';
						cItem.FromBaseProductSkuUid = rItem.FromBaseProductSkuUid;
						cItem.FromSupplierProductSkuUid = rItem.FromSupplierProductSkuUid;
						cItem.Subject = rItem.Subject;
						cItem.Attributes = rItem.Attributes ? JSON.stringify(rItem.Attributes) : '';
						cItem.ImageObjectId = rItem.ImageObjectId;
						cItem.OtherJson = rItem.OtherJson;
						cItem.RootNodeFxUserId = rItem.RootNodeFxUserId;
						cItem.SharePathCode = rItem.SharePathCode;
						cItem.PathNodeDeep = rItem.PathNodeDeep;
						cItem.ImageUrl = rItem.ImageUrl;
						cItem.SinglePrice = rItem.SinglePrice;

						cItem.IsDefaultPadding = rItem.IsDefaultPadding ? rItem.IsDefaultPadding : false;
						cItem.OriginalStockCount = rItem.StockCount == 0 ? 0 : rItem.StockCount ? rItem.StockCount : '';

						for (var k in cItem) {
							switch (k) {
								case "ShortTitle":
									cItem[k] = rItem.ShortTitle ? rItem.ShortTitle : '';
									break;
								case "CostPrice":
									cItem[k] = rItem.CostPrice == 0 ? 0 : rItem.CostPrice ? rItem.CostPrice : '';
									break;
								case "SettlePrice":
									cItem[k] = rItem.SettlePrice == 0 ? 0 : rItem.SettlePrice ? rItem.SettlePrice : '';
									break;
								case "DistributePrice":
									cItem[k] = rItem.DistributePrice == 0 ? 0 : rItem.DistributePrice ? rItem.DistributePrice : '';
									break;
								case "StockCount":
									cItem[k] = rItem.StockCount == 0 ? 0 : rItem.StockCount ? rItem.StockCount : '';

									break;
								case "SalePrice":
									cItem[k] = rItem.SalePrice ? rItem.SalePrice : '';
									break;
								case "SkuCode":
									if (OperateType == "edmit") {
										cItem[k] = rItem.SkuCode ? rItem.SkuCode : '';
									}
									break;
								default:
									break;
							}
						}
					}
				})
			})
		});
		//console.log("newWareHouseSkuLists====", newWareHouseSkuLists)

		rawResultSkus = []
		newWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				resultSkus.forEach(function (rItem, rI) {
					rItem.isHas = false;
					if (cItem.AttributesName == rItem.AttributesName) {
						rItem.isHas = true;
					}
				})
			})
		});

		resultSkus.forEach(function (rItem, rI) {
			rItem.isHas = false;
			newWareHouseSkuLists.forEach(function (item, i) {
				item.datas.forEach(function (cItem, cI) {
					if (cItem.AttributesName == rItem.AttributesName) {
						rItem.isHas = true;
					}
				})
			});

		})
		resultSkus.forEach(function (ccitem, i) {  //获取被删除的
			if (!ccitem.isHas) {
				rawResultSkus.push(ccitem);
			}
		})
		var isHasRawResultSkus = rawResultSkus.length > 0 ? true : false;
		initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists, isHasRawResultSkus);

	}


	module.matchedMoreSku = function (i, ci) {

		//document.body.style.overflow = 'hidden';//禁用滚动条
		event.stopPropagation();
		$(".rawSkusWrap").remove();
		var isCheck = false;
		rawResultSkus.forEach(function (item, i) {
			if (item.isCheck) {
				isCheck = true;
			}
		});
		var tplt = $.templates("#rawResultSkus_render");
		var html = tplt.render({
			rawResultSkus: rawResultSkus,
			isCheck: isCheck,
			index: i,
			cIndex: ci
		});
		$(this).append(html);
		var offsetObj = $(this).offset();
		var top = offsetObj.top - document.documentElement.scrollTop;
		$(this).find(".rawSkusWrap").css({ left: offsetObj.left, top: top });
	}

	module.delMatchedMoreSku = function (index, cIndex) {
		event.stopPropagation();
		rawResultSkus.forEach(function (item, i) {
			if (item.checkNum == "num" + index + cIndex) {
				item.isCheck = false;
				item.checkNum = "";
			}
		});
		$(this).closest(".matchedMoreSku").removeClass("active");
		$(this).closest(".matchedMoreSku-title-wrap").css({ display: 'inline-block' });


		newWareHouseSkuLists.forEach(function (item, i) {

			if (i == index) {
				item.datas.forEach(function (cItem, cI) {
					if (cIndex == cI) {
						cItem.SettlePrice = '';
						cItem.CostPrice = '';
						cItem.DistributePrice = '';
						cItem.StockCount = '';
						cItem.SkuCode = '';
						cItem.ShortTitle = '';
						cItem.IsNewSkuCode = true;

					}
				})
			}
		});

		$(this).closest("tr.chx").find("input[name=SettlePrice]").val('');
		$(this).closest("tr.chx").find("input[name=CostPrice]").val('');
		$(this).closest("tr.chx").find("input[name=DistributePrice]").val('');
		$(this).closest("tr.chx").find("input[name=SkuCode]").val('');
		$(this).closest("tr.chx").find("input[name=ShortTitle]").val('');
		$(this).closest("tr.chx").find(".edmitStockCountText").text('');
		$(this).closest("tr.chx").find(".titleLength").text('0');

	}

	module.chooseRawResultSku = function (id, index, cIndex) {
		event.stopPropagation();
		document.body.style.overflow = '';//启用滚动条

		var checkObj = {};

		rawResultSkus.forEach(function (item, i) {
			if (item.checkNum == "num" + index + cIndex) {
				item.isCheck = false;
				item.checkNum = "";
			}
		});

		rawResultSkus.forEach(function (item, i) {
			if (item.Id == id) {
				item.isCheck = true;
				checkObj = item;
				item.checkNum = "num" + index + cIndex;
			}
		});


		newWareHouseSkuLists.forEach(function (item, i) {
			if (i == index) {
				item.datas.forEach(function (cItem, cI) {
					if (cIndex == cI) {
						cItem.SettlePrice = checkObj.SettlePrice;
						cItem.CostPrice = checkObj.CostPrice;
						cItem.DistributePrice = checkObj.DistributePrice;
						cItem.StockCount = checkObj.StockCount;
						cItem.SkuCode = checkObj.SkuCode;
						cItem.ShortTitle = checkObj.ShortTitle;
						cItem.SkuUid = checkObj.UidStr;
						cItem.IsNewSkuCode = null;
						cItem.isNew = false;
					}
				})
			}
		});

		$(this).closest(".matchedMoreSku").addClass("active").find(".matchedMoreSku-title2").text('原：' + checkObj.AttributesName.substr(0, checkObj.AttributesName.length - 1));
		$(this).closest("tr.chx").find("input[name=SettlePrice]").val(checkObj.SettlePrice);
		$(this).closest("tr.chx").find("input[name=CostPrice]").val(checkObj.CostPrice);
		$(this).closest("tr.chx").find("input[name=DistributePrice]").val(checkObj.DistributePrice);
		$(this).closest("tr.chx").find("input[name=SkuCode]").val(checkObj.SkuCode);
		$(this).closest("tr.chx").find(".edmitStockCountText").text(checkObj.StockCount);
		$(this).closest("tr.chx").find("input[name=ShortTitle]").val(checkObj.ShortTitle);
		$(this).closest("tr.chx").find(".titleLength").text(checkObj.ShortTitle.length);
		$(this).closest(".rawSkusWrap").remove();

	}

	module.checkWareHouseStatus = function () {

	}

	module.checkShortTitle = function () {
		var val = $(this).val().trim();
		$(this).closest(".priceInputWrap").find(".titleLength").text(getByteLen(val));
	}

	//新建仓库
	module.CreateWareHouseStatus = function () {

		layer.open({
			type: 1,
			title: "新建仓库",
			content: $('.createStoreDialog'),
			area: ["600px"],
			btn: ['确定', '取消'],
			success: function () {
				commonModule.LoadAreaInfoToControl('province-select', 1, function () { }, selectCallBack, "name");
				$("#warehouseName-input").val("");
				$("#address-textarea").val("");
				$("#contact-input").val("");
				$("#mobile-input").val("");
				$("#telphone-input").val("");
			},
			yes: function () {
				//检测输入框
				var checkInput = module.checkNewCreateBtnWrap();
				if (!checkInput) {
					return;
				}
				var _requestModel = {
					WarehouseName: $.trim($("#warehouseName-input").val()),
					Province: $("#province-select").val(),
					City: $("#city-select").val(),
					County: $("#county-select").val(),
					Address: $.trim($("#address-textarea").val()),
					Contact: $.trim($("#contact-input").val()),
					Mobile: $.trim($("#mobile-input").val()),
					Telphone: $.trim($("#telphone-input").val())
				};
				commonModule.Ajax({
					url: "/StockControl/AddStoreManagement",
					type: "POST",
					loading: true,
					async: false,
					data: { requestModel: _requestModel },
					success: function (rsp) {
						if (commonModule.IsError(rsp)) {
							return;
						}
						layer.closeAll();
						layer.msg('创建成功！');
						WareHouseStatus = 1;
					}
				});
			},
			cancel: function () {
				layer.closeAll();
			}
		});
	}

	//私有方法-省市区联动相关
	function selectCallBack(control) {
		var deep = control.attr('deep');
		if (deep > 1) {
			var dataValue = control.attr("data-value");
			var isExistsVal = control.find("option[value='" + dataValue + "']").length;
			if (isExistsVal > 0)
				control.val(dataValue).trigger('change');
		}
	};

	//校验编辑新建仓库输入框
	module.checkNewCreateBtnWrap = function () {
		var $warehouseName = $('#warehouseName-input').val().trim();
		var $contact = $('#contact-input').val().trim();
		var $mobile = $('#mobile-input').val().trim();
		var $telphone = $('#telphone-input').val().trim();
		var $province_select = $('#province-select').val();
		var $city_select = $('#city-select').val();
		var $county_select = $('#county-select').val();
		var $address_textarea = $('#address-textarea').val().trim();
		var isTrue = true;
		if ($warehouseName == '') {
			layer.msg('请填写仓库名称.');
			return false;
		}
		if ($contact == '') {
			layer.msg('请填写联系人姓名.');
			return false;
		}
		if ($mobile == '' && $telphone == '') {
			layer.msg('手机号码和固定电话至少填写一个.');
			return false;
		}


		if ($province_select == "0") {
			layer.msg('请选择省份.');
			return false;
		}
		if ($city_select == "0") {
			layer.msg('请选择城市.');
			return false;
		}
		if ($county_select == "0") {
			layer.msg('请选择区域.');
			return false;
		}
		if ($address_textarea == '') {
			layer.msg('请填写详细地址.');
			return false;
		}
		return isTrue;

	}

	module.DefaultAddressChange = function (_this) {
		var state = $(_this).prop('checked');
		if (state) {
			commonModule.Ajax({
				url: '/StockControl/LoadAddress',
				data: {},
				async: false,
				loading: true,
				type: 'POST',
				success: function (rsp) {
					if (commonModule.IsError(rsp)) {
						return;
					}
					if (rsp.Data != null) {
						$("#contact-input").val(rsp.Data.SenderName);
						$("#mobile-input").val(rsp.Data.SenderMobile);
						$("#telphone-input").val(rsp.Data.SenderTelePhone);

						$("#city-select").attr("data-value", rsp.Data.City).val(rsp.Data.City);
						$("#county-select").attr("data-value", rsp.Data.County).val(rsp.Data.County);
						$("#province-select").attr("data-value", rsp.Data.Province).val(rsp.Data.Province).change();
						$("#address-textarea").val(rsp.Data.Address);
					}
				}
			});
		}
	}

	module.checkSpuCode = function () {
		if (OperateType == "edmit") {
			var that = this;
			var html = '';
			html += '<div class="checkCodeWarn">';
			html += '<div class="checkCodeWarn-item">新编码将会覆盖所有历史已关联的商品信息,请确认是否修改 ?</div>';
			//html += '<div class="checkCodeWarn-item">·选择不覆盖仅会新生成商品，原编码继续使用。</div>';
			html += '</div>';
			var checkCodeDailog = layer.open({
				type: 1,
				title: '重要提示', //不显示标题
				content: html,
				area: '600px', //宽高
				closeBtn: 1,
				btn: ['取消', '确定'],
				skin: 'checkCodeWarnSkin n-skin',
				yes: function (index) {
					//IsNewSpuCode = false;
					$("input[name=productOuterId]").val(OldproductOuterId);
					layer.close(checkCodeDailog);
				},
				btn2: function () {
					IsNewSpuCode = true;

					layer.close(checkCodeDailog);
				},
				cancel: function () {
					$(that).val(OldSpuCode);
				}

			});
		}
	}




	//手动入出库
	module.setWareHouseNum = function (isTrue, index) {
		$(this).closest('.edmitStockCountWrap').find('.wareHouseNum').css({ display: 'flex' });
		$(this).closest('.edmitStockCountWrap').find(".wareHouseNum-input").hide();
		if (isTrue) {
			$(this).closest('.edmitStockCountWrap').find(".wareHoustyle").text("+");
			$(this).closest('.edmitStockCountWrap').find(".wareHouseTitle").text("入库").css({ color: "#99cc00" });
			$(this).closest('.edmitStockCountWrap').find("input[name=EntryIn]").css({ display: 'inline-block' });
		} else {
			$(this).closest('.edmitStockCountWrap').find(".wareHoustyle").text("-");
			$(this).closest('.edmitStockCountWrap').find(".wareHouseTitle").text("出库").css({ color: "#f29a1a" });
			$(this).closest('.edmitStockCountWrap').find("input[name=StockOut]").css({ display: 'inline-block' });
		}

		newWareHouseSkuLists.forEach(function (item, i) {
			if (i == index) {
				var InOrOutObj = {};
				item.forEach(function (cItem, cI) {
					if (cItem.AttributeName == 'InOrOut') {
						cItem.InOrOut = isTrue;
						InOrOutObj = cItem;
					}
				})
				item.forEach(function (cItem, cI) {
					if (cItem.AttributeName == 'StockCount') {
						cItem.inOrOutObj = InOrOutObj;
					}
				})
			}

		})

	}

	module.EntryInOrOut = function (index, isTrue) {
		var val = $(this).val().trim();
		var re = /^\d+$/;
		if (re.test(val) === false) {
			layer.msg("请输入正整数!");
			$(this).val("");
			val = "";
		}
		newWareHouseSkuLists.forEach(function (item, i) {
			if (i == index) {
				var isHas = false;
				item.forEach(function (cItem, cI) {
					if (cItem.AttributeName == 'InOrOut') {
						cItem.InOrOut = isTrue;
						cItem.ChangeStockCount = val;
						isHas = true;
					}
				})
				if (!isHas) {
					var obj = {};
					obj.AttributeName = 'InOrOut';
					obj.Name = '进出库';
					obj.InOrOut = isTrue;
					obj.ChangeStockCount = val;
					item.push(obj);
				}
				var inOrOutObj = {};
				item.forEach(function (cItem, cI) {
					if (cItem.AttributeName == 'InOrOut') {
						inOrOutObj = cItem;
					}
				})
				item.forEach(function (cItem, cI) {
					if (cItem.AttributeName == 'StockCount') {
						cItem.inOrOutObj = inOrOutObj;
					}
				})
			}

		})
	}

	module.checkFormInputVal = function (typeName) {
		var val = $(this).val().trim();
		if (typeName == "productFullName") {
			if (getByteLen(val) < 15 || getByteLen(val) > 60) {
				$(this).addClass("addWarnInput");
				$(this).next().css({ display: 'inline-block' });
			}
		} else if (typeName == "productShortName") {
			if (val.gblen() > 20) {
				$(this).addClass("addWarnInput");
				$(this).next().css({ display: 'inline-block' });
			}
		}
		showRemindTarData();

	}

	function getByteLen(val) {
		if (!val) return
		var len = 0;
		for (var i = 0; i < val.length; i++) {
			if (val[i].match(/[^\x00-\xff]/ig) != null) //全角
				len += 2;
			else
				len += 1;
		}
		return len;
	}


	module.inpuFormInputVal = function (typeName) {
		$(this).removeClass("addWarnInput");
		$(this).next().css({ display: 'none' });
	}

	module.inpuListenInput = function (typeName) {
		var valLen = 0;
		//if (typeName == "productShortName") {
		//	valLen = $(this).val().trim().length;
		//} else {
		//	valLen = getByteLen($(this).val().trim());
		//}
		valLen = getByteLen($(this).val().trim());
		$(this).closest(".n-inputWrap").find('.titleLength').text(valLen);
	}

	module.changeShowAttributesStatus = function (that, id, IsSelectSku) {
		if (!IsSelectSku) {
			commonModule.w_alert({ type: 3, content: '请选择规格类型' });
			return;
		}
		var hasInputLen = $(that).closest('.addSkuWrap-Skus-main').find('.hasData-input').length;
		if (hasInputLen == 0) {
			commonModule.w_alert({ type: 3, content: '请填写规格值！' });
			return;
		}
		meuResultData.forEach(function (item, i) {
			if (item.Id == id) {
				item.IsEdit = false;
			}
		})

		renderSkuMeu(meuResultData);




	}

	module.changeHideAttributesStatus = function (that, id) {
		meuResultData.forEach(function (item, i) {
			if (item.Id == id) {
				item.IsEdit = true;
			}
		})
		renderSkuMeu(meuResultData);
		sortMenuSkuAttribute();
	}

	module.fullScreen = function () {
		$("#newSkuTableWrap").toggleClass("active");
		if ($("#newSkuTableWrap").hasClass("active")) {
			$("#newSkuTableWrap .icon-a-fullsreen1x").hide();
			$("#newSkuTableWrap .icon-a-fullscreen-exit1x").css({ display: 'inline-block' });
			$("#fullscreenText").text("退出全屏");
			window.parent.postMessage({ isShowScreen: true }, '*');

		} else {
			$("#newSkuTableWrap .icon-a-fullsreen1x").css({ display: 'inline-block' });
			$("#newSkuTableWrap .icon-a-fullscreen-exit1x").hide();
			$("#fullscreenText").text("编辑更多信息");
			window.parent.postMessage({ isShowScreen: false }, '*');
		}

	}
	function listenCustomAddPic() {

		var customPicsOptions = {};
		customPicsOptions.callBack = getCustomPic;
		customPicsOptions.isMultiple = false;
		customPicsOptions.businessType = businessType;

		$("#custom_Table_Wrap .table_content_tbody>tr").each(function (i, item) {
			if ($(this).hasClass('itemChx')) {
				var itemChxIndex = $(this).find('.n-productInfo-img-add').attr("data-index");
				var itemChxCindex = $(this).find('.n-productInfo-img-add').attr("data-cindex");
				customPicsOptions.ele = "#addPic_" + itemChxIndex + itemChxCindex;
				var upCustomPicsOptionsMenu = new upPicFileModule();
				upCustomPicsOptionsMenu.initData(customPicsOptions);
			}
			
		})

		function getCustomPic(resultData) {
			var obj = resultData[0];
			var ImgUrl = obj.TransitUrl;
			var index = $(obj.ele).attr("data-index");
			var cindex = $(obj.ele).attr("data-cindex");
			newWareHouseSkuLists[index].datas.forEach(function (item, i) {
				if (cindex == i) {
					item.ImageUrl = ImgUrl;
					item.ImageUrlStr = obj.ImageUrl;
					item.ImageId = 0;
				}
			});
			var isHasRawResultSkus = rawResultSkus.length > 0 ? true : false;
			initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists, isHasRawResultSkus);
			showRemindTarData();
			checkEdmitOpions();
		}
	}

	module.allCheckSkubox = function () {
		$(this).toggleClass('activeF');

		if ($(this).hasClass('activeP')) {
			$(this).removeClass('activeP');
			$(this).removeClass('activeF');
		}


		var isActiveF = $(this).hasClass('activeF');

		if (isActiveF) {
			$("#sku_tbody .n-newCheckbox.f-chx").addClass('activeF');
			//$("#sku_tbody .n-newCheckbox.c-chx").addClass('activeF');
			$("#sku_tbody tr.chx").addClass('chxActive');

		} else {
			$("#sku_tbody .n-newCheckbox.f-chx").removeClass('activeF');
			$("#sku_tbody .n-newCheckbox.c-chx").removeClass('activeF');
			$("#sku_tbody tr.chx").removeClass('chxActive');

		}


		newWareHouseSkuLists.forEach(function (item, i) {
			item.isCheck = isActiveF;
			item.datas.forEach(function (cItem, cI) {
				if (!cItem.IsDefaultPadding) {
					cItem.isCheck = isActiveF;
				} else {
					cItem.isCheck = false;
				}
			})
		});
		showCheckBoxStatus();

	}

	module.partCheckSkubox = function (i) {
		$(this).toggleClass('activeF');

		if ($(this).hasClass('activeP')) {
			$(this).removeClass('activeP');
			$(this).removeClass('activeF');
		}

		var isActiveF = $(this).hasClass('activeF');
		if (isActiveF) {
			$(this).closest('.chx').addClass('chxActive');
		} else {
			$(this).closest('.chx').removeClass('chxActive');
		}
		$("#sku_tbody .activeSkuName .n-newCheckbox.c-chx").each(function ($I, item) {
			if ($(item).attr("data-index") == i) {
				if (isActiveF) {
					$(item).addClass('activeF');
					$(item).closest('.chx').addClass('chxActive');
				} else {
					$(item).removeClass('activeF');
					$(item).closest('.chx').removeClass('chxActive');

				}
			}
		});
		newWareHouseSkuLists.forEach(function (item, ii) {
			if (ii == i) {
				item.isCheck = isActiveF;
				item.datas.forEach(function (cItem, cI) {
					if (!cItem.IsDefaultPadding) {
						cItem.isCheck = isActiveF;
					} else {
						cItem.isCheck = false;
					}
				})
			}
		});
		showCheckBoxStatus();
	}

	module.singleCheckSkubox = function (i, ci) {
		var defaultPaddingLen = $(this).closest(".SkuName").find(".defaultPaddingTdWrap").length;
		if (defaultPaddingLen > 0) {
			return;
		}

		$(this).toggleClass('activeF');
		var isActiveF = $(this).hasClass('activeF');
		if (isActiveF) {
			$(this).closest('.chx').addClass('chxActive');
			$("#tr_" + i).addClass('chxActive');
		} else {
			$(this).closest('.chx').removeClass('chxActive');
			$("#tr_" + i).removeClass('chxActive');
		}
		newWareHouseSkuLists.forEach(function (item, ii) {
			if (ii == i) {
				item.datas.forEach(function (cItem, cI) {
					if (cI == ci) {
						var isCheck = false;
						cItem.isCheck = isActiveF;
						if (cItem.isCheck) {
							isCheck = true;
						}
						item.isCheck = isCheck;
					}
				})
			}
		});

		showCheckBoxStatus();
	}

	function showCheckBoxStatus() {
		var checks = 0;
		var total = newWareHouseSkuLists.length;
		var isAll = true;
		var checkTotal = 0;

		newWareHouseSkuLists.forEach(function (item, i) {
			if (item.isCheck) {
				checks++;
			}
			item.datas.forEach(function (cItem, cI) {
				if (!cItem.isCheck) {
					isAll = false;
				}
				if (cItem.isCheck) {
					checkTotal++;
				}
			})
		});
		if (checks > 0 && !isAll) {
			$("#allCheckSkubox").removeClass('activeF').addClass('activeP');
		} else if (checks > 0 && checks == total && isAll) {
			$("#allCheckSkubox").removeClass('activeP').addClass('activeF');
		}
		if (checks == 0) {
			$("#allCheckSkubox").removeClass('activeP').removeClass('activeF');
		}

		newWareHouseSkuLists.forEach(function (item, i) {
			var itemChecks = 0;
			var itemTotal = item.datas.length;
			item.datas.forEach(function (cItem, cI) {
				if (cItem.isCheck) {
					itemChecks++;
					$("#chx_" + i + cI).addClass('activeF');
				}
			})

			if (itemChecks > 0 && itemChecks < itemTotal) {
				$("#tr_" + i).find('.f-chx').removeClass('activeF').addClass('activeP');
			} else if (itemChecks == itemTotal) {
				$("#tr_" + i).find('.f-chx').removeClass('activeP').addClass('activeF');
			} else if (itemChecks == 0) {
				$("#tr_" + i).find('.f-chx').removeClass('activeP').removeClass('activeF');
			}
		});
		var html = '';

		if (checkTotal > 0) {
			html += '已选<i style="padding:0 3px;">' + checkTotal + '</i>个规格';
			$("#skuNameWrap").html(html);
			$("#skuTable .n-table th").css({ backgroundColor: "#fff" });
			$("#skuTable .n-table").addClass('activeEdmitTable');
		} else {
			$("#skuTable .n-table").removeClass('activeEdmitTable');
		}

		if (checkTotal == 0) {

			html += '<i>规格</i><span class="rightSpan"><i style="padding:0 3px;font-size:14px;">·</i><s onclick="PrepareDistributionModule.allShowOrHideSku.bind(this)()">全部折叠</s></span>';
			$("#skuNameWrap").html(html);
			$("#skuTable .n-table th").css({ backgroundColor: "#f5f5f5" })
		}

	}



	module.batchSkuItems = function (i, field) {
		var val = $(this).val().trim();
		var moneyreg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/; //金额正则
		if (!moneyreg.test(val)) {
			commonModule.w_alert({ type: 2, content: '价格格式错误!' });
			$(this).val("");
			return;
		}
		newWareHouseSkuLists.forEach(function (item, index) {
			if (index == i) {
				item.datas.forEach(function (cItem, cI) {
					cItem[field] = val;
				});
			}
		})
		//initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists);//重新渲染sku表格
		setSkuTableValue()
	}

	var batchSkusDailog = null;

	module.batchSkus = function (field) {
		var checkTotal = 0;
		newWareHouseSkuLists.forEach(function (item, index) {
			item.datas.forEach(function (cItem, cI) {
				if (cItem.isCheck) {
					checkTotal++;
				}
			});
		})

		if (checkTotal == 0) {
			commonModule.w_alert({ type: 3, content: '请选择编辑SKU!' });
			return;
		}
		if (field == 'SalePrice') {
			PrepareDistributionModule.onDistributionSet("Price", true);
			return
		}
		if (field == 'StockCount') {
			PrepareDistributionModule.onDistributionSet("Stock", true);
			return
		}
		
		if (field == 'SinglePrice') { // 批量设置单买价
			PrepareDistributionModule.onDistributionSet("SinglePrice", true);
			return
		}
		var title = "";
		var html = "";
		if (field == 'SettlePrice') {
			title = "采购价";
		} else if (field == 'CostPrice') {
			title = "成本价";
		} else if (field == 'DistributePrice') {
			title = "分销价";
		} else if (field == 'StockCount') {
			title = "库存";
		} else if (field == 'SkuCode') {
			title = "SKU 编码";
		} else if (field == 'ShortTitle') {
			title = "简称";
		} else if (field == 'SalePrice') {
			title = "售价";
		}


		html += '<div class="batchSkus-content">';
		html += '<div class="batchSkus-title">' + title + '</div>';
		if (field == 'SettlePrice' || field == 'CostPrice' || field == 'DistributePrice' || field == 'SalePrice') {
			html += '<div class="batchSkus-main batchSkus-inputWrap"><i class="priceNum">￥</i><input type="number" id="batchSkusValue" oninput=\'PrepareDistributionModule.batchSkuInput.bind(this)("' + field + '")\' class="n-layui-input priceInput" type="text"></div>';
		}
		if (field == 'StockCount') {
			html += '<div class="batchSkus-main batchSkus-inputWrap"><input type="number" id="batchSkusValue" oninput=\'PrepareDistributionModule.batchSkuInput.bind(this)("' + field + '")\' class="n-layui-input priceInput"></div>';
		}
		if (field == 'SkuCode') {
			html += '<div class="batchSkus-main batchSkus-inputWrap"><span class="n-newCheckbox" onclick="PrepareDistributionModule.changeBatchSkuCode.bind(this)()"></span><span style="font-size:14px;color:#000;">自动生成编码</span></div>';
		}

		if (field == 'ShortTitle') {
			html += '<div class="priceInputWrap">';
			html += '<input id="batchSkusValue" class="n-layui-input" style="width:100%;" type="text" value="" name="ShortTitle" oninput=\'PrepareDistributionModule.batchSkuInput.bind(this)("' + field + '")\' placeholder="请输入" maxlength="10" >';
			html += '<span class="input-num"><i class="titleLength">0</i>/20</span>';
			html += '</div>';
		}

		html += '</div>';
		html += '<div class="layui-layer-btn layui-layer-btn-">';
		html += '<span class="layui-layer-btn-text">对<i>' + checkTotal + '</i>个规格生效</span>';
		html += '<div>';
		html += '<a class="layui-layer-btn0" onclick="PrepareDistributionModule.closeBatchSkusDailog()">取消</a>';
		html += '<a class="layui-layer-btn1 stop" onclick=\'PrepareDistributionModule.sureBatchSkusDailog.bind(this)("' + field + '")\'>确定</a>';
		html += '</div>';
		html += '</div>';

		batchSkusDailog = layer.open({
			type: 1,
			title: '编辑' + title, //不显示标题
			shade: false,
			content: html,
			skin: 'n-skin batchSku-skin',
			area: '560px', //宽高
			btn: false,

		});

	}

	module.closeBatchSkusDailog = function () {
		layer.close(batchSkusDailog);
	}

	module.changeBatchSkuCode = function () {
		$(this).toggleClass("activeF");

		if ($(this).hasClass("activeF")) {
			$(this).closest(".layui-layer-content").find(".layui-layer-btn1").removeClass("stop");

		} else {
			$(this).closest(".layui-layer-content").find(".layui-layer-btn1").addClass("stop");

		}
	}

	module.batchSkuInput = function (field) {
		var val = $(this).val().trim();
		if (field == "StockCount") {
			var re = /^\d+$/;
			if (re.test(val) === false) {
				//commonModule.w_alert({ type: 2, content: '请输入正整数!' });
				$(this).val("");
				$(this).closest(".layui-layer-content").find(".layui-layer-btn1").addClass("stop");
				return;
			}
		} else if (field == "ShortTitle") {
			$(this).closest(".priceInputWrap").find(".titleLength").text(val.length);
		} else {
			var moneyreg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/; //金额正则
			if (!moneyreg.test(val) && val != '') {
				commonModule.w_alert({ type: 2, content: '价格格式错误!' });
				$(this).val("");
				$(this).closest(".layui-layer-content").find(".layui-layer-btn1").addClass("stop");
				return;
			}
		}
		$(this).closest(".layui-layer-content").find(".layui-layer-btn1").removeClass("stop");
	}

	module.sureBatchSkusDailog = function (field, value) {
		if ($(this).hasClass('stop')) {
			return;
		}
		// 批量设置拼单价，根据新拼单价重新计算单买价和参考价
		var isRecalculate = false;
		// 单买价校验
		var SinglePriceFlag = false;
		// 最大单买价
		var maxSinglePrice = 0;
		if (field != 'SkuCode') {
			var val = $("#batchSkusValue").val() || value;
			newWareHouseSkuLists.forEach(function (item, index) {
				item.datas.forEach(function (cItem, cI) {
					if (cItem.isCheck) {
						if (typeof val == 'object') {
							if (field === 'SalePrice' || field == "SinglePrice") {
								val.forEach(function (item2, index2) {
									if (cItem.FromBaseProductSkuUid === item2.SkuUid) {
										cItem[field] = item2.Price === 0 ? '' : item2.Price;
									}
									// 拼多多拼单价弹窗设置,跟据配置重新计算单买价
									if (item2.isRecalculate) {
										isRecalculate = true;
										// PriceType 1:单买价 2:参考价 3:单买价
										if (item2.PriceType == 3) {
											cItem.SinglePrice = item2.Price;
										} else {
											cItem.SinglePrice = item2.Price ? item2.Price + 1 : 0;
										}
									}
								})
							}
							if (field === 'StockCount') {
								val.forEach(function (item2, index2) {
									if (cItem.FromBaseProductSkuUid === item2.SkuUid) {
										cItem[field] = item2.value === 0 ? '' : item2.value;
									}
								})
							}
						} else {
							if (field == "SinglePrice" && val - cItem.SalePrice < 1 ) {
								SinglePriceFlag = true;
							}
							cItem[field] = val;
						}
					}
					maxSinglePrice = cItem.SinglePrice > maxSinglePrice ? cItem.SinglePrice : maxSinglePrice;
				});
			});
			
			if(field == "SinglePrice") {
				if (SinglePriceFlag) {
					commonModule.w_alert({ type: 2, content: '单买价不能低于拼单价!' });
					return false;
				}
				$('input[name="ReferencePrice"]').val(parseFloat(val) + 1);
				if ($('input[name="ReferencePrice"]').closest('.n-inputWrap').hasClass('warnInput')) {
					$('input[name="ReferencePrice"]').closest('.n-inputWrap').removeClass('warnInput');
					$('input[name="ReferencePrice"]').closest('.n-inputWrap').find('.input-warnTitle').remove();
				}
			}
			if (isRecalculate) {
				// 参考价默认按单sku最高单买价+1
				$('input[name="ReferencePrice"]').val(parseFloat(maxSinglePrice) + 1);
				if ($('input[name="ReferencePrice"]').closest('.n-inputWrap').hasClass('warnInput')) {
					$('input[name="ReferencePrice"]').closest('.n-inputWrap').removeClass('warnInput');
					$('input[name="ReferencePrice"]').closest('.n-inputWrap').find('.input-warnTitle').remove();
				}
			}
			initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists);//重新渲染sku表格
			if (field == "ShortTitle") {
				$("#sku_tbody .titleLength").text(val.length);
			}

		} else {  //批量生成 SKU 编码

			var promiseSelectPicArry = [];
			newWareHouseSkuLists.forEach(function (item, index) {
				item.datas.forEach(function (cItem, cI) {
					if (cItem.isCheck) {
						var PromiseObj = new Promise(function (resolve, reject) {
							commonModule.Ajax({
								url: "/BaseProduct/CodeGenerate",
								type: "GET",
								loading: true,
								async: true,
								data: {},
								success: function (rsp) {
									if (commonModule.IsError(rsp)) {
										reject(commonModule.IsError(rsp));
										return;
									}
									if (rsp.Success) {
										resolve(rsp.Data.Code);
									}
								}
							});
						})
						promiseSelectPicArry.push(PromiseObj)
					}
				});
			})

			Promise.all(promiseSelectPicArry).then(function (resultData) {
				var mun = 0;
				newWareHouseSkuLists.forEach(function (item, index) {
					item.datas.forEach(function (cItem, cI) {
						if (cItem.isCheck) {
							cItem[field] = resultData[mun];
							mun++;
						}
					});
				})
				oldWareHouseSkuLists = JSON.parse(JSON.stringify(newWareHouseSkuLists));
				initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists);//重新渲染sku表格
			})
		}
		layer.close(batchSkusDailog);
		showRemindTarData();
		resultSkus.forEach(function (item, index) {
			for (var k in item) {
				if (k == field) {
					item[k] = val
				}
			}
		})

	}

	module.allShowOrHideSku = function () {
		$(this).toggleClass("active");

		if ($(this).hasClass("active")) {
			$(".thskuHeader s").text("全部展开");
			$("#sku_tbody .itemChx").addClass("hide");
			$("#sku_tbody .chx .icon-down").addClass("active");

		} else {
			$(".thskuHeader s").text("全部折叠");
			$("#sku_tbody .itemChx").removeClass("hide");
			$("#sku_tbody .chx .icon-down").removeClass("active");
		}
	}

	function animateScrollTop(el) {
		var scrollTop = el.offset().top - 20;
		scrollTop = $('.full-mask-content').scrollTop() + scrollTop;
		$('.full-mask-content').animate({
			scrollTop: scrollTop > 0 ? scrollTop : 0
		}, 300);
	}
	function checkSinglePrice(flag) {  //检测参考价和单买价 flag 为true时滚动条不定位

		var isReturn = flag || false;
		// 参考价校验
		var referencePrice = $('.referencePriceInputWrap input[name="ReferencePrice"]').val();
		var maxSinglePrice = 0; //最大单买价
		var len = 0;
		newWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				if (!cItem.IsDefaultPadding) {
					if (cItem.SinglePrice - cItem.SalePrice < 1) {
						$("#sku_tbody input[name=SinglePrice]").each(function (eachInex, eachItem) {
							var val = $(eachItem).val().trim();
							if (val == "") {
								$(eachItem).closest(".n-inputWrap").addClass("warnInput");
								commonModule.w_alert({ type: 2, content: '请填写单买价!' });
								if (!isReturn) {
									animateScrollTop($(eachItem));
								}
								isReturn = true;
								return isReturn;
							}
							if (eachInex == len) {
								$(eachItem).closest(".n-inputWrap").addClass("warnInput");
								$(eachItem).val("");
								$(eachItem).closest(".priceInputWrap").after('<div class="input-warnTitle">至少比拼单价高1元</div>');
								if (!isReturn) {
									animateScrollTop($(eachItem));
								}
								isReturn = true;
								return isReturn;
							}
							
						});
					}
					if (cItem.SinglePrice > maxSinglePrice) {
						maxSinglePrice = cItem.SinglePrice;
					}
					// 规格图片校验
					if (!cItem.ImageUrl) {
						$("#sku_tbody .newProductPicShow-itembox").each(function (eachInex, eachItem) {
							if (eachInex == len) {
								$(eachItem).find('.img-warnTitle').remove();
								$(eachItem).addClass('warnImg');
								$(eachItem).append('<div class="img-warnTitle">请上传规格图片</div>');
								if (!isReturn) {
									animateScrollTop($(eachItem));
								}
								isReturn = true;
								return isReturn;
							}
						})
					}
					len++;
				}
			})
		})
		var referencePriceTip = ''; //参考价提示
		if ( referencePrice > maxSinglePrice * 5) {
			referencePriceTip = '参考价不得高于最高规格单买价的5倍: '+maxSinglePrice * 5+'元';
		}
		if (!referencePrice ||referencePrice <= maxSinglePrice) {
			referencePriceTip ='参考价应大于商品最大单买价'
		}
		if (referencePriceTip) {
			$('input[name="ReferencePrice"]').closest(".priceInputWrap").find('.input-warnTitle').remove();
			$('input[name="ReferencePrice"]').closest(".priceInputWrap").addClass("warnInput");
			$('input[name="ReferencePrice"]').closest(".priceInputWrap").val();
			$('input[name="ReferencePrice"]').after('<div class="input-warnTitle">'+referencePriceTip+'</div>');
			if (!isReturn) {
				animateScrollTop($('#productSku input[name="ReferencePrice"]'));
			}
			isReturn = true;
			return isReturn;
		}
		return isReturn;
	}
	function checkEdmitOpions() {
		var isReturn = false;
		setProductImages = [];
		setProductDetailsPics = [];
		IsNewImage = [];
		ImageId = [];
		checkCateProps()
		isReturn = checkSinglePrice()
		productFullName = $('input[name=productFullName]').val().trim();
		if (productFullName.gblen() < 15 || productFullName.gblen() > 60) {
			//layer.msg('请输入商品名称为：15-60个字符（8-30个汉字）！');
			commonModule.w_alert({ type: 2, content: '请输入15-60个字符（8-30个汉字）' });
			$('input[name=productFullName]').addClass('addWarnInput');
			animateScrollTop($('input[name=productFullName]'));
			isReturn = true;
			return isReturn;
		}

		productImages.forEach(function (item) {
			setProductImages.push(item.ImageUrl);
			if (item.ClassName) {
				IsNewImage.push(true);
				ImageId.push(0);
			} else {
				IsNewImage.push(false);
				ImageId.push(item.getId);
			}
		})

		if (setProductImages.length < 1) {
			commonModule.w_alert({ type: 2, content: '主图至少1张图片！' });
			$(".addnewProductPicShow_item").css({ borderColor: '#fe6f4f' })
			$('.productPicWrap-inputWarnTitle').show();
			$('.productPicWrap-inputWarnTitle').html('主图至少1张图片');
			animateScrollTop($('#newProductPicShow'));
			isReturn = true;
			return isReturn;
		}
		if (setProductImages.length > 10) {
			commonModule.w_alert({ type: 2, content: '主图最多上传10张！' });
			isReturn = true;
			animateScrollTop($('#newProductPicShow'));
			return isReturn;
		}
		productDetailsPics.forEach(function (item) {
			if (item.Url != '') {
				setProductDetailsPics.push(item.ImageUrl);
			};
		})

		if (setProductDetailsPics.length < 1) {
			commonModule.w_alert({ type: 2, content: '请上传详情图' });
			$(".decorateWrap").css({ borderColor: '#fe6f4f' });
			$(".detailsPicsWrap .input-warnTitle").css({ display: 'block' });
			animateScrollTop($('.decorateWrap'));
			isReturn = true;
			return isReturn;
		}
		if (setProductDetailsPics.length > 50) {
			commonModule.w_alert({ type: 2, content: '详情图至多上传50张图片！' });
			$(".decorateWrap").css({ borderColor: '#fe6f4f' });
			$('.decorateWrap-inputWarnTitle').show();
			animateScrollTop($('.decorateWrap'));
			isReturn = true;
			return isReturn;
		}


		if (newWareHouseSkuLists.length == 0) {
			commonModule.w_alert({ type: 2, content: '添加商品规格!' });
			isReturn = true;
			return isReturn;
		}

		$("#sku_tbody input[name=SalePrice]").each(function (i, item) {
			var val = $(item).val().trim();
			if (val == "") {
				$(item).closest(".n-inputWrap").addClass("warnInput");
				commonModule.w_alert({ type: 2, content: '请填写拼单价!' });
				isReturn = true;
				if (!isReturn) {
					animateScrollTop($(item));
				}
				return isReturn;
			}
		});
		
		

		var maxAttributeData = 100;
		meuResultData.forEach(function (item, i) {
			if (item.IsSelectSku && item.SelectItem.AttributeData.length == 0) {
				layer.msg('请添加' + item.SelectItem.Name + "值");
				isReturn = true;
				return isReturn;
			}
			if (item.IsSelectSku && item.SelectItem.AttributeData.length > maxAttributeData) {
				layer.msg('您请添加<i style="font-weight:700;padding:0 5px">' + item.SelectItem.Name +
					"值</i>，超过了" + maxAttributeData + "条");
				isReturn = true;
				return isReturn;
			}

		})



		return isReturn;
	}
	// 未填写强提示
	function checkEdmitOpionsWarn() {
		var isReturn = false;
		setProductImages = [];
		setProductDetailsPics = [];
		IsNewImage = [];
		ImageId = [];
		var warnDom = null;
		checkCateProps()
		checkSinglePrice(true)
		productFullName = $('input[name=productFullName]').val().trim();
		if (productFullName.gblen() < 15 || productFullName.gblen() > 60) {
			//layer.msg('请输入商品名称为：15-60个字符（8-30个汉字）！');
			$('input[name=productFullName]').addClass('addWarnInput');
			warnDom = $('input[name=productFullName]');
		}

		productImages.forEach(function (item) {
			setProductImages.push(item.ImageUrl);
			if (item.ClassName) {
				IsNewImage.push(true);
				ImageId.push(0);
			} else {
				IsNewImage.push(false);
				ImageId.push(item.getId);
			}
		})

		if (setProductImages.length < 1) {
			$(".addnewProductPicShow_item").css({ borderColor: '#fe6f4f' })
			$('.productPicWrap-inputWarnTitle').show();
			$('.productPicWrap-inputWarnTitle').html('主图至少1张图片');
			warnDom = $('#newProductPicShow');
		}
		productDetailsPics.forEach(function (item) {
			if (item.Url != '') {
				setProductDetailsPics.push(item.ImageUrl);
			};
		})

		if (setProductDetailsPics.length < 1) {
			$(".decorateWrap").css({ borderColor: '#fe6f4f' });
			$(".detailsPicsWrap .input-warnTitle").css({ display: 'block' });
		}


		$("#sku_tbody input[name=SalePrice]").each(function (i, item) {
			var val = $(item).val().trim();
			if (val == "") {
				$(item).closest(".n-inputWrap").addClass("warnInput");
			}
		});


		var maxAttributeData = 100;
		meuResultData.forEach(function (item, i) {
			if (item.IsSelectSku && item.SelectItem.AttributeData.length == 0) {
				layer.msg('请添加' + item.SelectItem.Name + "值");
			}
			if (item.IsSelectSku && item.SelectItem.AttributeData.length > maxAttributeData) {
				layer.msg('您请添加<i style="font-weight:700;padding:0 5px">' + item.SelectItem.Name +
					"值</i>，超过了" + maxAttributeData + "条");
			}

		})

		if (warnDom) {
			//animateScrollTop(warnDom)
		}

		return isReturn;
	}

	function changeProductSkus() {

		var setSkus = [];
		newWareHouseSkuLists.forEach(function (item, i) {
			var obj = {};
			item.forEach(function (cItem, cI) {
				if (cItem.AttributeName == "ImageUrl") {
					obj[cItem.AttributeName] = cItem.ImgObj;

				} else if (cItem.AttributeName == "InOrOut") {
					obj.InOrOut = cItem.InOrOut;
					obj.ChangeStockCount = cItem.ChangeStockCount;
				}
				else {
					obj[cItem.AttributeName] = cItem.Value;
				}
				if (OperateType == "edmit") {
					if (cItem.AttributeName == "SkuCode") {
						if (Reflect.has(cItem, 'IsNewSkuCode')) {
							obj.IsNewSkuCode = cItem.IsNewSkuCode;
						}
					}
					if (cItem.AttributeName == "CostPrice") {
						if (Reflect.has(cItem, 'IsUpdatePtSkuCostPrice')) {
							obj.IsUpdatePtSkuCostPrice = cItem.IsUpdatePtSkuCostPrice;
						}
					}
					if (cItem.AttributeName == "SettlePrice") {
						if (Reflect.has(cItem, 'IsUpdatePtSkuSettlePrice')) {
							obj.IsUpdatePtSkuSettlePrice = cItem.IsUpdatePtSkuSettlePrice;
						}
					}
					if (cItem.AttributeName == "DistributePrice") {
						if (Reflect.has(cItem, 'IsUpdatePtSkuDistributePrice')) {
							obj.IsUpdatePtSkuDistributePrice = cItem.IsUpdatePtSkuDistributePrice;
						}
					}

				}

			})
			setSkus.push(obj);
		})

		setSkus.forEach(function (item) {
			for (var k in item) {
				if (k == "Attribute") {
					var obj = {};
					var index = 1;
					var resultObj = item[k][0];
					obj.ImgAttributeValueNo = isShowImg ? 1 : 0;
					for (var cK in resultObj) {
						var AttributeName = "AttributeName" + index;
						var AttributeValue = "AttributeValue" + index;
						obj[AttributeName] = cK;
						obj[AttributeValue] = resultObj[cK];
						index = index + 1;
					}
					item["Attribute"] = obj;
				}
			}
		})

		setSkus.forEach(function (item, i) {

			if (i == 0 && OperateType == "edmit") {
				item.SkuUid = edmitBaseProductSkuUid;
			}
			item.IsPublic = false;
			item.UpFxUserId = 0;
			item.UpSkuUid = false;
			item.IsCombineSku = false;
			item.Weight = 0;  //重量先默认为0

			if (OperateType == "edmit") {
				item.ImageObjectId = ImageObjectId;
				item.OldSkuCode = OldSkuCode;
			}

		})
		return setSkus;
	}

	function changeNewProductSkus() {


		var setSkus = [];	
		newWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				setSkus.push(cItem);

			})
		})
		setSkus.forEach(function (item, i) {

			if (!item.ImageUrl && item.SkuObj[0].ImgObj) {
				if (isShowImg) {
					item.ImageUrl = item.SkuObj[0].ImgObj.ShowUrl;
					item.ImageId = item.SkuObj[0].ImgObj.ImageObjectId;
				} else {
					item.ImageUrl = '';
					item.ImageId = '';
				}
			} else {
				item.ImageUrl = item.ImageUrl || '';
				item.ImageId = item.ImageId || '';
			}
			if (OperateType == "edmit") {
				if (item.isNew) {
					item.IsNewSkuCode = true;
				}
			}


			var Attribute = {};
			Attribute.ImgAttributeValueNo = isShowImg ? 1 : 0;
			item.SkuObj.forEach(function (cItem, cI) {
				var AttributeName = "AttributeName" + (cI + 1);
				var AttributeValue = "AttributeValue" + (cI + 1);
				Attribute[AttributeName] = cItem.Name;
				Attribute[AttributeValue] = cItem.Value;
				if (isShowImg && item.SkuObj[0] && item.SkuObj[0].ImgObj && JSON.stringify(item.SkuObj[0].ImgObj) != '{}') {
					var showUrl = item.SkuObj[0].ImgObj.ShowUrl;
					Attribute.ValueUrl = showUrl.replace('/Common/GetImageFile?objectKey=', '');
					Attribute.ValueUrlKey = showUrl.replace('/Common/GetImageFile?objectKey=', '');
				}
			})
			item.Attribute = Attribute;
			item.IsPublic = false;
			item.UpFxUserId = 0;
			item.UpSkuUid = false;
			item.IsCombineSku = false;
			item.Weight = 0;  //重量先默认为0

			if (item.SkuCode == "") {
				item.SkuCode = "at" + new Date().getTime() + i;
			}
		})

		return setSkus;
	}


	function getEdmitRewValueSorts() {
		var setValueSorts = [];
		$("#addSkuWrap_Skus_ul>li").each(function (i, item) {
			var obj = {};
			var str = "";
			var selectName = $(item).find("select.n-select").val() ? $(item).find("select.n-select").val() : '无';
			$(item).find("ul.showSkuValue>li").each(function (cI, cItem) {
				var val = $(cItem).find(".hasData-input").val();
				str += val;
			})
			obj[selectName] = str;

			setValueSorts.push(obj);
		})

		return setValueSorts;

	}


	$(document).on("click", function () {
		$(".skuCode-toast").css({ display: "none" });
		$(".rawSkusWrap").remove();
		document.body.style.overflow = '';//启用滚动条
		$("#selectBatchWrap").removeClass('active');
		$("#catePropsWrap .catePropsItem").removeClass('active');
		$(".n-myCommonSelect").removeClass("active");
		$("#catePropsWrap02 .catePropsItem").removeClass('active');

	})

	document.onscroll = function () {
		clearTimeout(window.scrollEndTimer)
		window.scrollEndTimer = setTimeout(function () {
			var rawSkusWrap = $("#rawSkusWrap");
			if (rawSkusWrap.length == 1) {
				var offset = rawSkusWrap.closest(".n-productInfo-right").find(".matchedMoreSku-title-wrap").offset();
				var allTop = offset.top - $(window).scrollTop();
				rawSkusWrap.css({ top: allTop })
			}
		}, 0)
	}



	function setOldAddSkuTableValue() {
		var changeOldWareHouseSkuLists = [];
		oldWareHouseSkuLists = mergeAttributes(oldWareHouseSkuLists);
		newWareHouseSkuLists = mergeAttributes(newWareHouseSkuLists);
		oldWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				changeOldWareHouseSkuLists.push(cItem);
			})
		});
		newWareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				changeOldWareHouseSkuLists.forEach(function (changeItem, changeI) {
					if (cItem.AttributesName == changeItem.AttributesName) {
						for (var k in cItem) {
							switch (k) {
								case "ShortTitle":
									cItem[k] = changeItem.ShortTitle ? changeItem.ShortTitle : '';
									break;
								case "CostPrice":
									cItem[k] = changeItem.CostPrice ? changeItem.CostPrice : '';
									break;
								case "SettlePrice":
									cItem[k] = changeItem.SettlePrice ? changeItem.SettlePrice : '';
									break;
								case "DistributePrice":
									cItem[k] = changeItem.DistributePrice ? changeItem.DistributePrice : '';
									break;
								case "StockCount":
									cItem[k] = changeItem.StockCount ? changeItem.StockCount : '';
									break;
								case "SkuCode":
									cItem[k] = changeItem.SkuCode ? changeItem.SkuCode : '';
									break;
								default:
									break;
							}
						}

					}

				})
			})
		});
		initNewAddSkuTable(document.getElementById("newSkuTableWrap"), newWareHouseSkuLists);//重新渲染sku表格

	}

	function mergeAttributes(wareHouseSkuLists) {
		wareHouseSkuLists.forEach(function (item, i) {
			item.datas.forEach(function (cItem, cI) {
				cItem.AttributesName = "";
				cItem.SkuObj.forEach(function (ccItem, ccI) {
					cItem.AttributesName += ccItem.Value + "·"
				})
			})
		})
		return wareHouseSkuLists;
	}

	function showRemindTarData() {


		var categoryMeuMustNum = 0;
		var categoryChangeMeuMustNum = 0;
		//商品类目
		if ($("#categoryMeu").hasClass('selectValue')) {
			var noRequiredNum = 0;
			var requiredNum = 0;
			$(".catePropsItemWrap.noRequired").each(function (index, item) {

				var itemss = $(item).find('.catePropsItem');

				var isHass = true;
				itemss.each(function (cIndex, cItem) {
					if (!$(cItem).hasClass("hasActive")) {
						isHass = false;
					}
				})
				if (isHass) {
					noRequiredNum++;
				}

			});
			$(".catePropsItemWrap.required").each(function (index, item) {

				var items = $(item).find('.catePropsItem');
				var isHas = true;
				items.each(function (cIndex, cItem) {
					if (!$(cItem).hasClass("hasActive")) {
						isHas = false;
					}
				})
				if (isHas) {
					requiredNum++;
				}
			});

			$("#hasCatePropsTotal02").html(noRequiredNum);
			$("#hasCatePropsTotal").html(requiredNum);

			categoryMeuMustNum = (categoryMeuMustNum + $(".catePropsItemWrap.required").length);
			categoryChangeMeuMustNum = categoryMeuMustNum - requiredNum;

			var categoryMeuNeedNum = $(".catePropsItemWrap.noRequired").length;
			var categoryChangeMeuNeedNum = categoryMeuNeedNum - noRequiredNum;

		} else {

			categoryMeuMustNum = 1;
			categoryChangeMeuMustNum = 1;
		}





		//商品信息
		var infoMustNum = 0;
		var infoNeedNum = 0;
		if ($('input[name=productFullName]').val().trim() == '') {
			infoMustNum++;
		};
		if (productImages.length == 0) {
			infoMustNum++;
		};

		if (productDetailsPics.length == 0) {
			infoMustNum++;
		};



		//商品规格
		if (newWareHouseSkuLists != undefined && newWareHouseSkuLists.length > 0) {
			var changeSkuNeedNum = 0;
			var allRows = 0;
			var changeRows = 0;
			// 最大单买价
			var maxSinglePrice = 0;
			newWareHouseSkuLists.forEach(function (item, i) {
				item.datas.forEach(function (cItem, cI) {

					if (!cItem.IsDefaultPadding) {
						allRows++;
						if (cItem.SkuCode == '') {
							changeRows++
						}
						if (!cItem.ImageUrl) {
							changeRows++
						}
						for (var k in cItem) {
							if (OperateType == "edmit") {

								if (k == "StockCount") {
									if (cItem[k] == "") {
										changeSkuNeedNum++;
									}
								}
								if (k == "SalePrice") {
									if (cItem[k] == "") {
										changeRows++;
									}
								}
								if (k == "SinglePrice") {
									if (cItem[k] == "") {
										changeRows++;
									}
								}

							} else {
								if (k == "SalePrice") {
									if (cItem[k] == "") {
										changeRows++;
									}
								}
								if (k == "SinglePrice") {
									if (cItem[k] == "") {
										changeRows++;
									}
								}
							}
						}
					}
					if (cItem.SinglePrice > maxSinglePrice) {
						maxSinglePrice = cItem.SinglePrice;
					}
				})
			})
			
			var referencePrice = $('.referencePriceInputWrap input[name="ReferencePrice"]').val();
			referencePrice = referencePrice ? parseFloat(referencePrice) : '';
			// 参考价
			if(referencePrice == '' || referencePrice > maxSinglePrice * 5|| referencePrice <= maxSinglePrice){
				changeRows++;
			};
			var skuchangeRows = parseInt($('#addSkuWrap_Skus_ul .n-sColor').length) || 0;
			if (skuchangeRows) {
				changeRows = changeRows + skuchangeRows
			}
		}


		var setDistributionMustNum = 6;
		var setDistributionChangeMustNum = 0;

		$("#setPrepareDistribution .n-mySelect.catePropsItem").each(function (index, item) {
			if (!$(this).is(":hidden") && !$(item).hasClass("hasActive")) {
				setDistributionChangeMustNum = setDistributionChangeMustNum + 1;
			}
		})

		remindTarData.forEach(function (item, i) {

			if (i == 0) {
				item.mustNum = categoryMeuMustNum;
				item.changeMustNum = categoryChangeMeuMustNum;
				item.needNum = categoryMeuNeedNum;
				item.changeNeedNum = categoryChangeMeuNeedNum;
			}
			if (i == 1) {
				item.changeMustNum = infoMustNum;
				item.changeNeedNum = infoNeedNum;
			}
			if (i == 2) {
				item.mustNum = allRows;
				item.changeMustNum = changeRows;
				item.needNum = allRows * 2;
				item.changeNeedNum = changeSkuNeedNum;
			}
			if (i == 3) {
				item.mustNum = setDistributionMustNum;
				item.changeMustNum = setDistributionChangeMustNum;

			}

		})


		var html = '';
		$("#writtenWarnContent").html("");
		remindTarData.forEach(function (item, i) {

			var classNameActive = item.IsActive ? 'active' : '';
			item.Status = 'must';
			html += '<li class="n-writtenWrp-main-li ' + classNameActive + '" onclick="chooseRemindItem(' + i + ')">';
			html += '<a href="#' + item.tarEle + '">';
			html += '<div class="n-writtenWrp-main-li-left">';
			html += '<span class="n-writtenWrp-main-li-left-text">' + item.Title + '</span>';

			if (item.changeMustNum == 0) {
				item.Status = 'need';
			}

			if (item.changeMustNum == 0 && item.changeNeedNum == 0) {
				item.Status = 'success';
			}

			if (item.Status == 'must') {
				var changeMustNum = '';
				if (item.changeMustNum > 0 && item.changeMustNum != item.mustNum) {
					changeMustNum = item.changeMustNum + '个';
				}
				html += '<span class="n-tarTxt n-tarTxt02 submitStop">' + changeMustNum + '需完善</span>';
			} else if (item.Status == 'need') {
				var needNum = '';
				if (item.changeNeedNum > 0 && item.needNum != item.changeNeedNum) {
					needNum = item.changeNeedNum + '个';
				}
				html += '<span class="n-tarTxt n-tarTxt01">' + needNum + '待完善</span>';
			} else if (item.Status == 'success') {
				html += '<span class="n-tarTxt n-tarTxt04">已完善</span>';
			}
			html += '</div>';
			html += '<span class="iconfont icon-a-arrow-right1x"></span>';
			html += '</a>';
			html += '</li>';

		});
		$("#writtenWarnContent").html(html);

		if ($("#writtenWarnContent .submitStop").length > 0) {
			$("#submitCreateDataBtn").addClass("stop");
			$("#saveBatchDistributionSetBtn").addClass("stop");

		} else {
			$("#submitCreateDataBtn").removeClass("stop");
			$("#saveBatchDistributionSetBtn").removeClass("stop");

		}


	}

	chooseRemindItem = function (i) {
		remindTarData.forEach(function (item, index) {
			item.IsActive = false;
			if (i == index) {
				item.IsActive = true;
			}
		});
		showRemindTarData();
	}

	closeCretaeProductShow = function () {
		DistributionCommonModule.goBack();
		//window.parent.postMessage({ operateType: 'closeCretaeProduct' }, '*');
	}
	selectBatchFun = function () {
		event.stopPropagation();
		$(this).toggleClass("active");
	}

	mouseHelpShowText = function () {
		var offset = $(this).offset();
		$(this).find('.th-tooltip').css({ top: offset.top });
	}

	function checkCateProps() {  //检测商品类目和属性值

		var isTrue = true;
		if (CatePathList.length == 0) {
			isTrue = false;
			$(".categoryMeuItem").addClass("formWarn")
			isTrue = false;
		}

		$("#catePropsWrap .catePropsItemWrap.required").each(function (index, item) {
			if (!$(item).find(".n-mySelect").hasClass("hasActive")) {
				$(item).addClass("formWarn");
				isTrue = false;
			}

			if ($(item).find('.multiValueMeasureWrap').length == 1 || $(item).find('.measureWrap').length == 1) {
				if ($(item).find('.n-mySelect-title-chooseItem').text() == "" || $(item).find('.n-mySelect-input').val() == "") {
					$(item).addClass("formWarn");
					isTrue = false;
				}
			}
		})
		return isTrue;
	}
	module.changeCheckboxFun = function(keyName) { // 复选框
        var BatchSettingValue = UserListingSetting.BatchSettingValue;
		BatchSettingValue[keyName] = !BatchSettingValue[keyName];
        $('span[name="' + keyName + '"]').removeClass('activeF');
		if (BatchSettingValue[keyName]) {
			$('span[name="' + keyName + '"]').addClass('activeF');
		}
	}

	//---------------------------编辑编辑逻辑--------------------------------------------------


	targetUrlFun = function () {
		layer.open({
			type: 1,
			title: '未保存，是否退出抖店编辑页面？', //不显示标题
			content: '<div style="font-size:14px;" class="c06">退出后，本次所有更改不会生效。</div>',
			area: '560px', //宽高
			btn: ['留下', '确定'],
			shade: false,
			skin: 'n-skin goBackskin',
			yes: function (index) {
				layer.close(index);
			},
			btn2: function () {
				window.parent.postMessage({ edmitBaseproductuid: edmitBaseproductuid, operateType: 'tarEdmitBaseProductUrl' }, '*');
			}
		});
	}

	refreshPage = function () {
		window.location.href = window.location.href;
	}
	goToBaseProductEdmitFun = function () {
		window.parent.postMessage({ operateType: 'goToBaseProductEdmitFun' }, '*');
	}
	function changeSubmitProductImages(productImages) {
		var newImages = [];
		productImages.forEach(function (item, i) {
			var obj = {};
			if (i == 0) {
				item.IsMain = true;
			} else {
				item.IsMain = false;
			}
			if (item.ClassName) {
				var obj = {};
				obj.ImageObjectId = item.Id;
				obj.ImageUrl = item.TransitUrl;
				obj.IsMain = item.IsMain;
				newImages.push(obj);

			} else {
				delete item.TransitUrl;
				newImages.push(item);
			}

		})

		return newImages;
	}

	goLastNext = function () {
		$("#full-content_step02").hide();
		$("#full-content_step01").show();
		$(".n-writtenWrp").hide();
		$("#prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
		$("#prepareDistribution_nav .prepareDistribution-nav-item:eq(0)").addClass("active");

	}

	$("#full-content_step02").scroll(function () {
		clearTimeout(window.scrollEndTimer02)
		window.scrollEndTimer02 = setTimeout(function () {
			$("#catePropsWrap02 .catePropsItem.active").removeClass("active");
		}, 100);
	});

	showMoreCateProps = function () {
		$("#catePropsWrap02").toggleClass("hideMorewCatePropsWrap");
		$(this).toggleClass("active");
		if ($(this).hasClass("active")) {
			$(this).find("span").text("折叠");
		} else {
			$(this).find("span").text("展开");
		}
	}

	//---------------------------编辑编辑逻辑 结束------------

	//n-addressDailog


	//提交数据--------------------------------------------

	function checkEdmitUserListingSetting() {
		var isReturn = false;
		$("#setPrepareDistribution .catePropsItemWrap").each(function (index, item) {

			if (!$(this).is(":hidden") && !$(item).find(".catePropsItem").hasClass("hasActive")) {
				isReturn = true;
				$(item).addClass("formWarn");

			}

		})

		return isReturn;

	}

	function ExtCategoryAttributes(CateProps) {


		CateProps.forEach(function (item, i) {
			if (item.FieldType == "brandSearch" || item.FieldType == "input" || item.FieldType == "singleCheck" || item.FieldType == "measure") {

				if (item.Value == null || item.Value == "") {
					item.ValueString = '';
				} else {
					item.ValueString = item.Value;
				}
			} else if (item.FieldType == "checkbox" || item.FieldType == "checkboxlist") {

				if (item.Value != null || item.Value != "") {

					if (Array.isArray(item.Value)) {
						item.ValueString = item.Value.join(",");
					} else {
						item.ValueString = item.ValueString;
					}
				} else {
					item.ValueString = '';
				}

			} else if (item.FieldType == "dateTimeRange") {

				if (item.MeasureTemplates.LeftValue != '' && item.MeasureTemplates.RightValue != '') {
					item.ValueString = item.MeasureTemplates.LeftValue + ',' + item.MeasureTemplates.RightValue;

				} else {
					item.ValueString = '';
				}

			} else if (item.FieldType == "multiValueMeasure") {
				var newValueString = '';
				item.MeasureTemplates.MultiValueMeasureList.forEach(function (item, i) {
					newValueString += (item.Name + ',' + item.Value) + '||';
				})
				item.ValueString = newValueString;
			}
		})


		CateProps.forEach(function (item, i) {
			if (item.FieldType == "checkbox" || item.FieldType == "checkboxlist") {
				if (Array.isArray(item.Value)) {
					item.Value = item.Value.join(",");

				}
				if (item.FieldType == "checkbox" || item.FieldType == "checkboxlist" || item.FieldType == "brandSearch") {
					item.Options = null;
				}

			}
		})
	}


	module.submitDistribution = function () {


		var that = this;
		if ($(this).hasClass("active")) { return; }

		if (!checkCateProps()) {  //检查类目和属性
			commonModule.w_alert({ type: 2, content: '请选择目录和类目属性' });
			animateScrollTop($('#category'));
			return;
		}
		var maxNum = 0;
		$(".multiValueMeasure").each(function (index, item) {
			var num = $(item).find(".n-mySelect-input").val().trim();
			if (num != "") {
				maxNum = maxNum + (num - 0);
			}
		})
		if (maxNum != 100 && CatePathList.length > 0 && $("#requiredCateProps .multiValueMeasure").length > 0) {
			commonModule.w_alert({ type: 2, content: '面料材质总和需等于100%' });
			animateScrollTop($('#category'));
			$(".multiValueMeasure").each(function (index, item) {
				$(item).addClass("formWarn");
			})
			return;
		}

		if (checkEdmitOpions()) {//检查其它必填属性
			return;
		}

		if (checkEdmitUserListingSetting()) {//检查其它必填属性
			commonModule.w_alert({ type: 2, content: '请填写铺货设置' });
			return;
		}

		var skuNotFilledIn = false;
		$('#addSkuWrap input.hasData-input').each(function (item, i) {
			var _val = $(this).val();
			if (!_val) {
				$(this).addClass('addWarnInput');
				animateScrollTop($(this));
				skuNotFilledIn = true
			}
		})
		if (skuNotFilledIn) {
			commonModule.w_alert({ type: 2, content: '请填写SKU规格值' });
			return;
		}


		//var options = {};
		//options.UniqueCode = UniqueCode;  //来自接口
		//options.Subject = productFullName;
		//options.PlatfromType = setPlatformTypeName;
		//options.BaseProductUid = BaseProductUid;
		//options.CreateFrom = GetCreateFrom;
		//options.FromFxUserId = GetFromFxUserId;
		//options.FromBaseProductUid = FromBaseProductUid;
		//options.FromSupplierProductUid = FromSupplierProductUid;
		//options.SpuCode = GetSpuCode;
		//options.CategoryId = GetCategoryId;
		//options.IsPublic = IsPublic;
		//options.RootNodeFxUserId = RootNodeFxUserId;
		//options.SharePathCode = SharePathCode;
		//options.PathNodeDeep = PathNodeDeep;
		//options.FxUserId = GetFxUserId;
		//options.FromType = GetFromType;
		//options.FromCode = GetFromCode;
		//options.IsWaitSyncBaseProduct = IsWaitSyncBaseProduct
		//options.IsSkuChange = IsSkuChange
		//options.ProductImages = changeSubmitProductImages(productImages);
		//options.DescriptionStr = setProductDetailsPics; //详情图片
		//options.CategoryInfoList = CatePathList;
		//ExtCategoryAttributes(CateProps);
		//options.CategoryAttribute = JSON.stringify(CateProps);


		//var newGetAttributeTypes = JSON.parse(JSON.stringify(getAttributeTypes));
		//PtProductDatas.AttributeTypes.forEach(function (item) {
		//	delete item.SelectItem
		//	item.AttributeValues.forEach(function (jData) {
		//		if (jData.SellPropertyValues && jData.SellPropertyValues.property_values) {
		//			delete jData.SellPropertyValues.property_values
		//		}
		//		if (PtProductDatas.SellPropertyJson.length === 0) {
		//			delete jData.SellPropertyValues
		//		} else {
		//			jData.SellPropertyValues = Array.isArray(jData.SellPropertyValues) ? jData.SellPropertyValues : [jData.SellPropertyValues]
		//		}
		//	})
		//})
		//newGetAttributeTypes.forEach(function (item) {
		//	delete item.SelectItem
		//	item.AttributeValues.forEach(function (jData) {
		//		if (jData.SellPropertyValues && jData.SellPropertyValues.property_values) {
		//			delete jData.SellPropertyValues.property_values
		//		}
		//		if (PtProductDatas.SellPropertyJson.length === 0) {
		//			delete jData.SellPropertyValues
		//		} else {
		//			jData.SellPropertyValues = Array.isArray(jData.SellPropertyValues) ? jData.SellPropertyValues :[jData.SellPropertyValues]
		//		}
		//	})
		//})
		//var ListingShopIds = [];
		//ShopsData.forEach(function (item) {
		//	if (item.IsCheck) {
		//		ListingShopIds.push(item.ShopId);
		//	}
		//})

		//options.ListingShopIds = ListingShopIds;

		//options.AttributeTypes = PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length ? PtProductDatas.AttributeTypes : newGetAttributeTypes;

		//options.UserListingSetting = UserListingSetting;
		//options.ListingConfig = ListingConfig;


		//var newProductInfoSkus = JSON.parse(JSON.stringify(changeNewProductSkus()));

		//newProductInfoSkus.forEach(function (item) {
		//	delete item.AttributesName;
		//	delete item.ChangeStockCount;
		//	delete item.ImageId;
		//	delete item.IsCombineSku;
		//	delete item.IsPublic;
		//	delete item.OldSkuCode;
		//	delete item.SkuName;
		//	delete item.SkuObj;
		//	delete item.SkuUid;
		//	delete item.UpFxUserId;
		//	delete item.UpSkuUid;
		//	delete item.fristSkuName;
		//	delete item.Weight;
		//	delete item.fristSkuObj;
		//	delete item.isNew;
		//})
		//options.ProductInfoSkus = newProductInfoSkus;

		options = getEditBatchOpions();

		//console.log("optionsoptions", options)
		//return;

		commonModule.Ajax({
			url: '/api/Listing/Save',
			type: "POST",
			loading: true,
			contentType: 'application/json',
			data: JSON.stringify(options),
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					//$(that).removeClass("active");
					return;
				}
				if (rsp.Success) {
					//创建成功后
					window.parent.postMessage({ resultData: rsp.Data, operateType: 'StartDistribution' }, '*');
				}
			}
		});


	}

	// 打开批量设置售价和库存
	module.onDistributionSet = function (source, isBatch) {
		sourceType = source;
		distributionSetBatch = isBatch || false;
		// console.log("queryQaram===",queryQaram)
		if (source === 'Price' || source === 'SinglePrice') {
			if (!isBatch) {
				var val = $(this).val().trim();
				var obj = {
					"Price": val,
					"SkuUid": "",
					"PriceType": source === 'SinglePrice' ? 3:0
				}
				sourceSetDom = $(this);
				queryQaram.CalculatePrices = [obj];

			} else {
				var CalculatePrices = [];
				newWareHouseSkuLists.forEach(function (item, index) {
					item.datas.forEach(function (cItem, cI) {
						if (cItem.isCheck) {
							var obj = {
								"Price": cItem.SalePrice || '',
								"SettlePrice": cItem.SettlePrice || 0,
								"DistributePrice": cItem.DistributePrice || 0,
								"SkuUid": cItem.FromBaseProductSkuUid || '',
								"PriceType": source === 'SinglePrice' ? 3:0
							}
							CalculatePrices.push(obj);
						}
					});
				});
				
				queryQaram.CalculatePrices = CalculatePrices;
			}
			if (source === 'SinglePrice') {  //批量设置单买价弹窗
				DistributionSettingsModule.onDialogSetSinglePricePdd();
				return
			} 
			DistributionSettingsModule.onDialogSetPrice()
		}
		
		
		//if (source === 'spuPrice') {
		//	DistributionSettingsModule.onDialogSetPrice()
		//}
		if (source === 'Stock') {
			var idx = $(this).attr('data-idx');
			var cindex = $(this).attr('data-cindex');
			var name = $(this).attr('name');
			var StockCountList = [];
			newWareHouseSkuLists.forEach(function (item, i) {
				if (i == idx && !isBatch) {
					item.datas.forEach(function (cItem, cI) {
						if (cI == cindex) {
							var obj = {
								"StockCount": cItem.StockCount,
								"SkuUid": cItem.FromBaseProductSkuUid || '',
								OriginalStockCount: cItem.OriginalStockCount, //源库存
							}
							StockCountList.push(obj);
						}
					})
				}
				if (isBatch) {
					item.datas.forEach(function (cItem, cI) {
						if (cItem.isCheck) {
							var obj = {
								"StockCount": cItem.StockCount,
								"SkuUid": cItem.FromBaseProductSkuUid || '',
								OriginalStockCount: cItem.OriginalStockCount, //源库存
							}
							StockCountList.push(obj);
						}
					})
				}
			})
			queryQaramStock.StockCountList = StockCountList;
			sourceSetDom = $(this);
			DistributionSettingsModule.onDialogSetStock()
		}

	}


	EdmitDistributionItem = function (UniqueCode, Subject) {

		module.EdmitEventInit();


		$("#new_prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
		$("#new_prepareDistribution_nav .prepareDistribution-nav-item:eq(2)").addClass("active");

		$("#isShowDefaultSet").hide();
		var ShopsIds = [];
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				ShopsIds.push(item.ShopId);
			}
		})

		var model = {};
		model.UniqueCode = UniqueCode;
		model.Subject = Subject;
		model.ShopIds = ShopsIds;

		commonModule.Ajax({
			url: '/api/PtProductInfoDraft/GetPtInfoDraft',
			type: "POST",
			loading: true,
			contentType: 'application/json',
			data: JSON.stringify(model),
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Success) {



					batchEdmitStepNum = 3;//标记批量操作 第三步 编辑功能
					var resultData = rsp.Data;
					if (resultData.IsSyncPtProductInfoButton) {
						$("#changeSyncPtProductInfoLabel").css({ display: "flex" });
						$('#changeSyncPtProductInfoLabel span:eq(1)').html('同步保存到拼多多资料')
					} else {
						$("#changeSyncPtProductInfoLabel").css({ display: "none" });
					}
					$("#distributionSet_list").hide();
					$("#full-content_step02").show();
					$("#singleDistributionSetFooter").hide();
					$("#saveBatchDistributionSetFooter").css({ display: "flex" });
					$(".n-writtenWrp").show();
					if (IsSyncPtProductInfo) {
						$("#IsSyncPtProductInfo").addClass("activeF");
					} else {
						$("#IsSyncPtProductInfo").removeClass("activeF");
					}

					SourceSku = JSON.parse(JSON.stringify(rsp.Data.ProductInfoSkus));
					PtProductDatas = JSON.parse(JSON.stringify(rsp.Data));
					
					if (PtProductDatas.SellPropertyJson) {
						PtProductDatas.SellPropertyJson = JSON.parse(PtProductDatas.SellPropertyJson);

						// sku规格属性模板转换
						onSellPropertyJsonFormat();

						var newSellPropertyJson = [];
						PtProductDatas.AttributeTypes.forEach(function (jData, j) {
							if (jData.SellPropertyJson) {
								jData.SellPropertyJson = JSON.parse(jData.SellPropertyJson);
							}
							PtProductDatas.SellPropertyJson.forEach(function (item, i) {
								if (item.sell_property_id == jData.SellPropertyId) {
									newSellPropertyJson.push(item)
									jData.AttributeValues.forEach(function (kData, k) {

										var obj = {
											support_remark: item.support_remark,
											support_diy: item.support_diy,
											sell_property_id: item.sell_property_id || "", //销售属性id 
											sell_property_name: item.sell_property_name || "", //销售属性名称 
											sell_property_value_id: '', //值对应的id ,
											sell_property_value_name: '', //值的名称 ,
											values: kData.Value || '', //完整的值 ,
											sourceValues: kData.OldValue || '', // 源sku值
											remark: item.remark, //备注的值 ,,
											value_modules: [],
											template_id: '',
										}
										if (kData.SellPropertyValues && kData.SellPropertyValues.length) {
											obj.sell_property_value_id = kData.SellPropertyValues[0].sell_property_value_id; //值对应的id ,
											obj.sell_property_value_name = kData.SellPropertyValues[0].sell_property_value_name;
											obj.value_modules = kData.SellPropertyValues[0].value_modules;
											obj.remark = kData.SellPropertyValues[0].remark;
											obj.template_id = item.measure_templates && item.measure_templates.length == 1 ? item.measure_templates[0].template_id : (kData.SellPropertyValues[0].ext1 || '');
											SellPropertyValuesId = kData.SellPropertyValues[0].ext2;
										}
										kData.SellPropertyValues = obj
									})
									// 特殊分类模板 - radio选中
									if (SellPropertyValuesId) {
										item.special_values.forEach(function (specialItem,specialIndex){
											specialItem.isCheck = false;
											if (specialItem.id == SellPropertyValuesId){
												specialItem.isCheck = true;
											}
										})
									}
								}

							})

						})
						PtProductDatas.SellPropertyJson = newSellPropertyJson.length ? newSellPropertyJson : PtProductDatas.SellPropertyJson;
						rsp.Data.SellPropertyJson = PtProductDatas.SellPropertyJson;
					}

					// sku规格属性值 - 分类导航数据
					var NavCateValues = rsp.Data.NavCateValues;
					if (NavCateValues && NavCateValues.length) {
						NavCateValues.forEach(function (item, index) {
							if (item.ChildNodes && item.ChildNodes.length) {
								item.ChildNodes.forEach(function (item2, index2) {
									item2.LowerLevelAttrData = JSON.parse(item2.LowerLevelAttrData)
								})
							}
						})
					}

					setBaseProductInfo(resultData);
					checkEdmitOpionsWarn();
					if (setPlatformTypeName == "Pinduoduo") {
						// 完成铺货的商品状态(放入仓库)，库存计数隐藏
						$('#warehouseCalcRule_set').closest('li').hide();
						$(".isshow_pdd").css("display", "flex");
						//判断是否显示完成铺货的商品状态(放入仓库)
						if ($('#Batch_productStatus_set ul li').attr('data-value') == "2") {
							$('#Batch_productStatus_set ul li:eq(1)').remove();
						}
					} else {
						$('#warehouseCalcRule_set').closest('li').show();
						$(".isshow_pdd").css("display", "none");
					}

				}
			}

		});
	}

	goBackBatchDistributionSet = function () {
		$(".n-writtenWrp").hide();
		$("#full-content_step02").hide();
		// 单商品铺货返回铺货设置
		if (edmitBaseproductuidArray && edmitBaseproductuidArray.length === 1 && isBatchPuhuo) {
			$('#distributionSet_step').css({ display: "block" });
			GoLastBatchDistribution()
			return false;
		}

		$("#distributionSet_list").show();
		$("#saveBatchDistributionSetFooter").css({ display: "flex" });
		$("#new_prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
		$("#new_prepareDistribution_nav .prepareDistribution-nav-item:eq(2)").addClass("active");
		commonModule.w_alert({ type: 5, skin: 'goBackBatchSetSkin', times: 5000, content: '已关闭商品编辑<span class="n-dColor mL12 hover" onclick="recoverBatchEdmit()">恢复</span>' });
	}

	recoverBatchEdmit = function () {

		$(".n-writtenWrp").show();
		$("#distributionSet_list").hide();
		$("#full-content_step02").show();
		$(".goBackBatchSetSkin").hide();

	}

	changeSyncPtProductInfo = function () {

		$("#IsSyncPtProductInfo").toggleClass("activeF");
		IsSyncPtProductInfo = $("#IsSyncPtProductInfo").hasClass("activeF");

	}

	saveBatchDistributionSet = function () {  //更新编辑铺货商品 

		var that = this;
		//if ($(this).hasClass("stop")) { return; }
		if ($(this).hasClass("active")) { return; }

		if (!checkCateProps()) {  //检查类目和属性
			commonModule.w_alert({ type: 2, content: '请选择目录和类目属性' });
			animateScrollTop($('#category'));
			return;
		}
		var maxNum = 0;
		$(".multiValueMeasure").each(function (index, item) {
			var num = $(item).find(".n-mySelect-input").val().trim();
			if (num != "") {
				maxNum = maxNum + (num - 0);
			}
		})
		if (maxNum != 100 && CatePathList.length > 0 && $("#requiredCateProps .multiValueMeasure").length > 0) {
			commonModule.w_alert({ type: 2, content: '面料材质总和需等于100%' });
			animateScrollTop($('#category'));
			$(".multiValueMeasure").each(function (index, item) {
				$(item).addClass("formWarn");
			})
			return;
		}

		if (checkEdmitOpions()) {//检查其它必填属性
			return;
		}

		if (checkEdmitUserListingSetting()) {//检查其它必填属性
			commonModule.w_alert({ type: 2, content: '请填写铺货设置' });
			return;
		}

		var skuNotFilledIn = false;
		$('#addSkuWrap input.hasData-input').each(function (item, i) {
			var _val = $(this).val();
			if (!_val) {
				$(this).addClass('addWarnInput');
				animateScrollTop($(this));
				skuNotFilledIn = true
			}
		})
		if (skuNotFilledIn) {
			commonModule.w_alert({ type: 2, content: '请填写SKU规格值' });
			return;
		}

		var options = getEditBatchOpions();
		if (IsSyncPtProductInfoButton) {
			IsSyncPtProductInfo = $("#IsSyncPtProductInfo").hasClass("activeF");
		}

		options.IsSyncPtProductInfo = IsSyncPtProductInfo;
		options.IsSyncPtProductInfoButton = IsSyncPtProductInfoButton;
		options.ShopCount = BatchShopCount;
		//console.log("optionsoptionsaaa", options);
		//return;
		$(this).addClass("active");

		commonModule.Ajax({
			url: '/api/PtProductInfoDraft/UpdatePtInfoDraft',
			type: "POST",
			loading: true,
			contentType: 'application/json',
			data: JSON.stringify(options),
			success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					//$(that).removeClass("active");
					return;
				}
				if (rsp.Success) {
					// 单品铺货直接铺货，不保存
					if (edmitBaseproductuidArray && edmitBaseproductuidArray.length === 1 && isBatchPuhuo) {
						//DistributionbaseListModule.GetPtInfoDrafts(PtProductUniqueCodeArray);//重新拉平台资料列表
						SubmitBatchDistribution()
						return
					}
					$(that).removeClass("active");
					commonModule.w_alert({ type: 4, content: '保存成功' });
					$("#distributionSet_list").show();
					$("#full-content_step02").hide();
					$("#singleDistributionSetFooter").css({ display: "flex" });
					$("#saveBatchDistributionSetFooter").hide();
					$(".n-writtenWrp").hide();
					DistributionbaseListModule.GetPtInfoDrafts(PtProductUniqueCodeArray);//重新拉平台资料列表


				}
			}
		});


	}

	function getEditBatchOpions() {  //获取提交参数  包括：单品铺货提交参数 和修改批量提交参数

		var options = {};
		options.UniqueCode = UniqueCode;  //来自接口
		options.Subject = productFullName;
		options.PlatfromType = setPlatformTypeName;
		options.BaseProductUid = BaseProductUid;
		options.CreateFrom = GetCreateFrom;
		options.FromFxUserId = GetFromFxUserId;
		options.FromBaseProductUid = FromBaseProductUid;
		options.FromSupplierProductUid = FromSupplierProductUid;
		options.SpuCode = GetSpuCode;
		options.CategoryId = GetCategoryId;
		options.IsPublic = IsPublic;
		options.RootNodeFxUserId = RootNodeFxUserId;
		options.SharePathCode = SharePathCode;
		options.PathNodeDeep = PathNodeDeep;
		options.FxUserId = GetFxUserId;
		options.FromType = GetFromType;
		options.FromCode = GetFromCode;
		options.IsWaitSyncBaseProduct = IsWaitSyncBaseProduct
		options.IsSkuChange = IsSkuChange
		options.ProductImages = changeSubmitProductImages(productImages);
		options.DescriptionStr = setProductDetailsPics; //详情图片
		options.CategoryInfoList = CatePathList;
		ExtCategoryAttributes(CateProps);
		options.CategoryAttribute = JSON.stringify(CateProps);
		options.ReferencePrice = parseFloat($('.referencePriceInputWrap input[name="ReferencePrice"]').val()) || '';


		var newGetAttributeTypes = JSON.parse(JSON.stringify(getAttributeTypes));
		PtProductDatas.AttributeTypes.forEach(function (item) {
			delete item.SelectItem
			item.AttributeValues.forEach(function (jData) {
				if (jData.IsRequiredTip) {
					jData.IsRequiredTip = false
				}
				if (jData.SellPropertyValues && jData.SellPropertyValues.property_values) {
					delete jData.SellPropertyValues.property_values
				}
				if (PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length === 0) {
					delete jData.SellPropertyValues
				} else {
					jData.SellPropertyValues = Array.isArray(jData.SellPropertyValues) ? jData.SellPropertyValues : [jData.SellPropertyValues]
				}
			})
		})
		newGetAttributeTypes.forEach(function (item) {
			delete item.SelectItem
			item.AttributeValues.forEach(function (jData) {
				if (jData.SellPropertyValues && jData.SellPropertyValues.property_values) {
					delete jData.SellPropertyValues.property_values
				}
				if (PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length === 0) {
					delete jData.SellPropertyValues
				} else {
					jData.SellPropertyValues = Array.isArray(jData.SellPropertyValues) ? jData.SellPropertyValues : [jData.SellPropertyValues]
				}
			})
		})
		var ListingShopIds = [];
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				ListingShopIds.push(item.ShopId);
			}
		})

		options.ListingShopIds = ListingShopIds;

		options.AttributeTypes = PtProductDatas.SellPropertyJson && PtProductDatas.SellPropertyJson.length ? PtProductDatas.AttributeTypes : newGetAttributeTypes;

		options.UserListingSetting = UserListingSetting;
		options.ListingConfig = ListingConfig;

		var newProductInfoSkus = JSON.parse(JSON.stringify(changeNewProductSkus()));

		newProductInfoSkus.forEach(function (item) {
			delete item.AttributesName;
			delete item.ChangeStockCount;
			delete item.ImageId;
			delete item.IsCombineSku;
			delete item.IsPublic;
			delete item.OldSkuCode;
			delete item.SkuName;
			delete item.SkuObj;
			delete item.SkuUid;
			delete item.UpFxUserId;
			delete item.UpSkuUid;
			delete item.fristSkuName;
			delete item.Weight;
			delete item.fristSkuObj;
			delete item.isNew;
		})
		options.ProductInfoSkus = newProductInfoSkus;

		return options;

	}

	return module;
})(PrepareDistributionModule || {}, jQuery, layer);

