
var selectShopParthModule = (function (module, commmon, $,) {
	var ShopPlatforms = [
		{ Id: 1, Platform: 'TouTiao', Name: "抖音", AuthUrl: "",Check:true, Display: true, sort: 1 },
		{ Id: 2, Platform: 'Pinduoduo', Name: "拼多多", AuthUrl: "", Check: false, Display: true, sort: 2 },
		{ Id: 3, Platform: 'Taobao', Name: "淘宝", AuthUrl: "", Check: false,Display: false, sort: 3 },
		{ Id: 4, Platform: 'Jingdong', Name: "京东", AuthUrl: "", Check: false, Display: false, sort: 4 },
		{ Id: 5, Platform: 'KuaiShou', Name: "快手", AuthUrl: "", Check: false, Display: true, sort: 5 },
		{ Id: 6, Platform: 'TouTiaoSaleShopSupply', Name: "即时零售", AuthUrl: "",Check:false, Display: true, sort: 6 },
	]
	module.RenderShopPlatforms = function () {
		
		var html = "";
		ShopPlatforms = ShopPlatforms.sort(function (a, b) { return a.sort - b.sort });
		ShopPlatforms.forEach(function (item,index) {
			if (setPlatformTypeName == 'TouTiaoSaleShopSupply' && item.Platform !== "TouTiaoSaleShopSupply") return;
			var className = item.Check ? "chooseShopPlatformWrap dgActive " + item.Platform : "chooseShopPlatformWrap TouTiao " + item.Platform;
			className = item.Display ? className : className + " stop";
			var className2 = "chooseShopPlatform-icon chooseShopPlatform " + item.Platform;
			html += '<li onclick="selectShopParthModule.choosePlatformNav(' + index +')" class="' + className + '" data-platform="' + item.Platform +'">';
			html += '<span class="' + className2 +'"></span>';
			html += '<span class="chooseShopPlatform-title">'+item.Name+'</span>';
			html += '</li>';
		});
		$("#choosePlatformAddShop").html(html);
	}

	module.choosePlatformNav = function (index) {
		var checkItem = ShopPlatforms[index];
		if (!checkItem.Display) {
			return;
		}
		ShopPlatforms.forEach(function (item,i) {
			item.Check = false;
			if (i == index) {
				item.Check = true;
				changePlatformType(item.Platform);
			}
		})
		module.RenderShopPlatforms();
		getShopsFun();//获取店铺;
	}
	changePlatformType = function (Platform) {
	    setPlatformTypeName = Platform;
		// 铺货设置平台数据结构是写死的，所以这里需要重新赋值
		if (BatchListingSettingObj) {
			BatchListingSettingObj.PlatformType = Platform;
			BatchListingSettingObj.UserListingSetting.PlatformType = Platform;
		}
		//$("#platformDistribution").attr("src", "~/Scripts/Proudctlist/DistributionParth/Edmit_parth_Pdd.js");
		// 获取jacascript标签，先删除后动态添加
		var scriptTag = document.getElementById("platformDistribution");
		if (scriptTag) {
			scriptTag.remove();
		}
		if (Platform == "Pinduoduo") {
			var script = document.createElement("script");
			script.id = "platformDistribution";
			script.type = "text/javascript";
			script.src = "/Scripts/Proudctlist/DistributionParth/Edmit_parth_Pdd.js";
			document.body.appendChild(script);
		} else if (Platform == "KuaiShou") {
			var script = document.createElement("script");
			script.id = "platformDistribution";
			script.type = "text/javascript";
			script.src = "/Scripts/Proudctlist/DistributionParth/Edmit_parth_Ks.js?v="+new Date().getTime();
			document.body.appendChild(script);
		} else{
			var script = document.createElement("script");
			script.id = "platformDistribution";
			script.type = "text/javascript";
			script.src = "/Scripts/Proudctlist/DistributionParth/Edmit_parth.js";
			document.body.appendChild(script);
		}
	}

    getShopsFun = function (ShopId, callback) {  //获取店铺列表
        var options = {};
        options.platformType = setPlatformTypeName;
        options.Flag = 1;
        if (ShopId) {
            options.ShopId = ShopId;
        }
        commonModule.Ajax({
            url: '/partner/LoadMyShopList',
            data: options,
            async: true,
            loading: true,
            type: 'GET',
            success: function (rsp) {
				if (commonModule.IsError(rsp)) {
					return;
				}
				if (rsp.Data == null || rsp.Data == "") {
					ShopsData = [];
				} else {
					ShopsData = rsp.Data.List || [];  
				}
                if (!ShopId) {
                    checkStorageShopsData(ShopsData);
                    renderShopsData(ShopsData);
                }
                if (typeof callback == "function") {
                    callback(ShopsData);
                }
            }
        });
    }

	function checkStorageShopsData(ShopsData) {
		var storageCheckShops = commonModule.getStorage("storageCheckShops") || [];
		storageCheckShops.forEach(function (item) {
			ShopsData.forEach(function (cItem, cI) {
				if (cItem.Status == 1) {
					if (item == cItem.ShopId) {
						cItem.IsCheck = true;
					}
				}
			})
		})
		var isCheckedData = ShopsData.filter(function (item) {
			return item.IsCheck;
		});
		//console.log('缓存的勾选店铺数据', isCheckedData);
		if (isCheckedData && isCheckedData.length > 0) {
			MatchShopPermissions(isCheckedData);
		}
	}
	function renderShopsData(ShopsData) {
		var tplt = $.templates("#shops_data_tr")
		var html = tplt.render({
			shops: ShopsData,
		});
		$("#tbody_shop_data").html(html);
		var checkNum = 0;
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				checkNum++;
			}
		})
		var shopLen = ShopsData.length;
		if (checkNum == shopLen) {
			$("#allShopsCheck").addClass("activeF");
		}
		if (checkNum > 0 && checkNum != shopLen) {
			$("#allShopsCheck").addClass("activeP").removeClass("activeF");
		}
		if (checkNum == 0) {
			$("#allShopsCheck").removeClass("activeP").removeClass("activeF");
		}
		if (checkNum > 0) {
			$(".chooseShopTable").addClass("active");
			$("#nextDistributionBtn").removeClass("stop");
		} else {
			$(".chooseShopTable").removeClass("active");
			$("#nextDistributionBtn").addClass("stop");
		}
	}

	// 匹配查找店铺新老应用权限
	function MatchShopPermissions(ShopsData) {
		var distributionData = [];
		var routineData = [];
		// 查找是否有false的数据，也就是是否有常规的应用
		distributionData = ShopsData.filter(function (item) {
			return !item.IsFxListingApp;
		});
		// 查找是否有true的数据，也就是是否有铺货的应用
		routineData = ShopsData.filter(function (item) {
			return item.IsFxListingApp;
		});
		// 双角色用户,获取当前用户标记,only_listing表示纯铺货用户，其他值老用户
		var userFlag = commonModule.userFlag;
		if (userFlag === "only_listing") {
			if (routineData.length > 0) {
				commonModule.orderingTips({
					title: '订单服务权限提示',
					content: "您当前的店铺暂未订购店管家分销代发，无法开启代发订单自动推送厂家，厂家代发单号无法自动回传您的店铺后台。"
				});
			}
		} else {
			if (distributionData.length > 0) {
				commonModule.orderingTips({
					content: "您当前的店铺暂未订购店管家选品上货应用，无法开启铺货权限，如需铺货功能，请前往服务市场订购店管家选品上货。"
				});
			}
		}
	}

	gotToAddShopFun = function () {
		var platform = $("#choosePlatformAddShop .chooseShopPlatformWrap.dgActive").attr("data-platform");
		var token = commonModule.GetQueryString("token");

		supportPlatforms.forEach(function (item) {
			if (platform == item.PlatformType) {
				window.open((item.AuthUrl + "&rp=" + token), '_blank');
			}
		})

		var html = '<div class="c06 f14" style="padding:16px;">完成授权后，即可选择该店铺进行铺货。</div>';
		layer.open({
			type: 1,
			title: '是否已完成店铺授权？', //不显示标题
			content: html,
			area: '560px', //宽高
			skin: 'n-skin',
			shade: false,
			zIndex: 1000000000000,
			success: function () { },
			btn: ['未完成', '已完成'],
			btn2: function () {
				window.location.href = window.location.href;
			}
		});
	}

	// 全选
	changeAllShopsCheck = function () {
		var HasStopNum = $("#tbody_shop_data .td-body .n-newCheckbox.stop").length;
		var ClassName = HasStopNum > 0 ? 'activeP' : 'activeF'
		if (HasStopNum > 0) {
			$(this).toggleClass(ClassName)
		} else {
			$(this).toggleClass(ClassName)
		}
		var that = this;
		ShopsData.forEach(function (item) {
			if (item.IsExpire && item.PlatformPayUrl != "") {
			} else {
				if (item.Status == 1) {
					item.IsCheck = $(that).hasClass(ClassName);
				}
			}
		});
		var IsCheck = $(that).hasClass(ClassName);
		//console.log('全选', IsCheck);
		if (IsCheck) {
			MatchShopPermissions(ShopsData);
		}
		renderShopsData(ShopsData);
		setStorageCheckShops(ShopsData);
	}

	// 单选
	singleShopsCheck = function (Id) {
		var $checkBox = $(this).find(".n-newCheckbox");
		if ($checkBox.hasClass("stop")) {
			ShopsData.forEach(function (item) {
				if (item.Id == Id) {
					CommonOrderTips(item);
				}
			});
			return;
		}
		ShopsData.forEach(function (item) {
			if (item.Id == Id) {
				item.IsCheck = !item.IsCheck;
				// 双角色用户,获取当前用户标记,only_listing表示纯铺货用户，其他值老用户
				if (item.IsCheck) {
					CommonOrderTips(item);
				}
			}
		});
		renderShopsData(ShopsData);
		setStorageCheckShops(ShopsData);
	}

	//选择店铺保存到缓存 下次点击赋值
	function setStorageCheckShops(ShopsData) {
		var setStorageCheckShops = [];
		ShopsData.forEach(function (item) {
			if (item.IsCheck) {
				setStorageCheckShops.push(item.ShopId);
			}
		});
		commonModule.setStorage("storageCheckShops", setStorageCheckShops);
	}


	function CommonOrderTips(item) {
		// 双角色用户,获取当前用户标记,only_listing表示纯铺货用户，其他值老用户
		var userFlag = commonModule.userFlag;
		if (userFlag === "only_listing") {
			if (item.IsFxListingApp) {
				commonModule.orderingTips({
					title: '订单服务权限提示',
					content: "您当前的店铺暂未订购店管家分销代发，无法开启代发订单自动推送厂家，厂家代发单号无法自动回传您的店铺后台。"
				});
			}
		} else {
			if (!item.IsFxListingApp) {
				commonModule.orderingTips({
					content: "您当前的店铺暂未订购店管家选品上货应用，无法开启铺货权限，如需铺货功能，请前往服务市场订购店管家选品上货。"
				});
			}
		}
	}

	latformRenewUrlFun = function (url, platformType, NickName) {
		commonModule.latformRenewUrlTouTiao(platformType, NickName, url);
	}

	nextDistribution = function (isTrue) {  //功能：选择店铺下一步  和  铺货日志跳过来重新铺货


		if (!isTrue) {
			if ($("#nextDistributionBtn").hasClass("stop")) {
				return;
			}
		}

		if (isBatchPuhuo) {  //铺货下一步

			$("#full-content_step01").hide();
			$("#distributionSet_step").show();
			$("#new_prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
			$("#new_prepareDistribution_nav .prepareDistribution-nav-item:eq(1)").addClass("active");
			DistributionbaseSetModule.GetBatchListingSetting();//获取铺货设置
			$("body").addClass("batchBody");

		} else {             //重新铺货

			$("#full-content_step01").hide();
			$("#full-content_step02").show();
			$(".n-writtenWrp").show();
			$("#prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
			$("#prepareDistribution_nav .prepareDistribution-nav-item:eq(1)").addClass("active");

			if (OperateType == "edmit" || OperateType == "copy") {  //来自基础商品编辑
				var FxUserId = commonModule.getQueryVariable('FxUserId') || '';
				var FromCode = commonModule.getQueryVariable('FromCode') || '';
				var PlatformType = commonModule.getQueryVariable('PlatformType') || '';
				UrlFromType = commonModule.getQueryVariable('FromType') || (FromCode ? 4 : FxUserId ? 3 : 2); //0跟据基础商品铺货，1 跟据平台资料，2 跟据自己货盘，3 跟据厂商货盘，4 铺货任务
				if (UrlFromType == 4) {
					changePlatformType(setPlatformTypeName || PlatformType);
					setTimeout(function() {
						PrepareDistributionModule.GetBaseProductDetail(edmitBaseproductuid, FxUserId, FromCode, PlatformType, UrlFromType);//获取商品详情
					},300);
				} else {
					PrepareDistributionModule.GetBaseProductDetail(edmitBaseproductuid, FxUserId, FromCode, PlatformType, UrlFromType);//获取商品详情
				}
			} else {
				PrepareDistributionModule.GetCategoryMenu();  //应该没有调用到  老数据原因逻辑
			}

			if (OperateType == "edmit") {
				$("body").addClass("edmitBody");
			}
			getTemplates();//获取运费模板
		}

	}


    return module;
}(selectShopParthModule || {}, commonModule, jQuery));