;(function(win) {

	win.wuTemplateModule = {
		onresizeLoading:true,
		getSearchItem: function(element) {
			if (!element) {
				return;
			}
			var searchData = [];
			//筛选列表数据 只用span用于划分格子，目前只有1x,2x
			$(element + ' .wu-searchWrap-item').each(function(index, item) {
				searchData.push({
					span: $(this).data('span') || 1
				})
			})
			return searchData;
		},
		// 屏幕宽度
		onReSize: function(searchData, element,onresizeLoading) {
			this.onresizeLoading = false;			
			$(element).addClass("initSearchWrap")
			var DomRect = document.querySelector(element).getBoundingClientRect();
			// 主体dom宽度
			var width = parseInt(DomRect.width);
			// 每项宽度
			var itemWidth = 160;
			// 每项最小宽度
			var itemMinWidth = 160;
			// 每项最大宽度
			//var itemMaxWidth = 328;																				
			// 每项间距
			var itemMargin = 8;
			var minCount = 1;

			// 每行显示个数
			var itemCount = Math.floor((width) / (itemWidth + itemMargin));
			itemCount = minCount > itemCount ? minCount : itemCount;

			var newSearchData = JSON.parse(JSON.stringify(searchData));
			// 每行剩余宽度(newSearchData循环)
			var columnWidthRemainder = width;
			// 每行个数(newSearchData循环)
			var rowCount = 0;
			var timer = null;
			// 是否满足dom宽度
			for (var i = 0; i < newSearchData.length; i++) {
				var item = newSearchData[i];
				item.span = Math.floor(item.span);
				rowCount = rowCount + item.span;
				// 下一项
				var nextItem = i + 1 < newSearchData.length ? Math.floor(newSearchData[i + 1].span) : 0;
				// 计算下一项加入后是否超出宽度
				if (rowCount + nextItem > itemCount && rowCount !== itemCount) {
					newSearchData[i].span = newSearchData[i].span + 1;
					++rowCount;
				}
				if (rowCount >= itemCount) {
					rowCount = 0;
				}
			}
			var __style = 'repeat(' + Math.floor(itemCount) + ', calc((100% - ' + (itemCount - 1) * 8 +
				'px) / ' + Math.floor(
					itemCount) + '))';
			$(element).css({
				'grid-template-columns': __style
			});
			// dom赋值每项宽度
			$(element+' .wu-searchWrap-item').each(function(index, item) {
				for (var i = 0; i < newSearchData.length; i++) {
					var jtem = newSearchData[i];
					if (index === i && jtem.span) {
						$(item).css({
							'grid-column': 'span ' + jtem.span,
							'width': '100%'
						})
						break;
					}
				}
			})

			$(element).removeClass("wu-initSearchWrap")
			this.onresizeLoading = true;		
		},
		onReSizeControlTable:function(element,callBack) {
			this.onresizeLoading=false;
			
			// 底部悬浮滚动条宽度赋值	
			var tableWrapElement=document.querySelector(element);
			var tableWrapScrollWidth= tableWrapElement.scrollWidth; 	
			var clientWidth=document.querySelector(element).clientWidth+32;
		
			if(tableWrapScrollWidth>clientWidth){
				tableWrapElement.classList.add('stickyOperateTable')
			}else{
				tableWrapElement.classList.remove('stickyOperateTable')
			}
			var _tableWidth = $(element + ' .wu-sticky-table').width();
			$('.art-sticky-scroll .art-sticky-scroll-item').css({
				width: (_tableWidth - 32) + 'px'
			});
			var stickyScroll = document.querySelector('.art-sticky-scroll');
			stickyScroll.addEventListener('scroll', function() {
				var _scrollLeft = document.querySelector('.art-sticky-scroll').scrollLeft;
				$(element).scrollLeft(_scrollLeft);
			});			
			if(typeof callBack=="function"){
				callBack();
			}
			this.onresizeLoading=true;		
		},
		allTrCheck:function(that,tableElement,callBack){
			$(that).toggleClass("checked");
			var isAll = $(that).hasClass("checked");
			if(isAll){
				$(that).removeClass("part_checked ");		
			}
			$(tableElement).find("tbody .wu-my-checkboxWrap").each(function(index, item) {
				var $tr = $(item).closest("tr");
				if (isAll) {
					$(item).addClass("checked");
					$tr.addClass("wu-active");
				} else {
					$(item).removeClass("checked");
					$tr.removeClass("wu-active");
				}
			})
			if (typeof callBack == "function") {
				callBack(that);
			}	
		},
		chooseTr(_this,callBack) {
			var isHas = false;
			if ($(_this).hasClass("wu-active")) {
				$(_this).removeClass("wu-active");
				$(_this).find(".wu-my-checkboxWrap").removeClass('checked');
				isHas = false;
			} else {
				$(_this).addClass("wu-active");
				$(_this).find(".wu-my-checkboxWrap").addClass('checked');
				isHas = true;
			}
			var checkedLen = $(_this).closest("table").find("tbody .wu-my-checkboxWrap.checked").length;
			var allLen = $(_this).closest("table").find("tbody .wu-my-checkboxWrap").length;
			var isAll = allLen == checkedLen;
			if (isAll) {
				$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").addClass("checked");
				$(_this).closest("table").find("thead tr").addClass("wu-active");
				$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").removeClass("part_checked");
			
			} else {
				$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").removeClass("checked");
				$(_this).closest("table").find("thead tr").removeClass("wu-active");
			
				if (checkedLen > 0) {
					$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").addClass("part_checked");
				} else {
					$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").removeClass("part_checked");
				}
			}
			
			if (typeof callBack == "function") {
				callBack(_this);
			}
			

		}
	}


})(window)