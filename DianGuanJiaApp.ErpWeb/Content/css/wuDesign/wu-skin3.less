
@import "./unit.less"; //变量：包括颜色 字体大小  圆角
.wu-radius2{
	border-radius: 2px;
}
.wu-radius4{
	border-radius: 4px;
}
.wu-radius6{
	border-radius: 6px;
}
.wu-radius8{
	border-radius: 8px;
}
.wu-background-white{
	background-color:#fff;
}
.wu-background-blue{
	background-color:@AColor;
}
.wu-background-red{
	background-color:@BColor;
}
.wu-background-yellow{
	background-color:@CColor;
}
.wu-background-green{
	background-color:@DColor;
}
.wu-width100{
	width: 100%;
}
.wu-heigt100{
	heigt: 100%;
}
.wu-fileWrap{

}
.wu-file-status{
	display: inline-flex;
	color: @c09;
	font-size: 14px;
	
	.wu-file-status-title{
		max-width: 240px;
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;
	}
	.icon-a-delete1x{
		display:none;
	}
	&:hover{
		background-color: @c01;
		.icon-a-delete1x{
			display:inline-block;
		}
	}
}
.wu-filePicWrap{
	width: 80px;
	height: 80px;
	&.wu-mid{
		width: 56px;
		height: 56px;
		.wu-filePic-title{
			display: none;
		}
	}
	&.wu-small{
		width: 32px;
		height: 32px;
		.wu-filePic-title{
			display: none;
		}
	}
	
}
.wu-up-filePic{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.wu-filePic-conent{
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 4px;
	border-radius: 6px;
	/* 中性色/Gray1 */
	background: @c01;
	box-sizing: border-box;
	/* 中性色/Gray3 */
	border: 0.5px dashed rgba(0, 0, 0, 0.14);
	color: @c09;
	font-size: 12px;
	cursor: pointer;
	&:hover{
		color: @AColor;
		border: 0.5px dashed @AColor;	
	}
	.wu-show-filePic{
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		display: none;
		img{
			width: 100%;
			height: 100%;
		}
	}
	.wu-file-operate{
		display: flex;
		justify-content: space-around;
		.wu-file-operate-item{
			display: flex;
			flex-direction: column;
			align-items: center;
			&:hover{
				color:@AColor;
			}
		}
	}
}
.wu-tooltip{
	position: relative;
}


.fendanContent-main-module{
	background-color: #fff;
}
	
    .compageWrap-module {
        display: flex;
        flex-direction: column;
        box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
        width: 100%;
        background-color: #fff;
        box-sizing: border-box;
        position: sticky;
        bottom: 0;
        .compageWrap-wrap {
            display: flex;
            justify-content: space-between;
        }
        .wu-checkboxWrap {
            color: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            .wu-my-checkboxWrap {
                margin-right: 8px;
            }
            .pL8 {
                padding-left: 8px;
            }
            .wu-my-checkboxNum{
                color: #0888FF;
                padding: 0 4px;
            }
        }
        .layui-myPage {
            .layui-box {
                margin: 12px 0 8px 0;
            }
        }
        .art-sticky-scroll {
            overflow: auto;
            position: sticky;
            bottom: 4px;
            .art-sticky-scroll-item {
                height: 1px;
                visibility: hidden;
            }
        }
    }




.wu-searchWrap.wu-searchWrap-container {
    display: grid;
    gap: 0 8px;
	background-color: #fff;
	.wu-searchWrap-contactTime {
        min-width:160px;
    }
    .wu-searchWrap-item {
        min-width:160px;
        padding-right: 0px;
		    height: 32px;
    }

	&.wu-initSearchWrap{
		position: relative;
		display: flex;
		.wu-searchWrap-item{
			 width: unset;
		}
		&::after{
			width: 100%;
			height: 100%;
			position: absolute;
			top:0;
			left:0;
			background-color: #fff;
			content: "";
			z-index: 1000;
			
		}
	}
}
.wu-table{
	
	&.stickyOperateTable{
	
	}	
	.wu-header-Sticky-th{
		position: sticky;
		right: 0;
		background-color: @c05;
	}
}
.wu-stickyTable-wrap{
	border-radius: 6px;
	border: 1px solid @c02;
	width: 100%;
	overflow-x: hidden;
	box-sizing: border-box;
	&.stickyOperateTable{
		.wu-header-Sticky-th{	
			&::after{
				position: absolute;
				content: "";
				height: 100%;
				left: 0;
				display: block;
				top: 0;
				width: 8px;
				opacity: 1;
				left: -8px;
				background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);				
			}
		}
		.wu-header-Sticky-td{
			&::after{
				position: absolute;
				content: "";
				height: 100%;
				background-color: red;
				left: 0;
				display: block;
				top: 0;
				width: 8px;
				opacity: 1;
				left: -8px;
				background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);		
			}
		}	
	}
}
.wu-sticky-table{
	border-collapse: collapse;
	border-spacing: 0;
	width: 100%;
	box-sizing: border-box;
	border-collapse: separate;
	thead tr th {
	    border-right: none;
	    // border-top: 1px solid @c02;
	    border-bottom: 1px solid @c02;
	    padding: 8px;
	    text-align: left;
	    font-weight: 500;
	    color: @c09;
		background-color: @c01;
		font-size: 14px;
		font-weight: 500;
		box-sizing: border-box;
	}
	tbody tr{
		td {
		    border-right: none;
		    border-bottom: 1px solid @c02;
		    padding: 8px;
			box-sizing: border-box;
		}   
		&:last-child{
			td{
				border-bottom:unset;				
			}
		}
		&:hover{
			background-color:@c01;
			.wu-header-Sticky-td{
				background-color:#f5f5f5;
			}
			
		}
		&.wu-active{
			background-color: @A1Color;
			.wu-header-Sticky-td{
				background-color:#ebf6ff;
			}
		}
	}
	.wu-header-Sticky-th{
		position: sticky;
		right: 0;
		background-color: #f5f5f5;
	}
	.wu-header-Sticky-td{
		position: sticky;
		right: 0;
		background-color: #fff;
	}	
}

.wu-radius-4{
	border-radius: 4px;
}
.wu-radius-BL-4{
	border-bottom-left-radius: 4px;
}
.wu-radius-BR-4{
	border-bottom-right-radius: 4px;
}
.wu-radius-TR-4{
	border-top-right-radius: 4px;
}
.wu-radius-TL-4{
	border-top-left-radius: 4px;
}

.wu-radius-6{
	border-radius: 6px;
}
.wu-radius-BL-6{
	border-bottom-left-radius: 6px;
}
.wu-radius-BR-6{
	border-bottom-right-radius: 6px;
}
.wu-radius-TR-6{
	border-top-right-radius: 6px;
}
.wu-radius-TL-6{
	border-top-left-radius: 6px;
}

.wu-radius-8{
	border-radius: 8px;
}
.wu-radius-BL-8{
	border-bottom-left-radius: 8px;
}
.wu-radius-BR-8{
	border-bottom-right-radius: 8px;
}
.wu-radius-TR-8{
	border-top-right-radius: 8px;
}
.wu-radius-TL-8{
	border-top-left-radius: 8px;
}
