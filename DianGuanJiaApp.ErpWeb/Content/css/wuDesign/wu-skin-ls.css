.jsls-skin .wu-btn {
  background-color: #0fb5c1;
  color: #fff;
}
.jsls-skin .wu-btn:hover {
  background-color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-btn:active {
  background-color: #03aab6;
}
.jsls-skin .wu-btn.disabled {
  background-color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.jsls-skin .wu-btn.wu-info.wu-one.wu-styleOne {
  background-color: rgba(15, 181, 193, 0.08);
  color: #0fb5c1;
}
.jsls-skin .wu-btn.wu-info.wu-one.wu-styleOne:hover {
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-btn.wu-primary:hover {
  border: 1px solid #0fb5c1;
  color: #fff;
}
.jsls-skin .wu-btn.wu-primary.wu-two {
  border: 1px solid #0fb5c1;
  background-color: #fff;
  color: #0fb5c1;
}
.jsls-skin .wu-btn.wu-primary.wu-two:hover {
  border: 1px solid rgba(15, 181, 193, 0.8);
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-btn.wu-primary.wu-two:active {
  border: 1px solid #03aab6;
  color: #03aab6;
}
.jsls-skin .wu-btn.wu-primary.wu-two.disabled {
  border-color: rgba(15, 181, 193, 0.3);
  color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.jsls-skin .wu-color-a {
  color: #0fb5c1;
}
.jsls-skin .wu-color-a:hover {
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-color-a.disabled {
  color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.jsls-skin .wu-inputWrap .wu-input:hover {
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-inputWrap .wu-input:focus {
  border: 1px solid #0fb5c1 !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-inputWrap.wu-formCombine .wu-inputWrap-content:hover {
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-inputWrap.wu-formCombine .wu-input:focus {
  box-shadow: unset!important;
  border: unset!important;
}
.jsls-skin .wu-inputWrap.wu-formCombine .wu-input {
  border: unset;
}
.jsls-skin .wu-inputWrap.wu-formCombine .wu-inputWrap-content:focus-within {
  border: 1px solid #0fb5c1;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-selectWrap:hover .wu-select {
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-selectWrap:focus {
  border: 1px solid #0fb5c1;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-selectWrap .wu-select:focus {
  border: 1px solid #0fb5c1;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .selectWrap.wu-select-skin .selectMore:hover {
  border: 1px solid #0fb5c1 !important;
}
.jsls-skin .selectWrap.wu-select-skin.active .selectMore {
  border: 1px solid #0fb5c1 !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
}
.jsls-skin .selectWrap .selectMore-choose li:nth-child(2) .selectMore-choose-title {
  color: #0fb5c1 !important;
}
.jsls-skin .wu-layui-select .layui-unselect:hover {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-layui-select .layui-unselect:focus {
  border-color: #0fb5c1 !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-radioWrap input[type=radio]:hover::before {
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-checkboxWrap input[type="checkbox"]:hover::after {
  border-color: #0fb5c1;
}
.jsls-skin .wu-checkboxWrap input[type="checkbox"]:checked::after {
  border: 1px solid #0fb5c1;
  background-color: #0fb5c1;
}
.jsls-skin .wu-radioWrap input[type=radio]:checked::after {
  background-color: #0fb5c1;
}
.jsls-skin .wu-radioWrap input[type=radio]:checked::before {
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-checkboxWrap.disabled input[type="checkbox"]:checked::after {
  border: 1px solid rgba(15, 181, 193, 0.08);
  background-color: rgba(15, 181, 193, 0.08);
}
.jsls-skin .wu-checkboxWrap.disabled input[type="checkbox"]:checked::before {
  border-left: 2px solid rgba(15, 181, 193, 0.3);
  border-bottom: 2px solid rgba(15, 181, 193, 0.3);
}
.jsls-skin .wu-my-checkboxWrap:hover .wu-my-checkbox {
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-my-radioWrap .wu-my-radio:hover {
  border-color: #0fb5c1;
}
.jsls-skin .wu-my-checkboxWrap.checked .wu-my-checkbox {
  border: 1px solid #0fb5c1;
  background-color: #0fb5c1;
}
.jsls-skin .wu-my-radioWrap.checked .wu-my-radio::after {
  background-color: #0fb5c1;
}
.jsls-skin .wu-my-radioWrap.checked .wu-my-radio {
  border-color: #0fb5c1;
}
.jsls-skin .wu-my-checkboxWrap.part_checked .wu-my-checkbox {
  border: 1px solid #0fb5c1;
  background-color: #0fb5c1;
}
.jsls-skin .wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox {
  background: #0fb5c1;
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-switch.active {
  border-color: #0fb5c1;
  background-color: #0fb5c1;
}
.jsls-skin .wu-switch.disabled.active {
  background: rgba(15, 181, 193, 0.3);
}
.jsls-skin .wu-my-checkboxWrap.disabled.checked .wu-my-checkbox {
  background: rgba(15, 181, 193, 0.08);
  border: 1px solid rgba(15, 181, 193, 0.08);
}
.jsls-skin .wu-my-checkboxWrap.disabled.checked .wu-my-checkbox:before {
  border-left: 2px solid rgba(15, 181, 193, 0.3);
  border-bottom: 2px solid rgba(15, 181, 193, 0.3);
}
.jsls-skin .wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox:before {
  background-color: rgba(15, 181, 193, 0.3);
}
.jsls-skin .wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox {
  background: rgba(15, 181, 193, 0.08);
  border: 1px solid rgba(15, 181, 193, 0.08);
}
.jsls-skin .wu-timeWrap .wu-selectWrap .wu-select {
  border-color: rgba(15, 181, 193, 0.3);
  background-color: rgba(15, 181, 193, 0.08);
}
.jsls-skin .wu-timeWrap .newinputSelectTime {
  background-color: rgba(15, 181, 193, 0.08);
  border-color: rgba(15, 181, 193, 0.3);
}
.jsls-skin .wu-timeWrap .wu-selectWrap.wu-form-mid .wu-timeWrap-left {
  border: 1px solid rgba(8, 136, 255, 0.2);
  background-color: rgba(15, 181, 193, 0.08);
}
.jsls-skin .wu-time .newinputSelectTime:hover {
  border-color: #0fb5c1;
}
.jsls-skin .wu-timeWrap:hover .wu-selectWrap .wu-timeWrap-left {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-skin .calender-cell.active,
.jsls-skin .wu-skin .calender-year-cell.active,
.jsls-skin .wu-skin .calender-mon-cell.active {
  background: #0fb5c1 !important;
}
.jsls-skin .newinputSelectTime .selectActiveTime > span {
  color: #0fb5c1;
}
.jsls-skin .newinputSelectTime .selectActiveTime > span.active {
  color: #fff;
  background-color: #0fb5c1;
}
.jsls-skin .wu-tabNav .wu-tabNav-li.wu-active,
.jsls-skin .wu-tabNav > li.wu-active,
.jsls-skin .wu-tabNav .wu-tabNav-li.active,
.jsls-skin .wu-tabNav > li.active {
  color: #0fb5c1;
}
.jsls-skin .wu-tabNav .wu-tabNav-li.wu-active::after,
.jsls-skin .wu-tabNav > li.wu-active::after,
.jsls-skin .wu-tabNav .wu-tabNav-li.active::after,
.jsls-skin .wu-tabNav > li.active::after {
  background-color: #0fb5c1;
}
.jsls-skin .wu-tabNav .wu-tabNav-li.wu-active,
.jsls-skin .wu-tabNav > li.wu-active,
.jsls-skin .wu-tabNav .wu-tabNav-li.active,
.jsls-skin .wu-tabNav > li.active {
  color: #0fb5c1;
}
.jsls-skin .wu-tabNav .wu-tabNav-li .wu-tabNav-li-span:hover,
.jsls-skin .wu-tabNav > li .wu-tabNav-li-span:hover,
.jsls-skin .wu-tabNav .wu-tabNav-li > span:hover,
.jsls-skin .wu-tabNav > li > span:hover {
  color: #0fb5c1;
}
.jsls-skin .wu-tabNav-two .wu-tabNav-li.wu-active,
.jsls-skin .wu-tabNav-two > li.wu-active,
.jsls-skin .wu-tabNav-two .wu-tabNav-li.layui-this,
.jsls-skin .wu-tabNav-two > li.layui-this {
  color: #0fb5c1;
}
.jsls-skin .wu-tabNav-two .wu-tabNav-li.wu-active,
.jsls-skin .wu-tabNav-two > li.wu-active,
.jsls-skin .wu-tabNav-two .wu-tabNav-li.layui-this,
.jsls-skin .wu-tabNav-two > li.layui-this {
  color: #0fb5c1;
  background: rgba(15, 181, 193, 0.08);
}
.jsls-skin .wu-tabNav-two .wu-tabNav-li:hover,
.jsls-skin .wu-tabNav-two > li:hover {
  background: rgba(15, 181, 193, 0.08);
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-tabNav-three .wu-tabNav-li.wu-active {
  color: #0fb5c1;
}
.jsls-skin .wu-tabNav-three .wu-tabNav-li:hover {
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-layui-tab .layui-tab-title .layui-this {
  color: #0fb5c1 !important;
}
.jsls-skin .wu-layui-tab .layui-tab-title .layui-this:after {
  background-color: #0fb5c1;
}
.jsls-skin .wu-tag.wu-processing {
  background-color: rgba(15, 181, 193, 0.08);
  color: #0fb5c1;
}
.jsls-skin .wu-tag.wu-processing.wu-dot {
  border: 0.5px solid rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-tag.wu-processing-dev {
  background-color: #0fb5c1;
}
.jsls-skin .wu-tag.wu-processing-dev.wu-dot {
  border: 0.5px solid rgba(8, 136, 255, 0.2);
  background-color: #fff;
  color: #0fb5c1;
}
.jsls-skin .wu-badge.wu-processing .wu-badge-dot {
  background-color: #0fb5c1;
}
.jsls-skin .wu-alert.wu-processing {
  background-color: rgba(15, 181, 193, 0.08);
  color: #0fb5c1;
  border: 0.5px solid rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-toast.wu-processing {
  color: #0fb5c1;
}
.jsls-skin .wu-dColor {
  color: #0fb5c1;
}
.jsls-skin .wu-dColor.wu-common-btn:hover {
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-page.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #0fb5c1;
}
.jsls-skin .wu-page.layui-laypage a:hover {
  color: #0fb5c1 !important;
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-commonPage .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #0fb5c1;
}
.jsls-skin .wu-commonPage .layui-laypage a:hover {
  color: #0fb5c1 !important;
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-page.layui-laypage select:hover {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-page.layui-laypage select:hover {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-commonPage .layui-laypage select:hover {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-commonPage .layui-laypage input:focus {
  border: 1px solid #0fb5c1 !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-commonPage .layui-laypage input:hover {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-page.layui-laypage input:hover {
  border-color: #0fb5c1 !important;
}
.jsls-skin .wu-page.layui-laypage input:focus {
  border: 1px solid #0fb5c1 !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-page.layui-laypage button:hover {
  border: 1px solid #0fb5c1;
  color: #0fb5c1;
}
.jsls-skin .wu-commonPage .layui-laypage button:hover {
  border: 1px solid #0fb5c1;
  color: #0fb5c1;
}
.jsls-skin .wu-header-tip .wu-header-tip-icon {
  background-color: #0fb5c1;
}
.jsls-skin .wu-filePic-conent:hover {
  color: #0fb5c1;
  border: 0.5px dashed #0fb5c1;
}
.jsls-skin .wu-dailog .layui-layer-btn a:nth-child(1) {
  background: #0fb5c1;
  border: 1px solid #0fb5c1;
}
.jsls-skin .wu-modal .wu-modal-icon.icon-a-info-circle-filled1x {
  color: #0fb5c1;
}
.jsls-skin .wu-dailog .layui-layer-btn a.layui-layer-btn0:hover {
  background: rgba(15, 181, 193, 0.8) !important;
  border: 1px solid rgba(15, 181, 193, 0.8) !important;
}
.jsls-skin .wu-dailog .layui-layer-btn a:last-child:hover {
  border-color: rgba(15, 181, 193, 0.8);
  color: rgba(15, 181, 193, 0.8);
}
.jsls-skin .wu-dailog.wu-warn .layui-layer-btn a.layui-layer-btn0:hover {
  background: #ea572e !important;
  border: 1px solid #ea572e !important;
}
.jsls-skin .wu-dailog.wu-warn .layui-layer-btn a.layui-layer-btn0:hover {
  background: #ea572e !important;
  border: 1px solid #ea572e !important;
}
.jsls-skin .wu-skin .newcalender-footer-button:hover {
  border: 1px solid rgba(8, 136, 255, 0.2);
  color: #0fb5c1;
  background-color: rgba(8, 136, 255, 0.2);
}
.jsls-skin .wu-tableWrap .wu-table-one tbody tr.wu-active {
  background: rgba(15, 181, 193, 0.08);
}
.wu-btn.wu-six {
  background-color: #0fb5c1;
}
.wu-btn.wu-six:hover {
  background: rgba(15, 181, 193, 0.8);
}
.wu-btn.wu-six:active {
  background-color: #03aab6;
}
.wu-btn.wu-six.disabled {
  background-color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-six.wu-info.wu-styleOne {
  background-color: rgba(15, 181, 193, 0.08);
  color: #0fb5c1;
}
.wu-btn.wu-six.wu-info.wu-styleOne:hover {
  color: rgba(15, 181, 193, 0.8);
}
.wu-btn.wu-six.wu-primary {
  border: 1px solid #0fb5c1;
  background-color: #fff;
  color: #0fb5c1;
}
.wu-btn.wu-six.wu-primary:hover {
  border: 1px solid rgba(15, 181, 193, 0.8);
  color: rgba(15, 181, 193, 0.8);
}
.wu-btn.wu-six.wu-primary:active {
  border: 1px solid #03aab6;
  color: #03aab6;
}
.wu-btn.wu-six.wu-primary.disabled {
  border-color: rgba(15, 181, 193, 0.3);
  color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.wu-color-l {
  color: #0fb5c1;
}
.wu-color-l.hover {
  color: rgba(15, 181, 193, 0.8);
}
.wu-color-l.active {
  color: #03aab6;
}
.wu-color-l.disabled {
  color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.wu-color-l.wu-operate {
  cursor: pointer;
}
.wu-color-l.wu-operate:hover {
  color: rgba(15, 181, 193, 0.8);
}
.wu-color-l.wu-operate:active {
  color: #03aab6;
}
.wu-color-l.wu-operate.disabled {
  color: rgba(15, 181, 193, 0.3);
  cursor: not-allowed;
}
.wu-color-l.wu-border {
  border: 1px solid rgba(8, 136, 255, 0.2);
}
.wu-color-l.wu-background {
  background-color: rgba(15, 181, 193, 0.08);
}
