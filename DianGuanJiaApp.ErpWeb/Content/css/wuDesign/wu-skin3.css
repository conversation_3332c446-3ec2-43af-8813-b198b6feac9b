.wu-radius2 {
  border-radius: 2px;
}
.wu-radius4 {
  border-radius: 4px;
}
.wu-radius6 {
  border-radius: 6px;
}
.wu-radius8 {
  border-radius: 8px;
}
.wu-background-white {
  background-color: #fff;
}
.wu-background-blue {
  background-color: #0888ff;
}
.wu-background-red {
  background-color: #ea572e;
}
.wu-background-yellow {
  background-color: #f18c03;
}
.wu-background-green {
  background-color: #73ac1f;
}
.wu-width100 {
  width: 100%;
}
.wu-heigt100 {
  heigt: 100%;
}
.wu-file-status {
  display: inline-flex;
  color: rgba(0, 0, 0, 0.9);
  font-size: 14px;
}
.wu-file-status .wu-file-status-title {
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.wu-file-status .icon-a-delete1x {
  display: none;
}
.wu-file-status:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
.wu-file-status:hover .icon-a-delete1x {
  display: inline-block;
}
.wu-filePicWrap {
  width: 80px;
  height: 80px;
}
.wu-filePicWrap.wu-mid {
  width: 56px;
  height: 56px;
}
.wu-filePicWrap.wu-mid .wu-filePic-title {
  display: none;
}
.wu-filePicWrap.wu-small {
  width: 32px;
  height: 32px;
}
.wu-filePicWrap.wu-small .wu-filePic-title {
  display: none;
}
.wu-up-filePic {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.wu-filePic-conent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
  /* 中性色/Gray1 */
  background: rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  /* 中性色/Gray3 */
  border: 0.5px dashed rgba(0, 0, 0, 0.14);
  color: rgba(0, 0, 0, 0.9);
  font-size: 12px;
  cursor: pointer;
}
.wu-filePic-conent:hover {
  color: #0888ff;
  border: 0.5px dashed #0888ff;
}
.wu-filePic-conent .wu-show-filePic {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: none;
}
.wu-filePic-conent .wu-show-filePic img {
  width: 100%;
  height: 100%;
}
.wu-filePic-conent .wu-file-operate {
  display: flex;
  justify-content: space-around;
}
.wu-filePic-conent .wu-file-operate .wu-file-operate-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.wu-filePic-conent .wu-file-operate .wu-file-operate-item:hover {
  color: #0888ff;
}
.wu-tooltip {
  position: relative;
}
.fendanContent-main-module {
  background-color: #fff;
}
.compageWrap-module {
  display: flex;
  flex-direction: column;
  box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  position: sticky;
  bottom: 0;
}
.compageWrap-module .compageWrap-wrap {
  display: flex;
  justify-content: space-between;
}
.compageWrap-module .wu-checkboxWrap {
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.compageWrap-module .wu-checkboxWrap .wu-my-checkboxWrap {
  margin-right: 8px;
}
.compageWrap-module .wu-checkboxWrap .pL8 {
  padding-left: 8px;
}
.compageWrap-module .wu-checkboxWrap .wu-my-checkboxNum {
  color: #0888FF;
  padding: 0 4px;
}
.compageWrap-module .layui-myPage .layui-box {
  margin: 12px 0 8px 0;
}
.compageWrap-module .art-sticky-scroll {
  overflow: auto;
  position: sticky;
  bottom: 4px;
}
.compageWrap-module .art-sticky-scroll .art-sticky-scroll-item {
  height: 1px;
  visibility: hidden;
}
.wu-searchWrap.wu-searchWrap-container {
  display: grid;
  gap: 0 8px;
  background-color: #fff;
}
.wu-searchWrap.wu-searchWrap-container .wu-searchWrap-contactTime {
  min-width: 160px;
}
.wu-searchWrap.wu-searchWrap-container .wu-searchWrap-item {
  min-width: 160px;
  padding-right: 0px;
  height: 32px;
}
.wu-searchWrap.wu-searchWrap-container.wu-initSearchWrap {
  position: relative;
  display: flex;
}
.wu-searchWrap.wu-searchWrap-container.wu-initSearchWrap .wu-searchWrap-item {
  width: unset;
}
.wu-searchWrap.wu-searchWrap-container.wu-initSearchWrap::after {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #fff;
  content: "";
  z-index: 1000;
}
.wu-table .wu-header-Sticky-th {
  position: sticky;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.wu-stickyTable-wrap {
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.09);
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}
.wu-stickyTable-wrap.stickyOperateTable .wu-header-Sticky-th::after {
  position: absolute;
  content: "";
  height: 100%;
  left: 0;
  display: block;
  top: 0;
  width: 8px;
  opacity: 1;
  left: -8px;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}
.wu-stickyTable-wrap.stickyOperateTable .wu-header-Sticky-td::after {
  position: absolute;
  content: "";
  height: 100%;
  background-color: red;
  left: 0;
  display: block;
  top: 0;
  width: 8px;
  opacity: 1;
  left: -8px;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}
.wu-sticky-table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  box-sizing: border-box;
  border-collapse: separate;
}
.wu-sticky-table thead tr th {
  border-right: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  padding: 8px;
  text-align: left;
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(0, 0, 0, 0.04);
  font-size: 14px;
  font-weight: 500;
  box-sizing: border-box;
}
.wu-sticky-table tbody tr td {
  border-right: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  padding: 8px;
  box-sizing: border-box;
}
.wu-sticky-table tbody tr:last-child td {
  border-bottom: unset;
}
.wu-sticky-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
.wu-sticky-table tbody tr:hover .wu-header-Sticky-td {
  background-color: #f5f5f5;
}
.wu-sticky-table tbody tr.wu-active {
  background-color: rgba(8, 136, 255, 0.08);
}
.wu-sticky-table tbody tr.wu-active .wu-header-Sticky-td {
  background-color: #ebf6ff;
}
.wu-sticky-table .wu-header-Sticky-th {
  position: sticky;
  right: 0;
  background-color: #f5f5f5;
}
.wu-sticky-table .wu-header-Sticky-td {
  position: sticky;
  right: 0;
  background-color: #fff;
}
.wu-radius-4 {
  border-radius: 4px;
}
.wu-radius-BL-4 {
  border-bottom-left-radius: 4px;
}
.wu-radius-BR-4 {
  border-bottom-right-radius: 4px;
}
.wu-radius-TR-4 {
  border-top-right-radius: 4px;
}
.wu-radius-TL-4 {
  border-top-left-radius: 4px;
}
.wu-radius-6 {
  border-radius: 6px;
}
.wu-radius-BL-6 {
  border-bottom-left-radius: 6px;
}
.wu-radius-BR-6 {
  border-bottom-right-radius: 6px;
}
.wu-radius-TR-6 {
  border-top-right-radius: 6px;
}
.wu-radius-TL-6 {
  border-top-left-radius: 6px;
}
.wu-radius-8 {
  border-radius: 8px;
}
.wu-radius-BL-8 {
  border-bottom-left-radius: 8px;
}
.wu-radius-BR-8 {
  border-bottom-right-radius: 8px;
}
.wu-radius-TR-8 {
  border-top-right-radius: 8px;
}
.wu-radius-TL-8 {
  border-top-left-radius: 8px;
}
