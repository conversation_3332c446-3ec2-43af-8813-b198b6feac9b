
@import "./unit.less"; //变量：包括颜色 字体大小  圆角

.jsls-skin {

    .wu-btn{
		background-color:@LColor;
		color:#fff;
		&:hover {
		    background-color: @L8Color;
			
		}	
		&:active {
		    background-color: @LBColor;
		}
		&.disabled {
		    background-color: @L3Color;
		    cursor: not-allowed;
		}
		&.wu-info {
		    &.wu-one {
		        &.wu-styleOne {
					background-color: @L1Color;
					color: @LColor;
					&:hover {
					    color: @L8Color;
					}
				}
			}
		}	
		&.wu-primary{
			&:hover {
			    border: 1px solid @LColor;
			    color: #fff;
			}
			&.wu-two {
			    border: 1px solid @LColor;
			    background-color: #fff;
			    color: @LColor;
				&:hover {
				    border: 1px solid @L8Color;;
				    color: @L8Color;;
				}
				&:active {
				    border: 1px solid @LBColor;
				    color: @LBColor;
				}
				&.disabled {
				    border-color: @L3Color;
				    color: @L3Color;
				    cursor: not-allowed;
				}
			}
		}
		
	}
	.wu-color-a{
		color: @LColor;
		&:hover {
			color: @L8Color;
		}
		&.disabled {
			color: @L3Color;
			cursor: not-allowed;
		}
	}
	
	.wu-inputWrap {
		.wu-input:hover {
		    border: 1px solid @LColor;
		}
		.wu-input:focus {
		    border: 1px solid @LColor !important;
		    box-shadow: 0px 0px 0px 2px @L2Color;
		}
		&.wu-formCombine{

		    .wu-inputWrap-content:hover {
		        border: 1px solid @LColor;
		    }
			.wu-input:focus {
			    box-shadow: unset!important;
				border: unset!important;			
			}
			.wu-input {
			    border: unset;
			}
			.wu-inputWrap-content:focus-within {
			    border: 1px solid @LColor;
			    box-shadow: 0px 0px 0px 2px @L2Color;
			}
			
		}	
	}
		
	.wu-selectWrap {
		&:hover{
			.wu-select{
				border: 1px solid @LColor;				
			}
		} 
		&:focus {
		    border: 1px solid @LColor;
		    box-shadow: 0px 0px 0px 2px @L2Color;
		}
		.wu-select:focus {
		    border: 1px solid @LColor;
		    box-shadow: 0px 0px 0px 2px @L2Color;
		}
	}
	.selectWrap.wu-select-skin .selectMore:hover {
	    border: 1px solid @LColor !important;
	}
	.selectWrap.wu-select-skin.active .selectMore {
	    border: 1px solid @LColor !important;
	    box-shadow: 0px 0px 0px 2px  @L2Color !important;
	}
	.selectWrap{
		.selectMore-choose{
			li:nth-child(2){
				.selectMore-choose-title{
					color: @LColor !important;
				}
			}
		}
	} 
	.wu-layui-select .layui-unselect:hover {
	    border-color: @LColor !important;
	}
	.wu-layui-select .layui-unselect:focus{
		border-color: @LColor !important;
		box-shadow: 0px 0px 0px 2px @L2Color;
	}
	.wu-radioWrap input[type=radio]:hover::before {
	    border: 1px solid @LColor;
	}
	.wu-checkboxWrap input[type="checkbox"]:hover::after {
	    border-color: @LColor;
	}
	.wu-checkboxWrap input[type="checkbox"]:checked::after {
	    border: 1px solid @LColor;
	    background-color: @LColor;
	}
	.wu-radioWrap input[type=radio]:checked::after {
	    background-color: @LColor;
	}
	.wu-radioWrap input[type=radio]:checked::before {
	    border: 1px solid @LColor;
	}
	.wu-checkboxWrap.disabled input[type="checkbox"]:checked::after {
	    border: 1px solid @L1Color;
	    background-color: @L1Color;
	}
	.wu-checkboxWrap.disabled input[type="checkbox"]:checked::before {
	    border-left: 2px solid @L3Color;
	    border-bottom: 2px solid @L3Color;
	}
	.wu-my-checkboxWrap:hover .wu-my-checkbox {
	    border: 1px solid @LColor;
	}
	.wu-my-radioWrap .wu-my-radio:hover {
	    border-color: @LColor;
	}
	.wu-my-checkboxWrap.checked .wu-my-checkbox {
	    border: 1px solid @LColor;
	    background-color: @LColor;
	}
	.wu-my-radioWrap.checked .wu-my-radio::after {
	    background-color:@LColor;
	}
	.wu-my-radioWrap.checked .wu-my-radio {
	    border-color: @LColor;
	}
	.wu-my-checkboxWrap.part_checked .wu-my-checkbox {
	    border: 1px solid @LColor;
	    background-color: @LColor;
	}
	.wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox {
	    background:@LColor;
	    border: 1px solid @LColor;
	}
	.wu-switch.active {
	    border-color: @LColor;
	    background-color: @LColor;
	}
	.wu-switch.disabled.active {
		background: @L3Color;
	}
	.wu-my-checkboxWrap.disabled.checked .wu-my-checkbox {
		background: @L1Color;
		border: 1px solid @L1Color;
	}
	.wu-my-checkboxWrap.disabled.checked .wu-my-checkbox:before {
	    border-left: 2px solid @L3Color;
	    border-bottom: 2px solid @L3Color;
	}
	.wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox:before {
	    background-color: @L3Color;
	}
	.wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox {
		background: @L1Color;
		border: 1px solid @L1Color;
	}
	.wu-timeWrap .wu-selectWrap .wu-select {
	    border-color: @L3Color;
	    background-color: @L1Color;
	}
	.wu-timeWrap .newinputSelectTime {
	    background-color: @L1Color;
	    border-color: @L3Color;
	}
	.wu-timeWrap .wu-selectWrap.wu-form-mid .wu-timeWrap-left {
	    border: 1px solid @L2Color;;
	    background-color: @L1Color;;
	}
	.wu-time .newinputSelectTime:hover {
	    border-color:@LColor;
	}
	.wu-timeWrap:hover .wu-selectWrap .wu-timeWrap-left {
	    border-color: @LColor !important;
	}
	.wu-skin .calender-cell.active, .wu-skin .calender-year-cell.active, .wu-skin .calender-mon-cell.active {
	    background: @LColor!important;
	}
	.newinputSelectTime .selectActiveTime>span {
	    color: @LColor;
	}
	.newinputSelectTime .selectActiveTime > span.active {
	    color: #fff;
		background-color:@LColor;
	}
	.wu-tabNav .wu-tabNav-li.wu-active, .wu-tabNav > li.wu-active, .wu-tabNav .wu-tabNav-li.active, .wu-tabNav > li.active {
	    color: @LColor;
	}
	.wu-tabNav .wu-tabNav-li.wu-active::after, .wu-tabNav > li.wu-active::after, .wu-tabNav .wu-tabNav-li.active::after, .wu-tabNav > li.active::after {
	    background-color: @LColor;
	}
	.wu-tabNav .wu-tabNav-li.wu-active, .wu-tabNav > li.wu-active, .wu-tabNav .wu-tabNav-li.active, .wu-tabNav > li.active {
	    color: @LColor;
	}
	.wu-tabNav .wu-tabNav-li .wu-tabNav-li-span:hover, .wu-tabNav > li .wu-tabNav-li-span:hover, .wu-tabNav .wu-tabNav-li > span:hover, .wu-tabNav > li > span:hover {
	    color: @LColor;
	}
	.wu-tabNav-two .wu-tabNav-li.wu-active, .wu-tabNav-two > li.wu-active, .wu-tabNav-two .wu-tabNav-li.layui-this, .wu-tabNav-two > li.layui-this {
	    color: @LColor;
	}
	.wu-tabNav-two .wu-tabNav-li.wu-active, .wu-tabNav-two > li.wu-active, .wu-tabNav-two .wu-tabNav-li.layui-this, .wu-tabNav-two > li.layui-this {
	    color: @LColor;
	    background: @L1Color;
	} 
	.wu-tabNav-two .wu-tabNav-li:hover, .wu-tabNav-two > li:hover {
	    background: @L1Color;
	    color: @L8Color;
	}  
	.wu-tabNav-three .wu-tabNav-li.wu-active {
	    color: @LColor;
	}
	.wu-tabNav-three .wu-tabNav-li:hover {
	    color: @L8Color;
	} 
	.wu-layui-tab .layui-tab-title .layui-this {
	    color: @LColor!important;
	}
	.wu-layui-tab .layui-tab-title .layui-this:after {
	    background-color: @LColor;
	}
	.wu-tag.wu-processing {
	    background-color:  @L1Color;
	    color: @LColor;
	}
	.wu-tag.wu-processing.wu-dot {
	    border: 0.5px solid  @L2Color;
	}
	.wu-tag.wu-processing-dev {
	    background-color: @LColor;
	}
	.wu-tag.wu-processing-dev.wu-dot {
	    border: 0.5px solid @L2Color;
	    background-color: #fff;
	    color: @LColor;
	}
	.wu-badge.wu-processing .wu-badge-dot {
	    background-color: @LColor;
	}
	.wu-alert.wu-processing {
	    background-color: @L1Color;
	    color: @LColor;
	    border: 0.5px solid @L2Color;
	}
	.wu-toast.wu-processing {
	    color: @LColor;
	}
	.wu-dColor {
	    color: @LColor;
	}
	.wu-dColor.wu-common-btn:hover {
	    color: @L8Color;
	}
	.wu-page.layui-laypage .layui-laypage-curr .layui-laypage-em {
	    background-color: @LColor;
	}
	.wu-page.layui-laypage a:hover {
	    color: @LColor!important;
	    border-color: @LColor!important;
	}
	.wu-commonPage .layui-laypage .layui-laypage-curr .layui-laypage-em {
	    background-color: @LColor;
	}
	.wu-commonPage .layui-laypage a:hover {
	    color: @LColor !important;
	    border-color: @LColor !important;
	}
	.wu-page.layui-laypage select:hover {
	    border-color: @LColor !important;
	}
	.wu-page.layui-laypage select:hover {
	    border-color: @LColor !important;
	}
	.wu-commonPage .layui-laypage select:hover {
	    border-color: @LColor !important;
	}
	.wu-commonPage .layui-laypage input:focus {
	    border: 1px solid @LColor !important;
	    box-shadow: 0px 0px 0px 2px @L2Color;
	}
	.wu-commonPage .layui-laypage input:hover {
	    border-color: @LColor !important;
	}
	.wu-page.layui-laypage input:hover {
	    border-color:  @LColor !important;
	}
	.wu-page.layui-laypage input:focus {
	    border: 1px solid @LColor  !important;
	    box-shadow: 0px 0px 0px 2px @L2Color ;
	}
	.wu-page.layui-laypage button:hover {
	    border: 1px solid @LColor;
	    color: @LColor;
	}
	.wu-commonPage .layui-laypage button:hover {
	    border: 1px solid @LColor;
	    color: @LColor;
	}
	.wu-header-tip .wu-header-tip-icon {
	    background-color: @LColor;
	}
	.wu-filePic-conent:hover {
	    color: @LColor;
	    border: 0.5px dashed @LColor;
	}
	.wu-dailog .layui-layer-btn a:nth-child(1) {
	    background: @LColor;
	    border: 1px solid @LColor;
	}
	.wu-modal .wu-modal-icon.icon-a-info-circle-filled1x{
		color: @LColor;
	}
	.wu-dailog .layui-layer-btn a.layui-layer-btn0:hover {
	    background: @L8Color !important;
	    border: 1px solid @L8Color !important;
	}
	.wu-dailog .layui-layer-btn a:last-child:hover {
	    border-color: @L8Color;
	    color: @L8Color;
	}
	.wu-dailog.wu-warn .layui-layer-btn a.layui-layer-btn0:hover {
		background: #ea572e !important;
		border: 1px solid #ea572e !important;

	}
	.wu-dailog.wu-warn .layui-layer-btn a.layui-layer-btn0:hover {
		background: #ea572e !important;
		border: 1px solid #ea572e !important;
	}
	.wu-skin .newcalender-footer-button:hover {
	    border: 1px solid @L2Color;
	    color: @LColor;
	    background-color: @L2Color;
	}
	.wu-tableWrap .wu-table-one tbody tr.wu-active {
	    background: @L1Color;
	}
}
.wu-btn{
	&.wu-six{
		background-color: @LColor;
		
		&:hover {
		    background: @L8Color;
		}
		
		&:active {
		    background-color: @LBColor;
		}
		
		&.disabled {
		    background-color: @L3Color;
		    cursor: not-allowed;
		}
		
		&.wu-info {
		
		    &.wu-styleOne {
				background-color: @L1Color;
				color: @LColor;
				&:hover {
				    color: @L8Color;
				}
			}		
		}
		&.wu-primary{
			border: 1px solid @LColor;
		    background-color: #fff;
		    color: @LColor;
			&:hover {
			    border: 1px solid @L8Color;;
			    color: @L8Color;;
			}
			&:active {
			    border: 1px solid @LBColor;
			    color: @LBColor;
			}
			&.disabled {
			    border-color: @L3Color;
			    color: @L3Color;
			    cursor: not-allowed;
			}
		}	
		
	}

}
 .wu-color-l{
	 color:@LColor;
	 &.hover {
	     color: @L8Color;
	 }
	 
	 &.active {
	     color: @LBColor;
	 }
	 
	 &.disabled {
	     color: @L3Color;
	     cursor: not-allowed;
	 }
	 
	 &.wu-operate {
		 
		 cursor: pointer;		 
		 &:hover {
		     color: @L8Color;
		 }
		 
		 &:active {
		     color: @LBColor;
		 }
		 
		 &.disabled {
		     color: @L3Color;
		     cursor: not-allowed;
		 }
		 
	 }
	 &.wu-border{
		 border:1px solid @L2Color;
	 }
	 &.wu-background{
	 	background-color: @L1Color;
	 }
 }