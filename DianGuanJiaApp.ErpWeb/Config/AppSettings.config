<?xml version="1.0" encoding="utf-8"?>
<appSettings>
	<add key="owin:AppStartup" value="false" />
	<add key="owin:AutomaticAppStartup" value="false"/>

	<add key="ids" value="" />
	<add key="SearchShopId" value="1" />
	<add key="Days" value="1,2,3,4,5" />
	<add key="CollectionNames" value="LogForOperator20200610" />

	<add key="webpages:Version" value="*******" />
	<add key="webpages:Enabled" value="false" />
	<add key="ClientValidationEnabled" value="true" />
	<add key="UnobtrusiveJavaScriptEnabled" value="true" />

	<!-- 是否为切换用户环境工具 -->
	<add key="IsSwitchVersionTool" value="1" />

	<!--校验是否开通采购金时，1=直接返回false，0=正常校验 -->
	<add key="IsReturnNotOpenWangShangPay" value="0" />

	<!--是否启用地址强校验-->
	<add key="IsTestReceiverChange" value="0" />
	<!--是否启用分销系统省市区短地址查询-->
	<add key="IsPinduoduoUseShortAddress" value="1" />

	<!--是否使用抖音新订单接口（全量发布后弃用）-->
	<add key="IsUseDyNewInterface" value="1" />
	<!--商品列表是否禁止检测-->
	<add key="IsSyncDisabledByProductExamine" value="0" />

	<!--物流预警更新起始时间设置(不加载旧数据)-->
	<add key="LogisticUpdate:StartTime" value="2020-08-05" />
	<!--物流预警更新间隔时间(s)-->
	<add key="LogisticUpdate:IntervalTime" value="300" />
	<!--预发货实时更新最大值-->
	<add key="Preordain:MaxUpdateLogistic" value="50" />
	<!--操作超时写入日志时间(s):0 表示都要记录-->
	<add key="LogTimeOut" value="0" />
	<!--不需要写入日志数据库的操作(ExpressPrint,SendPrint,SyncOrder,SearchOrder)-->
	<add key="NotNeedLogOperators" value="" />
	<!--无图显示URL-->
	<add key="NoPicUrl" value="http://img.dgjapp.com/nopicurl.jpg" />

	<!--子账号权限版本 关联到菜单版本和系统权限版本-->
	<add key="FxPermissionVersion" value="V250902" />

	<!--排除操作类型查询-->
	<add key="ExceptOperatorType" value="增量同步订单,插入未评论的订单" />
	<!--设置店铺过期，多少天提示-->
	<add key="EndServiceDay" value="5" />
	<!--同步是否被禁用（同步订单、商品）-->
	<add key="IsSyncDisabled" value="1" />
	<!--是否启用本地连接数据库调试模式，1为启用，用于本地调试-->
	<add key="IsLocalDbDebug" value="1" />
	<!--是否启用调试模式，1为启用-->
	<add key="IsDebug" value="1" />
	<!--分单系统是否异步同步订单-->
	<add key="IsAsyncSyncOrder" value="1" />
	<!--是否是抖音电子面单测试，1为启用-->
	<add key="IsTouTiaoWaybillTest" value="0" />
	<!--平台类型：TB 1688 sj1688 pdd WeBus other-->
	<add key="Platform" value="other" />
	<!--售后平台类型 多个用逗号隔开，全部填写all-->
	<add key="PlatformAfterSale" value="TouTiao,Pinduoduo,Alibaba,Taobao,AlibabaC2M,KuaiShou,Jingdong,WxVideo,TaobaoMaiCai,Other_Heliang,Other_JuHaoMai,BiliBili" />
	<!--是否写数据库日志-->
	<add key="IsWriteDbLog" value="0" />
	<!--是否使用1688的新接口-->
	<add key="IsUse1688NewApi" value="1" />
	<!--接口请求超时警告时间-->
	<add key="ApiExecuteWarningSeconds" value="30" />
	<!--限制翻页深度最大页数-->
	<add key="PageMaxSize" value="500" />
	<!--<add key="PageMaxSize" value="500" />-->

	<add key="ConnectionString" value="server=*************;uid=sa;pwd=**********;database=PrintDB;" />

	<!--内网图片服务器-->
	<add key="ImageServer" value="http://img.dgjapp.com/" />
	<!--外网图片服务器-->
	<add key="ImageServerOut" value="http://img.dgjapp.com/" />

	<!--淘宝Api是否走代理(本地调试不支持)-->
	<add key="Taobao:ApiUseProxy" value="true"/>


	<!--淘宝Api代理地址-->
	<add key="ApiProxy:Taobao" value="http://taobaoproxy.dgjapp.com/Taobao"/>

	<!--1688授权回调加密密钥-->
	<add key="Alibaba:AuthCallbackKey" value="grewoiga" />
	<!--登录cookie加密秘钥-->
	<add key="LoginCookieEncryptKey" value="grewoiga" />

	<!--淘宝应用(电子面单)key-->
	<!--********-->
	<add key="Taobao_Waybill:AppKey" value="********" />
	<!--淘宝应用(电子面单)秘钥-->
	<add key="Taobao_Waybill:AppSecret" value="ca0f2dbf9155a25a5883fc6b3f3421c8" />
	<!--淘宝应用(电子面单)授权回调地址-->
	<add key="Taobao_Waybill:Auth_RedirectUrl" value="http://img.dgjapp.com/" />

	<!--菜鸟(电子面单)key-->
	<!--********-->
	<add key="CaiNiao:AppKey" value="566703" />
	<!--菜鸟(电子面单)秘钥-->
	<add key="CaiNiao:AppSecret" value="1031469n4j3482q09ZhU0JP1AYbxu87d" />
	<!--菜鸟(电子面单)授权回调地址  如有修改，请更新下授权回调的程序-->
	<add key="CaiNiao:RedirectUrl" value="https://www.dgjapp.com:30001" />
	<!--菜鸟(电子面单)授权 Url-->
	<add key="CaiNiao:Url" value="http://lcp.cloud.cainiao.com" />
	<!--菜鸟(电子面单)请求api的 Url-->
	<add key="CaiNiao:DailyUrl" value="http://link.cainiao.com/gateway/link.do" />

	<!--淘宝应用key-->
	<!--********-->
	<add key="Taobao:AppKey" value="********" />
	<!--淘宝应用秘钥-->
	<!--ca0f2dbf9155a25a5883fc6b3f3421c8-->
	<add key="Taobao:AppSecret" value="ca0f2dbf9155a25a5883fc6b3f3421c8" />
	<!--淘宝api rul-->
	<!--http://gw.api.taobao.com/router/rest-->
	<add key="Taobao:ApiUrl" value="http://gw.api.taobao.com/router/rest" />
	<!--淘宝授权获取授权 api rul-->
	<!--https://oauth.taobao.com/authorize-->
	<add key="Taobao:AuthUrl" value="https://oauth.taobao.com/authorize" />
	<!--淘宝授权获取token api url-->
	<!--https://oauth.taobao.com/token-->
	<add key="Taobao:Auth_GetToken_Url" value="https://oauth.tbsandbox.com/token" />
	<!--淘宝授权回调地址-->
	<add key="Taobao:Auth_RedirectUrl" value="http://kdimg.dgjapp.com/" />
	<!--http://**************/tbAuth.aspx-->
	<!--http://localhost/TaobaoAuth-->

	<!--阿里巴巴应用key-->
	<add key="Alibaba:AppKey" value="1004884" />
	<!--阿里巴巴应用秘钥-->
	<add key="Alibaba:AppSecret" value="ZoLb9v!Mwin" />
	<!--淘宝买菜应用key-->
	<add key="Alibaba:TbmcAppKey" value="34704816" />
	<!--淘宝买菜应用秘钥-->
	<add key="Alibaba:TbmcAppSecret" value="5d9bc7b5562e0862de69623c73169175" />
	<!--阿里巴巴api url-->
	<add key="Alibaba:ApiUrl" value="" />
	<!--阿里巴巴授权 api url-->
	<add key="Alibaba:Auth_Url" value="" />
	<!--阿里巴巴授权获取token api url-->
	<add key="Alibaba:Auth_GetToken_Url" value="" />
	<!--阿里巴巴授权回调地址-->
	<add key="Alibaba:Auth_RedirectUrl" value="" />

	<!--阿里巴巴C2M应用key-->
	<add key="AlibabaC2M:AppKey" value="3531515" />
	<!--阿里巴巴C2M应用秘钥-->
	<add key="AlibabaC2M:AppSecret" value="pkV6U2U7CA" />

	<!--阿里巴巴轻应用key-->
	<add key="AlibabaQing:AppKey" value="5382198" />
	<!--阿里巴巴轻应用秘钥-->
	<add key="AlibabaQing:AppSecret" value="pkV6U2U7CA" />

	<!--拼多多应用key-->
	<add key="Pinduoduo:AppKey" value="afabb0e68b3443b9a0dda11c1442a042" />
	<!--拼多多应用秘钥-->
	<add key="Pinduoduo:AppSecret" value="bf3a8a74b9977cf0cc65e21a4cbfbca9f7962093" />

	<!--拼多多电子面单应用key-->
	<add key="PddWaybill:AppKey" value="99af635d613046799dcbab8bd684a15a" />
	<!--拼多多电子面单应用秘钥-->
	<add key="PddWaybill:AppSecret" value="24d3666b794159058fa5a3275543d560c49d8510" />

	<!--众邮电子面单应用key-->
	<add key="ZYKDWaybill:AppKey" value="de341bea955d484bbaae762a163da871" />
	<!--众邮电子面单应用秘钥-->
	<add key="ZYKDWaybill:AppSecret" value="0eca00386a9f48999b1e7b58248c0fd8" />
	<!--众邮电子面单授权Token-->
	<add key="ZYKDWaybill:Token" value="13e5176b30da443e9b4cfd9a62a81cbc" />
	<!--refresh token为： 9efd0f89deb0409f9560bc59d5ab57cb-->
	<!--开放平台账号-->
	<add key="ZYKDWaybill:UserCode" value="18603065384" />

	<!--小店应用key-->
	<add key="XiaoDian:AppKey" value="100735" />
	<!--<add key="XiaoDian:AppKey" value="100703" />-->
	<!--小店应用Secret-->
	<add key="XiaoDian:AppSecret" value="3FDC91EF7E02732FBEEE7E93EA57B371" />
	<!--<add key="XiaoDian:AppSecret" value="CA6B6F782C8BE4E75A7522718D572AFA" />-->

	<!--蘑菇街/美丽说应用key-->
	<add key="MoGu:AppKey" value="101444" />
	<!--蘑菇街/美丽说应用Secret-->
	<add key="MoGu:AppSecret" value="007C741556A63FF1D4C2ED261FA7204F" />

	<!--有赞应用key-->
	<add key="YouZan:AppKey" value="9dead7a0e2adca1a15" />
	<!--有赞应用Secret-->
	<add key="YouZan:AppSecret" value="ed4c06980606f48b862f8cc4b2f0f1b9" />

	<!--微店应用key-->
	<add key="WeiDian:AppKey" value="690712" />
	<!--微店应用Secret-->
	<add key="WeiDian:AppSecret" value="ee934f9351f9e35357e015228f61d1d0" />

	<!--微盟应用key-->
	<add key="WeiMeng:AppKey" value="68EFCADAD95AFCAFFF91883A5873FB3D" />
	<!--微盟应用Secret-->
	<add key="WeiMeng:AppSecret" value="5D7308D88572F9F1D3D196E2927864C8" />
	<!--微盟消息 ProviderClientId-->
	<add key="WeiMeng:ProviderClientId" value="08895E0C6874A388371B372A13F49B49" />
	<!--微盟消息 ProviderClientSecret-->
	<add key="WeiMeng:ProviderClientSecret" value="58F0ED0E0F399B6C3BC6981E40E2E524" />

	<!--考拉应用key edb6c3b9ac4847e7584c38e2b630b14f   -->
	<add key="KaoLa:AppKey" value="b3a6feeb035f29eaf19ae65082e8593f" />
	<!--考拉应用Secret 8200ee92ec22fcae76e2f00bc5c79247188e0593 -->
	<add key="KaoLa:AppSecret" value="5acaa489e99fb136ca64a6a63ea308189bccd3ce82222db0d5274d1f0abe1660" />

	<!--唯品会应用key   -->
	<add key="VipShop:AppKey" value="a722074a" />
	<!--唯品会应用Secret -->
	<add key="VipShop:AppSecret" value="6EC72934A6A874B5E2348221869F5719" />

	<!--唯品会沙箱环境Key-->
	<!--<add key="VipShop:AppKey" value="a876c4cc" />-->
	<!--唯品会沙箱环境Secret -->
	<!--<add key="VipShop:AppSecret" value="77780A5819EC3CFBE648436DB9F95492" />-->

	<!--头条应用key-->
	<add key="TouTiao:AppKey" value="6838756062733010445" />
	<!--头条应用Secret-->
	<add key="TouTiao:AppSecret" value="11248815-739a-4f43-b3f6-31f267429fb9" />

	<!--贝贝用key-->
	<add key="BeiBei:AppKey" value="eojj" />
	<!--贝贝应用Secret-->
	<add key="BeiBei:AppSecret" value="91bef3f086e90928e9c614ebc6347585" />

	<!--小红书沙箱环境Key-->
	<add key="XiaoHongShu:AppKey" value="47724293314f40bd845d" />
	<!--小红书沙箱环境Secret -->
	<add key="XiaoHongShu:AppSecret" value="ed3e169529bdc5c08ec4f0fc8ffe8502" />

	<!--小红书分销环境Key-->
	<add key="XiaoHongShuFX:AppKey" value="8fb3aacf06e74aba91ae" />
	<!--小红书分销环境Secret -->
	<add key="XiaoHongShuFX:AppSecret" value="35a63a0d6df636241c53266300676f3e" />

	<!--苏宁应用key   -->
	<add key="Suning:AppKey" value="b60b52ac00505cb576a0c3e0d9372fb9" />
	<!--苏宁应用Secret -->
	<add key="Suning:AppSecret" value="1286d6eb865f23344c1aa7491cd79dd0" />
	<!--苏宁应用收费代码（授权时需要）-->
	<add key="Suning:ItemCode" value="1"/>

	<!--京东应用key-->
	<add key="JingDong:AppKey" value="557C37C5BB73B0A55A1A01422F0705F4" />
	<!--京东应用Secret-->
	<add key="JingDong:AppSecret" value="00c553795cd44e7aa2db5853001c968a" />
	<!--京东接口是否使用代理-->
	<add key="JingDong:IsApiUseProxy" value="1" />

	<!--德邦key-->
	<add key="DBKD:AppKey" value="f96e42ae0f7d04167a612dcce9621b9a" />
	<!--德邦sign值-->
	<add key="DBKD:Sign" value="UCFQ" />
	<!--公司编码 -->
	<add key="DBKD:CompanyCode" value="EWBSZDGJRJYXGS" />

	<!--丰桥key-->
	<add key="FengQiao:AppKey" value="DGJRJ" />
	<!--丰桥Secret-->
	<add key="FengQiao:AppSecret" value="238ffwiXUrvMCAOIXs4ceyVkSQMetbaw" />
	<!--萌推key 测试-->
	<!--<add key="MengTui:AppKey" value="vlFvTLu4hFd3asuM" />-->
	<!--萌推Secret 测试-->
	<!--<add key="MengTui:AppSecret" value="5th4aLRPzzO1BXrZdLDDXksEBQ4rKtUb" />-->
	<!--萌推key-->
	<add key="MengTui:AppKey" value="vKFAuWb2c8L1GzSC" />
	<!--萌推Secret-->
	<add key="MengTui:AppSecret" value="RiXna53vqCwaEXtUC0ZZOomxKBQi8P0M" />
	<!--云集key 测试-->
	<!--<add key="YunJi:AppKey" value="*********-1001" />-->
	<!--云集Secret 测试-->
	<!--<add key="YunJi:AppSecret" value="7321620095809CE5954C34793D2CF7A3" />-->
	<!--云集ISVID 测试-->
	<!--云集key-->
	<add key="YunJi:AppKey" value="*********-1001" />
	<!--云集Secret-->
	<add key="YunJi:AppSecret" value="7321620095809CE5954C34793D2CF7A3" />
	<!--云集key-->
	<add key="YunJi:ISVID" value="ISV1000001" />
	<!--云集ISVID-->
	<!--<add key="YunJi:ISVID" value="ISV1000036" />-->
	<!--快手小店key-->
	<add key="KuaiShou:AppKey" value="ks680325021400191755" />
	<!--快手小店Secret-->
	<add key="KuaiShou:AppSecret" value="HBbyCdJqvOrNCCSSRremRQ" />
	<!--物流中心key-->
	<add key="LogisticCenter:AppKey" value="1" />
	<!--物流中心Secret-->
	<add key="LogisticCenter:AppSecret" value="123abc~!@" />
	<!--物流中心地址-->
	<add key="LogisticCenter:ApiUrl" value="http://pddlogistics.dgjapp.com/" />

	<!--魔筷星选key-->
	<add key="MoKuai:AppKey" value="fe705c624abf4ae4bc41b301322b216b" />
	<!--魔筷星选Secret-->
	<add key="MoKuai:AppSecret" value="80a54d468ccc4637b7167c18864a332c" />

	<!--极兔快递key-->
	<add key="JtExpress:AppKey" value="242127489864826939" />
	<!--极兔快递Secret-->
	<add key="JtExpress:AppSecret" value="9b59f51093604b1090e06859e2abe842" />
	<!--极兔快递ApiUrl-->
	<add key="JtExpress:ApiUrl" value="https://openapi.jtexpress.com.cn/webopenplatformapi/api/" />

	<!--微信小商店配置-->
	<add key="WxXiaoShangDian:AppId" value="wx32ae4064a374b761" />
	<add key="WxXiaoShangDian:AppSecret" value="4c4619f55181886d5c192773a52c90ff" />
	<add key="WxXiaoShangDian:Token" value="423484234322" />
	<add key="WxXiaoShangDian:AESKey" value="fsdjl42343242333fsdf5435435435453fds543543g" />
	<!--申通直连key-->
	<add key="NewSto:AppKey" value="CAKsypICMJqWfUP" />
	<!--申通直连Secret-->
	<add key="NewSto:AppSecret" value="7qIFB9YyUQXXkkf1hAUVEqndtZMJRhge" />
	<!--申通资源Code-->
	<add key="NewSto:AppCode" value="CAKsypICMJqWfUP" />
	<!--申通直连来源编码-->
	<add key="NewSto:Source" value="DGJDD" />

	<!--http://**************/tbAuth.aspx-->
	<!--http://localhost/TaobaoAuth-->
	<!--应用程序Web访问链接，在生成店管家快捷方式时会用到，需在外网可访问-->
	<add key="ApplicationWebUrl" value="http://local.dgjapp.com" />
	<!--1688登录失败的重定向授权地址-->
	<add key="AlibabaRedirectAuthUrl" value="http://print1688.dgjapp.com" />
	<!-- 授权统一回调域名 -->
	<add key="AuthCallBackDomain" value="http://auth.dgjapp.com/"/>

	<!--老系统：淘宝链接-->
	<add key="Taobao:OldSystemLink" value="http://taobao2.dgjapp.com/" />
	<!--老系统：1688链接-->
	<add key="Alibaba:OldSystemLink" value="http://print1688.dgjapp.com" />
	<!--老系统：拼多多链接-->
	<add key="Pinduoduo:OldSystemLink" value="http://pdd2.dgjapp.com" />
	<!--淘宝：淘宝应用key 供订单使用 -->
	<add key="Taobao:AppKey2" value="23172028" />
	<!--淘宝应用秘钥  供订单使用-->
	<add key="Taobao:AppSecret2" value="d0b07cc2d59c70226c22a742585dc1ff" />
	<!--新系统：1688C2M链接-->
	<add key="AlibabaC2M:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：1688链接-->
	<add key="Alibaba:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：淘宝链接-->
	<add key="Taobao:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：拼多多链接-->
	<add key="Pinduoduo:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：小店平台链接-->
	<add key="XiaoDian:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：蘑菇街平台链接-->
	<add key="MoGuJie:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：美丽说平台链接-->
	<add key="MeiLiShuo:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：有赞平台链接-->
	<add key="YouZan:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：微店平台链接-->
	<add key="WeiDian:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：微盟平台链接-->
	<add key="WeiMeng:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：考拉平台链接-->
	<add key="KaoLa:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：唯品会平台链接-->
	<add key="VipShop:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：苏宁链接-->
	<add key="Suning:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：值点平台链接-->
	<add key="ZhiDian:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：京东平台链接-->
	<add key="JingDong:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：萌推平台链接-->
	<add key="MengTui:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：云集平台链接-->
	<add key="YunJi:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：开放平台链接-->
	<add key="OpenV1:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：小红书链接-->
	<add key="XiaoHongShu:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：小商店链接-->
	<add key="WxXiaoShangDian:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：魔筷链接-->
	<add key="MoKuai:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：度小店链接-->
	<add key="DuXiaoDian:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：快团团链接-->
	<add key="KuaiTuanTuan:NewSystemLink" value="http://local.dgjapp.com" />

	<!--快递鸟应用key-->
	<add key="KuaiDiNiao:AppKey" value="1312996" />
	<!--快递鸟应用秘钥-->
	<add key="KuaiDiNiao:AppSecret" value="cf2c6c94-3fae-4026-ae83-ff4f48e13234" />
	<!--快递鸟ApiUrl-->
	<add key="KuaiDiNiao:ApiUrl" value="http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx" />
	<!--新系统：快手小店平台链接-->
	<add key="KuaiShou:NewSystemLink" value="http://local.dgjapp.com" />
	<!--新系统：贝贝平台链接-->
	<add key="BeiBei:NewSystemLink" value="http://local.dgjapp.com" />

	<!--丰桥key-->
	<add key="FenQiao:AppKey" value="ZY_rvkYY" />
	<!--丰桥秘钥-->
	<add key="FenQiao:AppSecret" value="zAPWgsYjLXUYl2iM3Qy9jMyoXpgNrFre" />

	<!--个人中心入口-->
	<add key="PortalLink" value="http://portal.dgjapp.com/" />
	<!--子账号登录入口-->
	<!--<add key="PortalLink" value="https://portal.dgjapp.com/" />-->
	<add key="SubUserLoginLink" value="https://subaccount.dgjapp.com/" />
	<!--微信新版对应的小程序 appid-->
	<add key="WXAppId" value="wx8e023a4836a52f22" />
	<add key="WXAppSecret" value="a7b6e0568975147c1661ddb9520f6ff6" />
	<!--微信 微商版本小程序-->
	<add key="WsXcxAppId" value="wxbe31668c3e7f7d50" />
	<add key="WsXcxAppSecret" value="1511029242e304408ce54f7483fdbc91" />
	<!--微信  是否需要验证签名-->
	<add key="IsVerifySigh" value="0" />
	<!--新系统：微商版链接-->
	<add key="WeiShangLink" value="http://local.dgjapp.com/" />
	<add key="SystemQueueUrl" value="http://queue.dgjapp.com/" />

	<!--begin RabbitMq配置-->
	<!--MQ的vhost-->
	<add key="MqVHost" value="dgjapp" />
	<!--MQ的地址-->
	<add key="MqHost" value="*************" />
	<!--MQ的端口-->
	<add key="MqHostPort" value="5672" />
	<!--MQ的用户名-->
	<add key="MqUserName" value="dgjapp" />
	<!--MQ的密码-->
	<add key="MqPassword" value="dgjapp.123.abc." />
	<!--end RabbitMq配置-->

	<!--begin TouTiao_RabbitMq配置-->
	<!--MQ的vhost-->
	<add key="TouTiao_MqVHost" value="dgjapp" />
	<!--MQ的地址-->
	<add key="TouTiao_MqHost" value="*************" />
	<!--MQ的端口-->
	<add key="TouTiao_MqHostPort" value="5672" />
	<!--MQ的用户名-->
	<add key="TouTiao_MqUserName" value="dgjapp" />
	<!--MQ的密码-->
	<add key="TouTiao_MqPassword" value="dgjapp.123.abc." />
	<!--end TouTiao_RabbitMq配置-->

	<!--是否启用店铺同步队列-->
	<add key="IsEnabledShopSyncQueue" value="0"/>
	<!--最大消息队列的警告长度，超过时会发出钉钉消息警告，默认200个-->
	<add key="MaxMessageQueueWarningCount" value="200"/>
	<!--<add key="Alidayu_TemplateCode" value="SMS_156895301" />-->
	<!--当前应用所在云平台，默认Alibaba 阿里云，暂时仅有四种值：Alibaba、Pinduoduo、Jingdong、TouTiao，分表为阿里云、多多云、京东云、抖店云；同步程序队列站点有用到-->
	<add key="CloudPlatformType" value="TouTiao"/>
	<!--拼多多默认版本号，3则指向多多云-->
	<add key="Pinduoduo:DefaultVersion" value="3"/>
	<add key="Pinduoduo:C2MLink" value="http://local.dgjapp.com"/>
	<add key="IsEnabledPddPushDb" value="0"/>
	<!--是否开启紧急解绑隐藏店铺订单的开关-->
	<add key="IsEnabledUnbindHideOrder" value="true"/>
	<!--头条版本号-->
	<!--<add key="TouTiaoVersion" value=""/>-->
	<!--<add key="PinduoduoApiUrl" value="https://pdd5.dgjapp.com/gate/route"/>
  <add key="AlibabaApiUrl" value="http://aliapi.dgjapp.com/api/route"/>
  <add key="ConfigApiUrl" value="http://config.dgjapp.com/api/route"/>-->
	<add key="PinduoduoApiUrl" value="https://pdd5.dgjapp.com/gate/route"/>
	<add key="PinduoduoApiUrlNew" value="http://192.168.1.196:8040/api/route"/>
	<add key="AlibabaApiUrl" value="http://192.168.1.196:8040/api/route"/>
	<add key="TouTiaoApiUrl" value="http://192.168.1.98:8089/api/route"/>
	<add key="ConfigApiUrl" value="http://config.dgjapp.com/api/route"/>

	<add key="ConfigApiUrlNew" value="http://192.168.1.196:8040/api/route2"/>

	<add key="Pinduoduo:ApiUrl" value="http://gw-api.pinduoduo.com/api/router"/>

	<!--<add key="PinduoduoApiUrl" value="http://localapi.dgjapp.com/api/route"/>
	<add key="AlibabaApiUrl" value="http://localapi.dgjapp.com/api/route"/>
	<add key="ConfigApiUrl" value="http://localapi.dgjapp.com/api/route"/>-->

	<add key="Pinduoduo:ApiUrl" value="http://gw-api.pinduoduo.com/api/router"/>



	<!--超出订单数量使用任务导出-->
	<add key="ExportByTaskOrderCountLimit" value="10"/>
	<!--超出底单数量使用任务导出-->
	<add key="ExportByTaskWaybillCodeCountLimit" value="10"/>
	<add key="OperatorType" value="打印快递单,批量发货,增量同步订单,更改订单打印状态"/>
	<add key="IsEnabledPddLogisticMessage" value="true"/>

	<!--start  订单分发系统相关配置信息-->
	<add key="DefaultFenFaSystemUrl" value="http://fendan.dgjapp.com"/>
	<add key="AlibabaFenFaSystemUrl" value="http://fendan.dgjapp.com"/>
	<add key="PinduoduoFenFaSystemUrl" value="http://fendan.dgjapp.com"/>
	<add key="JingdongFenFaSystemUrl" value="http://fendan.dgjapp.com"/>
	<add key="ToutiaoFenFaSystemUrl" value="http://fendan.dgjapp.com"/>
	<add key="TikTokFenFaSystemUrl" value="http://fendan.dgjapp.com"/>

	<!--拼多多站点相关配置-->
	<add key="PddFxWebApiUrl" value="http://***********:10013"/>
	<!--绑定店铺 绑定厂家 绑定商家，绑定额度作为可配置的，暂无上限，根据运营需求调整-->
	<add key="Fx_Authorizationlimit" value="100"/>
	<!--end  订单分发系统相关配置信息-->

	<!--订单分发2.0平台登录链接-->
	<add key="FendanLoginUrl" value="http://login.dgjapp.com/FFAccount" />
	<!--是否为分销站点，1为启用-->
	<add key="IsFendanSite" value="1" />
	<!--库存相关配置-->
	<add key="WarehouseApiUrl" value="http://localhost:8090"/>
	<add key="WarehouseAppkey" value="********"/>
	<add key="WarehouseAppsecret" value="1031469n4j3482q09ZhU0JP1AYbxu87d"/>
	<add key="MaintenanceTime" value="2021-08-04 23:00:00"/>


	<!--批量发货异常补偿：是否主动抛出异常 1=主动抛；其他=不主动抛-->
	<add key="IsThrowException" value="0"/>
	<!--是否设为发货失败，用于调试：1=首次发货设为失败；2=二次发货设为失败-->
	<add key="SetSendFail" value="0" />
	<add key="ApplicationName" value="分单系统站点"/>

	<!--忽略利润订单同步开启时间 允许执行同步 默认0不启用-->
	<add key="IgnoreProfitOrderSyncOpenTime" value="1"/>

	<!-- 修复发货记录时间（不调用发货接口）-->
	<add key="RepairSendHistoryDate" value="" />

	<!-- 请求源是否为抖音小程序（本地调试时设为1，其他为0） -->
	<add key="IsTouTiaoAppSite" value="1" />
	<!--日志存储开始-->
	<add key="AliLog:Url" value="cn-zhangjiakou.log.aliyuncs.com"/>
	<add key="AliLog:FxProject" value="fendan-system-log"/>
	<add key="AliLog:FxLogstore" value="logstore-fenxiao-service-exception"/>
	<add key="AliLog:FxLogstore:DataEventTracking" value="logstore-fenxiao-dataeventtracking"/>
	<add key="AliLog:FxLogTopic" value="service"/>
	<!--日志存储结束-->
	<!--是否开启订单同步服务-->
	<add key="DianGuanJia:IsEnabledOrderSyncService" value="false" />
	<!--是否开启全局订单同步服务-->
	<add key="DianGuanJia:IsEnabledGlobalOrderSyncService" value="false"/>
	<!--是否开启全局售后单同步服务-->
	<add key="DianGuanJia:IsEnabledGlobalAfterSaleSyncService" value="false"/>
	<!--是否开启售后单同步服务-->
	<add key="DianGuanJia:IsEnabledAfterSaleSyncService" value="false" />
	<!--是否开启商品同步服务-->
	<add key="DianGuanJia:IsEnabledProductSyncService" value="false" />
	<!--是否开启全局商品同步服务-->
	<add key="DianGuanJia:IsEnabledGlobalProductSyncService" value="false"/>

	<add key="BatchCountForSettlementLevel" value="10" />
	<!--是否开启全局数据埋点-->
	<add key="DianGuanJia:IsEnabledGlobalDataEventTracking" value="false"/>
	<!--是否指定店铺开启数据埋点-->
	<add key="DianGuanJia:IsEnabledGlobalProductSyncService" value="false"/>
	<!--系统参数 BEGIN-->
	<add key="DianGuanJiaApp:SystemName" value="fendan"/>
	<!--环境 本地:local 测试：test 生产:production-->
	<add key="DianGuanJiaApp:Environment" value="local"/>
	<!--系统参数 END-->
	<!--消息中间件 BEGIN-->
	<add key="DianGuanJiaApp:RabbitMQ:HostName" value="*************"/>
	<add key="DianGuanJiaApp:RabbitMQ:UserName" value="admin"/>
	<add key="DianGuanJiaApp:RabbitMQ:Password" value="david1103"/>
	<add key="DianGuanJiaApp:RabbitMQ:VirtualHost" value="dgjapp.fendan"/>
	<!--最大连接数，默认100-->
	<add key="DianGuanJiaApp:RabbitMQ:MaxConnectionCount" value="100"/>
	<!--消息中间件 END-->
	<!--Elasticsearch日志 BEGIN-->
	<add key="DianGuanJiaApp:ElasticSearch:Log:NodeUrls" value="http://*************:9200"/>
	<add key="DianGuanJiaApp:ElasticSearch:Log:UserName" value=""/>
	<add key="DianGuanJiaApp:ElasticSearch:Log:Password" value=""/>
	<!--Elasticsearch日志 END-->
	<!--是否开启新主体过审，隐藏和店管家相关文案链接等-->
	<add key="DianGuanJiaApp:IsEnabledNewCorpReview" value="true" />
	<!--统计店铺订单指定状态数量-->
	<add key="StatOrderStatus" value="success"/>
	<!--Redis缓存开关配置的键值-->
	<add key="FxCaching:FxCachingSettingKey" value="/System/Config/FenDan/FxCachingSettingGray1/" />
	<!--是否开启商品选择器查询V2版-->
	<add key="DianGuanJia:IsEnabledProductSelectorSearchV2" value="true"/>
	<!--同步分片天数（测试环境限制同步时间分片）-->
	<add key="SyncSplitDay" value="0.05"/>
	<!--合单新逻辑测试账号-->
	<add key="TestFxUserIds" value=""/>

	<!--1688分销支持的平台类型-->
	<add key="Fx1688SupportPlatformTypes" value="TouTiao,Taobao,KuaiShou,XiaoHongShu,Pinduoduo,Virtual" />
	<!--同步服务环境类型，默认值:空，正式版，其他：灰度：Gray-->
	<add key="DianGuanJiaApp:SyncService:Environment" value="Gray"/>
	<!--是否开启迁移时写云日志-->
	<add key="DianGuanJia:IsEnabledMigrateWriteCloudLog" value="false"/>

	<!--站内信-->
	<add key="SiteMessageAppsecret" value="1031469n4j3482q09ZhU0JP1AYbxu87d" />
	<add key="SiteMessageAppkey" value="********" />
	<!--<add key="SiteMessageApiUrl" value="https://sitemessageapi.dgjapp.com" />-->
	<add key="SiteMessageApiUrl" value="http://192.168.1.48:4133" />
	<!--是否启用订单审核处理服务-->
	<add key="IsEnableCheckRuleWinService" value="1" />
	<!--结算价导入达到多少走任务-->
	<add key="DianGuanJia:ImporSettlementPriceCountOfTask" value="1000"/>

	<!--抖店云OSS配置-->
	<add key="DianGuanJiaApp:TouTiaoTosS:AccessKeyId" value="AKLTMWRiOTUxYzk4ODdkNDI0ZmJiOTU5OGM1Y2M5OTVlODk"/>
	<add key="DianGuanJiaApp:TouTiaoTosS:AccessKeySecret" value="TWpoaVlqZ3daREk0TnpkbE5EVTRNbUl5TUdRMU9HSm1OR0poWTJGbU0yUQ=="/>
	<add key="DianGuanJiaApp:TouTiaoTosS:Endpoint" value="tos-cn-beijing.volces.com"/>
	<add key="DianGuanJiaApp:TouTiaoTosS:Region" value="cn-beijing"/>

	<!--各云平台消息接收域名-->
	<add key="AlibabaMessageDomain" value="http://192.168.1.196:8077"/>
	<add key="ToutiaoMessageDomain" value="http://192.168.1.196:8077"/>
	<add key="PddMessageDomain" value="http://192.168.1.196:8077"/>
	<add key="JdMessageDomain" value="http://192.168.1.196:8077"/>

	<!--是否是禾量测试环境地址-->
	<add key="IsHeliangTestUrl" value="1"></add>

	<!--是否开启查询MySQL的PrintHistoryConfigDb-->
	<add key="IsOpenMySqlByPrintHistoryConfigDb" value="1" />
	<!--是否开启查询MySQL的ReceiverConfigDb-->
	<add key="IsOpenMySqlByReceiverConfigDb" value="1" />
	<!--是否启用重置密码令牌设置过期开关，1为启用-->
	<add key="Login:IsClearTokenAtfterResetPwd" value="1"/>

	<!--跨境站点配置-->
	<add key="IsCrossBorderSite" value="0"/>
	<add key="CrossBorderCloudLocation" value="ChinaAliyun"/> 
	<add key="TikTok:ApiGateWay" value="https://tkmessage.dgjapp.com/cross"/>
	

	<!-- 系统环境版本，根据部署的服务器环境更改不同的值。固定值：Online（正式），Gray1（灰度1），Gray3,Test1（1t测试环境）,Test3,Development （开发环境） -->
	<add key="SystemEnvironmentVersion" value="Development"/>
	<!--同步服务环境类型，默认值:空，正式版，其他：灰度：Gray-->
	<add key="DianGuanJiaApp:SyncService:Environment" value="Gray"/>
	<!-- 是否开启链路日志 -->
	<add key="IsOpenOpenTelemetry" value="true"/>
	
	<!-- 系统环境版本，根据部署的服务器环境更改不同的值。固定值：Online（正式），Gray1（灰度1），Gray3,Test1（1t测试环境）,Test3,Development （开发环境） -->
	<add key="SystemEnvironmentVersion" value="Development"/>
	<!--打印记录迁移：增量迁移指定范围（整段）-->
	<!--打印记录迁移：全量迁移指定范围(分段)-->
	
	<!--打印记录迁移：是否是初始迁移（去除自增）1：全量初始 0：增量同步-->
	<add key="IsInitialPrintHistoryMigrate" value="0"></add>
	<!--打印记录迁移: false 分段迁移、true 整段迁移-->
	<add key="IsFullPrintHistoryMigrate" value="1"></add>
	<!--打印记录迁移: 迁移程序最小起始ID-->
	<add key="PrintHistoryMigrateMinStartId" value="11054"></add>
	<!--打印记录迁移: 迁移程序偏移量-->
	<add key="PrintHistoryMigrateOffset" value="24600"></add>
	<!--打印记录迁移：数据库是自增：true 开启、false 关闭-->
	<add key="IsAutoIncrementPrintHistoryKey" value="1"></add>
</appSettings>