<div class="n-main" id="New_setPrepareDistribution">
    <div class="n-layui-mywrap" style="width: 100%;">
        <div class="n-layui-mywrap-title" style="justify-content:space-between;display:flex;">
            <span class="n-font4">基础设置</span>
            <div class="n-layui-mywrap-title-right"></div>
        </div>

        <div class="basic-settings">
            <ul class="layui-content-ul">
                <li>
                    <div>
                        <span class="layui-content-ul-title">完成铺货的商品状态</span>
                        <div class="catePropsItemWrap" id="Batch_productStatus_set">
                            <div class="n-mySelect n-single catePropsItem hasActive  n-myCommonSelect" style="width: 404px;">
                                <div class="n-mySelect-title">
                                    <div class="n-mySelect-title-left">
                                        <span class="n-mySelect-title-placeholder">请选择</span>
                                        <span class="n-mySelect-title-chooseItem"></span>
                                    </div>
                                    <span class="iconfont icon-a-chevron-down1x"></span>
                                </div>
                                <div class="n-mySelect-showContent">
                                    <ul class="n-mySelect-showContent-ul">
                                        <li class="n-mySelect-showContent-ul-li"
                                            data-value="1"
                                            onclick="BatchselectPrepareDistribution.bind(this)('ProductStatus',1)">立即上架</li>
                                        <li class="n-mySelect-showContent-ul-li"
                                            data-value="2"
                                            onclick="BatchselectPrepareDistribution.bind(this)('ProductStatus',2)">放入仓库</li>
                                        <li class="n-mySelect-showContent-ul-li"
                                            data-value="3"
                                            onclick="BatchselectPrepareDistribution.bind(this)('ProductStatus',3)">放入草稿箱</li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li style="display: none;" class="isshow_ks">
                    <div>
                        <span class="layui-content-ul-title">发货</span>
                        <div style="display:flex;">
                            <div class="catePropsItemWrap" style="margin-right:2px">

                                <div class="n-mySelect n-single catePropsItem hasActive stop" style="width: 201px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">
                                            <span class="n-mySelect-title-left-title">发货模式</span>
                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                            <span class="n-mySelect-title-chooseItem" data-value="1">非预售</span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent" style="display:none;">
                                        <ul class="n-mySelect-showContent-ul">
                                            <li class="n-mySelect-showContent-ul-li "
                                                onclick="selectCateProps.bind(this)('DeliveryMode',1)"
                                                style="display: flex;">非预售</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="catePropsItemWrap" id="Batch_deliveryTime_set_ks">
                                <div class="n-mySelect n-single catePropsItem  n-myCommonSelect" style="width: 201px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">
                                            <span class="n-mySelect-title-left-title">发货时间</span>
                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                            <span class="n-mySelect-title-chooseItem"></span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent">
                                        <ul class="n-mySelect-showContent-ul">
                                            <li class="n-mySelect-showContent-ul-li " data-value="1" onclick="BatchselectPrepareDistribution.bind(this)('DeliveryTime',1)">24小时</li>
                                            <li class="n-mySelect-showContent-ul-li " data-value="2" onclick="BatchselectPrepareDistribution.bind(this)('DeliveryTime',2)">48小时</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div>
                        <span class="layui-content-ul-title">履约</span>

                        <div style="display:flex;">
                            <div class="catePropsItemWrap n-myCommonSelect" style="margin-right:2px" id="Batch_freightTemplateWrap">

                                <div id="Batch_template_select" class="n-mySelect n-single catePropsItem  n-myCommonSelect" style="width: 201px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">

                                            <span class="n-mySelect-title-left-title">运费模板</span>
                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                            <span class="n-mySelect-title-chooseItem"></span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent">
                                        <ul class="n-mySelect-showContent-ul" id="Batch_freightTemplate_ul"></ul>
                                        <div onclick="showBatchEdmitFreightTemplate.bind(this)()" class="n-mySelect-showContent-ul-li showEdmitFreightTemplate-btn n-dColor">
                                            <i class="iconfont icon-a-edit1x"></i><span>编辑运费模板</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="catePropsItemWrap n-myCommonSelect">
                                <div class="n-mySelect n-single catePropsItem hasActive stop" style="width: 201px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">

                                            <span class="n-mySelect-title-left-title">售后政策</span>
                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                            <span class="n-mySelect-title-chooseItem">7天无理由退货</span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent">
                                        <ul class="n-mySelect-showContent-ul">
                                            <li class="n-mySelect-showContent-ul-li "
                                                onclick="selectCateProps.bind(this)('')"
                                                style="display: flex;">7天无理由退货</li>

                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </li>

            
                <li class="ishide_ks">
                    <div>
                        <span class="layui-content-ul-title">发货</span>
                        <div style="display:flex;">
                            <div class="catePropsItemWrap" style="margin-right:2px">

                                <div class="n-mySelect n-single catePropsItem hasActive stop" style="width: 201px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">
                                            <span class="n-mySelect-title-left-title">发货模式</span>
                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                            <span class="n-mySelect-title-chooseItem" data-value="1">现货发货模式</span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent" style="display:none;">
                                        <ul class="n-mySelect-showContent-ul">
                                            <li class="n-mySelect-showContent-ul-li "
                                                onclick="selectCateProps.bind(this)('DeliveryMode',1)"
                                                style="display: flex;">现货发货模式</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="catePropsItemWrap" id="Batch_deliveryTime_set">
                                <div class="n-mySelect n-single catePropsItem  n-myCommonSelect" style="width: 201px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">
                                            <span class="n-mySelect-title-left-title">发货时间</span>
                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                            <span class="n-mySelect-title-chooseItem"></span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent">
                                        <ul class="n-mySelect-showContent-ul">
                                            <li class="n-mySelect-showContent-ul-li " data-value="1" onclick="BatchselectPrepareDistribution.bind(this)('DeliveryTime',1)">当日发</li>
                                            <li class="n-mySelect-showContent-ul-li " data-value="2" onclick="BatchselectPrepareDistribution.bind(this)('DeliveryTime',2)">次日发</li>
                                            <li class="n-mySelect-showContent-ul-li " data-value="3" onclick="BatchselectPrepareDistribution.bind(this)('DeliveryTime',3)">48小时内发货</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div>
                        <span class="layui-content-ul-title">库存计数</span>
                        <div class="catePropsItemWrap" id="Batch_warehouseCalcRule_set">

                            <div class="n-mySelect n-single catePropsItem  n-myCommonSelect hasActive" style="width: 404px;">
                                <div class="n-mySelect-title">
                                    <div class="n-mySelect-title-left">
                                        <span class="n-mySelect-title-placeholder">请选择</span>
                                        <span class="n-mySelect-title-chooseItem">下单减库存</span>
                                    </div>
                                    <span class="iconfont icon-a-chevron-down1x"></span>
                                </div>
                                <div class="n-mySelect-showContent">
                                    <ul class="n-mySelect-showContent-ul">
                                        <li class="n-mySelect-showContent-ul-li"
                                            data-value="1"
                                            onclick="BatchselectPrepareDistribution.bind(this)('WarehouseCalcRule',1)">下单减库存</li>
                                        <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="BatchselectPrepareDistribution.bind(this)('WarehouseCalcRule',2)">付款减库存</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <li style="display: none;" class="isshow_pdd">
                    <div>
                        <span class="layui-content-ul-title">承诺</span>
                        <div class="" id="Batch_promise">
                            <label>
                                <span class="n-newCheckbox activeF" name="ReturnPolicyDays" onclick="changeCheckboxFun('ReturnPolicyDays')"></span><span>7天无理由退货</span>
                            </label>
                            <label>
                                <span class="n-newCheckbox activeF" name="FakeOneCompensateTen" onclick="changeCheckboxFun('FakeOneCompensateTen')"></span><span>假一赔十</span>
                            </label>
                            <label>
                                <span class="n-newCheckbox" name="DamageCoverage" onclick="changeCheckboxFun('DamageCoverage')"></span><span>坏了包赔</span>
                            </label>
                        </div>
                    </div>
                </li>
                <li style="display: none;">
                    <div>
                        <span class="layui-content-ul-title">发货仓库</span>

                        <div class="catePropsItemWrap n-myCommonSelect" style="margin-right:2px" id="Batch_deliveryTemplateWrap">

                            <div id="Batch_deliveryTemplate_select" class="n-mySelect n-single catePropsItem  n-myCommonSelect" style="width: 404px;">
                                <div class="n-mySelect-title">
                                    <div class="n-mySelect-title-left">

                                        <span class="n-mySelect-title-left-title">发货仓库</span>
                                        <span class="n-mySelect-title-placeholder">请选择</span>
                                        <span class="n-mySelect-title-chooseItem"></span>
                                    </div>
                                    <span class="iconfont icon-a-chevron-down1x"></span>
                                </div>
                                <div class="n-mySelect-showContent">
                                    <ul class="n-mySelect-showContent-ul" id="Batch_deliveryTemplate_ul"></ul>
                                    <!-- <div onclick="showEdmithippingWarehouseTemplate.bind(this)()" class="n-mySelect-showContent-ul-li showEdmitFreightTemplate-btn n-dColor">
                                        <i class="iconfont icon-tongbu"></i><span>同步仓库模板</span>
                                    </div>
                                    <div onclick="showEdmithippingWarehouseTemplate.bind(this)()" class="n-mySelect-showContent-ul-li showEdmitFreightTemplate-btn n-dColor">
                                        <i class="iconfont icon-a-add1x"></i><span>添加仓库模板</span>
                                    </div> -->
                                </div>
                            </div>
                        </div>

                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>


<div class="n-main" id="New_setPrepareDistribution_2">
    <div class="n-layui-mywrap" style="width: 100%;">
        <div class="n-layui-mywrap-title" style="justify-content:space-between;display:flex;">
            <span class="n-font4">价格设置</span>
            <div class="n-layui-mywrap-title-right"></div>
        </div>
        <div style="justify-content:space-between;display:flex;">
            <ul class="layui-content-ul">
                <li>
                    <div>
                        <span class="layui-content-ul-title">默认根据以下顺序取价</span>
                        <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">商品售卖价格的取值顺序，如果商品没有参考基础价导致未取到或者计算出的，则商品售价显示为空，需手动填写</span></i>
                    </div>
                    <ul class="setpriceWrap" id="setpriceWrap_ul"></ul>
                </li>
                <li class="isshow_pdd" style="display:none;">
                    <div style="display:flex;align-items:baseline;">
                        <span class="layui-content-ul-title">单买价</span>
                        <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">单买价至少比拼单价高1元</span></i>
                    </div>
                    <ul class="setpriceWrap" id="singlePriceWrap_ul"></ul>
                </li>
                <li class="isshow_pdd" style="display:none;">
                    <div style="display:flex;align-items:baseline;">
                        <span class="layui-content-ul-title">参考价</span>
                        <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">参考价即吊牌价、市场价、需高于商品单买价，并且不得超过单买价的5倍</span></i>
                    </div>
                    <ul class="setpriceWrap" id="referencePriceWrap_ul"></ul>
                </li>
                <li>
                    <div>
                        <span class="layui-content-ul-title">角分处理</span>
                    </div>
                    <ul class="setpriceWrap">
                        <li class="setpriceWrap-item n-center" id="MinuteOfArc_01">
                            <span class="n-newRadio mR8 MinuteOfArc" onclick="changeBatchListingRaio.bind(this)('MinuteOfArc',1)"></span>
                            <span class="f14 c09">保留角和分</span>
                        </li>
                        <li class="setpriceWrap-item n-center" id="MinuteOfArc_02">
                            <span class="n-newRadio mR8 MinuteOfArc hasMinuteOfArc" onclick="changeBatchListingRaio.bind(this)('MinuteOfArc',2)"></span>
                            <span class="f14 c09 mR4">统一改为</span>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input" style="width:100px;" placeholder="请输入" id="MinuteOfArc_value" onchange="changeBatchListingInput.bind(this)('MinuteOfArc')">
                            </div>
                            <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">例如：商品计算后的价格为64.53元，选择此项并值输入99，则价格会调整为64.99元</span></i>
                        </li>
                    </ul>
                </li>
                <li class="isshow_pdd" style="display:none;">
                    <div>
                        <span class="layui-content-ul-title">满件折扣</span>
                    </div>
                    <ul class="setpriceWrap">
                        <li class="setpriceWrap-item n-center setpriceWrap-active">
                            <span class="f14 c09 mR4">商品满2件打</span>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input" style="width:100px;" placeholder="5-9.9" id="BulkDiscount_value" onchange="changeBatchListingInput.bind(this)('BulkDiscount')">
                            </div>
                            <span class="f14 c09 mL4">折</span>
                            <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">1.部分类目必须设置2件折扣，否则会铺货失败，若您未设置将会自动用折扣最大值 
                            <span style="color: #DC8715;display:block;margin-bottom: 16px;">若折扣范围为5.0-9.9，折扣最大值是9.9折</span> 2.商品满2件折扣与店铺满2件折扣不会叠加</span>
                            </i>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="n-main" id="New_setPrepareDistribution_3" style="margin-bottom:70px">
    <div class="n-layui-mywrap" style="width: 100%;">
        <div class="n-layui-mywrap-title" style="justify-content:space-between;display:flex;">
            <span class="n-font4">库存设置</span>
            <div class="n-layui-mywrap-title-right"></div>
        </div>
        <div style="justify-content:space-between;display:flex;">
            <ul class="layui-content-ul">
                <li>
                    <div>
                        <span class="layui-content-ul-title">库存规则</span>
                    </div>
                    <ul class="setpriceWrap">
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 Repertory" onclick="changeBatchListingRaio.bind(this)('Repertory',1)"></span>
                            <span class="f14 c09">和源库存一致</span>
                        </li>
                        <li class="setpriceWrap-item n-center" id="hasRepertory_02">
                            <span class="n-newRadio mR8 Repertory hasRepertory" onclick="changeBatchListingRaio.bind(this)('Repertory',2)"></span>
                            <span class="f14 c09 mR4">SKU 库存统一改为</span>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input" style="width:100px;" placeholder="请输入" id="Repertory_value" onchange="changeBatchListingInput.bind(this)('Repertory')">
                                <span class="input-num">件</span>
                            </div>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</div>


<div class="createBase-footer" style="justify-content:flex-start;">
    <span class="n-mButton n-sActive" style="margin-right:10px;" onclick="DistributionSetLastStep.bind(this)()">上一步</span>
    <span class="n-mButton" style="margin-right:10px;margin-left:0;" onclick="DistributionSetNextStep.bind(this)()">下一步</span>
</div>


<div id="BatchEdmitShippingTemplatesWrap" style="display:none"></div>

<script id="BatchedmitShippingTemplates_templates" type="text/x-jsrender">

    <div class="edmitShippingTemplates" id="edmitShippingTemplates">
        <div class="n-tabNavWrap">
            <ul class="n-tabNav" id="edmitShippingTemplates_nav">
                {{for edmitTemplateObj}}
                <li data-id="{{:FxUserId}}" data-uniquecode="{{:UniqueCode}}" data-index="{{:#getIndex()}}" class="n-tabNav-item {{:IsActive?'active':''}}">
                    {{if Type=="creat"}}
                    <i class="iconfont icon-a-add1x"></i>
                    {{/if}}
                    <span>{{:TemplateGroupName}}</span>
                </li>
                {{/for}}
            </ul>
        </div>
        {{for edmitTemplateObj ~Templates=Templates}}

        <ul class="layui-content-ul edmitShippingTemplates-ul" id="edmitShippingTemplates_{{:#getIndex()}}" style="{{: IsActive?'display:block':'display:none' }}">
            <li>
                <div class="n-layui-mywrap">
                    <span class="layui-content-ul-title">模板类型</span>
                    <div class="n-inputWrap">
                        <input data-oldName="{{:TemplateGroupName}}" id="templateName_{{:#getIndex()}}" type="text" onchange="changeTemplateInput.bind(this)('{{:#getIndex()}}')" value="{{:Type=="creat"?"":TemplateGroupName}}" class="layui-input n-layui-input" style="width: 100%;padding-right:60px;" placeholder="请输入内容">
                    </div>
                </div>
            </li>
            <li style="margin-top: 0;">
                <div class="n-layui-mywrap">
                    <span class="layui-content-ul-title">运费模板设置</span>
                    <div class="tableShippingWrap">
                        <table class="tableShippingTable n-table edmitTable activeEdmitTable">
                            <thead>
                                <tr>
                                    <th style="width: 272px;">店铺名称</th>
                                    <th style="width: 282px;">运费模板</th>
                                </tr>
                            </thead>
                            <tbody id="tbody_sipping_data_{{:#getIndex()}}">
                                {{for SaveLisTempGroupItemModel ~pIndex=#getIndex()}}
                                <tr>
                                    <td>
                                        <div class="sipping-name">
                                            <span class="n-smallIcon {{:PlatformType}}"></span>
                                            <span class="shopName">{{:NickName}}</span>

                                        </div>
                                    </td>
                                    <td>
                                        <div class="rightOperateWrap">
                                            <div class="catePropsItemWrap ">

                                                <div class="n-mySelect n-single catePropsItem n-myCommonSelect {{:PlatformTemplateId ? 'hasActive':'addWarnInput'}}" style="width: 260px;">
                                                    <div class="n-mySelect-title">
                                                        <div class="n-mySelect-title-left">

                                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                                            <span class="n-mySelect-title-chooseItem">{{:PlatformTemplateName?PlatformTemplateName:''}}</span>
                                                        </div>
                                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                                    </div>
                                                    <div class="n-mySelect-showContent02" style="width: 260px;">
                                                        <ul class="n-mySelect-showContent-ul">
                                                            {{for AllExpressTemplates ~ShopId=Id ~TemplateId=TemplateId }}
                                                            <li class="n-mySelect-showContent-ul-li" data-shopid="{{~ShopId}}" data-id="{{:Id}}" data-name="{{:DeliveryTemplateName}}" onclick="selectTemplate.bind(this)('{{:Id}}','{{:DeliveryTemplateName}}','{{:~ShopId}}','{{:~pIndex}}')">{{:DeliveryTemplateName}}</li>
                                                            {{/for}}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <span class="iconfont icon-tongbu n-dColor" onclick="refreshDeliveryTemplateFun.bind(this)('{{:ShopId}}')"></span>
                                        </div>
                                    </td>
                                </tr>
                                {{/for}}
                            </tbody>
                        </table>

                    </div>
                </div>
            </li>
        </ul>

        {{/for}}
    </div>
</script>
<!-- 发货仓模版 -->
<div id="BatchEdmitShippingWarehouseTemplatesWrap" style="display:none"></div>
<!-- 发货仓模版 -->
<script id="BatchedmitShippingWarehouseTemplates_templates" type="text/x-jsrender">

    <div class="edmitShippingWarehouseTemplates" id="edmitShippingWarehouseTemplates">
        <div class="n-tabNavWrap">
            <ul class="n-tabNav" id="edmitShippingWarehouseTemplates_nav">
                {{for edmitTemplateObj}}
                <li data-id="{{:FxUserId}}" data-uniquecode="{{:Code}}" data-index="{{:#getIndex()}}" class="n-tabNav-item {{:IsActive?'active':''}}">
                    {{if Type=="creat"}}
                    <i class="iconfont icon-a-add1x"></i>
                    {{/if}}
                    <span>{{:Name}}</span>
                </li>
                {{/for}}
            </ul>
        </div>
        {{for edmitTemplateObj }}

        <ul class="layui-content-ul edmitShippingWarehouseTemplates-ul" id="edmitShippingWarehouseTemplates_{{:#getIndex()}}" style="{{: IsActive?'display:block':'display:none' }}">
            <li>
                <div class="n-layui-mywrap">
                    <span class="layui-content-ul-title">模板类型</span>
                    <div class="n-inputWrap">
                        <input data-oldName="{{:Name}}" id="ShippingWarehouse_templateName_{{:#getIndex()}}" type="text" onchange="changeTemplateInput.bind(this)('{{:#getIndex()}}')" value="{{:Type=="creat"?"":Name}}" class="layui-input n-layui-input" style="width: 100%;padding-right:60px;" placeholder="请输入内容">
                    </div>
                </div>
            </li>
            <li style="margin-top: 0;">
                <div class="n-layui-mywrap">
                    <span class="layui-content-ul-title">仓库模板设置</span>
                    <div class="tableShippingWrap">
                        <table class="tableShippingTable n-table edmitTable activeEdmitTable">
                            <thead>
                                <tr>
                                    <th style="width: 272px;">店铺名称</th>
                                    <th style="width: 282px;">发货仓</th>
                                </tr>
                            </thead>
                            <tbody id="tbody_sippingWarehouse_data_{{:#getIndex()}}">
                                {{for ShopFieldMapping ~pIndex=#getIndex() ~Code=Code}}
                                <tr>
                                    <td>
                                        <div class="sipping-name">
                                            <span class="n-smallIcon {{:PlatformType}}"></span>
                                            <span class="shopName">{{:ShopName}}</span>

                                        </div>
                                    </td>
                                    <td>
                                        <div class="rightOperateWrap">
                                            <div class="catePropsItemWrap ">

                                                <div class="n-mySelect n-single catePropsItem n-myCommonSelect {{:SlectId ? 'hasActive':'addWarnInput'}}" style="width: 240px;">
                                                    <div class="n-mySelect-title">
                                                        <div class="n-mySelect-title-left">
                                                            <span class="n-mySelect-title-placeholder">请选择</span>
                                                            {{for MappingDetailsList ~SlectId=SlectId}}
                                                            {{if Id == ~SlectId}}
                                                            <span class="n-mySelect-title-chooseItem">{{:Name}}</span>
                                                            {{/if}}
                                                            {{/for}}
                                                            {{if !SlectId}}
                                                            <span class="n-mySelect-title-chooseItem"></span>
                                                            {{/if}}
                                                        </div>
                                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                                    </div>
                                                    <div class="n-mySelect-showContent02" style="width: 260px;">
                                                        <ul class="n-mySelect-showContent-ul">
                                                            {{for MappingDetailsList ~ShopId=ShopId  }}
                                                            <li class="n-mySelect-showContent-ul-li" data-shopid="{{~ShopId}}" data-id="{{:Id}}" data-name="{{:Name}}" onclick="selectShippingWarehouseTemplate.bind(this)('{{:Id}}','{{:Name}}','{{:~ShopId}}','{{:~pIndex}}')">{{:Name}}</li>
                                                            {{/for}}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <span class="iconfont icon-tongbu n-dColor mL8 hover" onclick="refreshShippingWarehouseTemplateFun.bind(this)('{{:ShopId}}','{{:~Code}}')"></span>
                                            <span class="iconfont icon-a-add1x n-dColor mL8 hover" onclick="addShippingWarehouseTemplateFun.bind(this)()"></span>
                                        </div>
                                    </td>
                                </tr>
                                {{/for}}
                            </tbody>
                        </table>

                    </div>
                </div>
            </li>
        </ul>

        {{/for}}
    </div>
</script>


<script id="basePriceSet_temp" type="text/x-jsrender">
    {{for data}}
    <li class="setpriceWrap-item {{:IsChoose?'setpriceWrap-active':''}}">
        <div class="setpriceWrap-item-left">
            <div class="setpriceWrap-item-left-up" style="display:flex;align-items:center;">
                <i>{{:#getIndex()+1}}</i>
                {{if Type=="ByDouyinPrice"}}
                <span class="setpriceWrap-item-left-up-title setpriceWrap-item-title" id="">取抖音资料的商品售价</span>
                {{/if}}
                {{if  Type=="ByPurChasePrice"}}
                <span class="setpriceWrap-item-left-up-title" style="display:flex;align-items:center;">
                    取商品的采购价：售价<s class="sumIcon">=</s>采购价<s class="sumIcon">×</s>
                    <div class="n-inputWrap {{:Percent==""?'warnInput':''}}">
                        <input placeholder="请输入" type="text" class="layui-input n-layui-input" style="width:80px;" value='{{:Percent}}' onchange="changeBasePrice.bind(this)('ByPurChasePrice_Percent')">
                        <span class="input-num">%</span>
                    </div>
                    <s class="sumIcon">+</s>
                    <div class="n-inputWrap">
                        <input type="text" class="layui-input n-layui-input" style="width:100px;" placeholder="0" value='{{:Num==""?0:Num}}' onchange="changeBasePrice.bind(this)('ByPurChasePrice_Num')">
                        <span class="input-num">元</span>
                    </div>
                </span>
                {{/if}}
                {{if  Type=="ByDistributorPrice"}}
                <span class="setpriceWrap-item-left-up-title" style="display:flex;align-items:center;">
                    取商品的分销价：售价<s class="sumIcon">=</s>分销价<s class="sumIcon">×</s>
                    <div class="n-inputWrap {{:Percent==""?"warnInput":""}}">
                        <input placeholder="请输入" type="text" class="layui-input n-layui-input" style="width:80px;" value='{{:Percent}}' onchange="changeBasePrice.bind(this)('ByDistributorPrice_Percent')">
                        <span class="input-num">%</span>
                    </div>
                    <s class="sumIcon">+</s>
                    <div class="n-inputWrap">
                        <input type="text" class="layui-input n-layui-input n-input-price" style="width:100px;" placeholder="0" value='{{:Num==""?0:Num}}' onchange="changeBasePrice.bind(this)('ByDistributorPrice_Num')">
                        <span class="input-num">元</span>
                    </div>
                </span>
                {{/if}}
            </div>

            {{if Type=="ByPurChasePrice"}}
            <div class="setpriceWrap-item-left-down">例：采购价为10元，则售价为：10 ×{{:Percent?Percent+"%":1}}+{{:Num?Num:0}}  ={{:~PriceCount(Percent,Num)}}元</div>
            {{/if}}

            {{if Type=="ByDistributorPrice"}}
            <div class="setpriceWrap-item-left-down">例：分销价为10元，则售价为：10 ×{{:Percent?Percent+"%":1}}+{{:Num?Num:0}} = {{:~PriceCount(Percent,Num)}}元</div>
            {{/if}}
        </div>
        <div class="setpriceWrap-item-right">
            <div class="setpriceWrap-item-right-item">
                <div class="n-form-switch m-switch {{:IsChoose?'active':''}}" onclick="changePriceSwitchFun('{{:#getIndex()}}','{{:Type}}')"><i class="n-form-switch-icon"></i></div>
            </div>
            <div class="setpriceWrap-item-right-item {{:#getIndex()==0?'stop':''}}" onclick="BasePriceSetMoveUpOrDown.bind(this)('{{:#getIndex()}}',true)">
                <i class="iconfont icon-a-arrow-up1x"></i>
                <span>上移</span>
            </div>
            <div class="setpriceWrap-item-right-item {{:#getIndex()==2?'stop':''}}" onclick="BasePriceSetMoveUpOrDown.bind(this)('{{:#getIndex()}}',false)">
                <i class="iconfont icon-a-arrow-down1x"></i>
                <span>下移</span>
            </div>
        </div>
    </li>
    {{/for}}
</script>
// 单买价
<script id="baseSinglePriceSet_temp" type="text/x-jsrender">
    <li class="setpriceWrap-item">
        <div class="setpriceWrap-item-left">
            <div class="setpriceWrap-item-left-up" style="display:flex;align-items:center;">
                <span class="setpriceWrap-item-left-up-title" style="display:flex;align-items:center;">
                    单买价<s class="sumIcon">=</s>拼单价<s class="sumIcon">+</s>
                    <div class="n-inputWrap">
                        <input type="text" class="layui-input n-layui-input" style="width:100px;" placeholder="0" value='{{:data.SinglePrice_Num }}' onchange="changeBasePrice.bind(this)('SinglePrice_Num')">
                        <span class="input-num">元</span>
                    </div>
                </span>
            </div>
            <div class="setpriceWrap-item-left-down" style="padding-left: 0px;">例：拼单价为10元，则单买价为：10 +{{:data.SinglePrice_Num?data.SinglePrice_Num:0}}  ={{:~PriceAddCount(data.SinglePrice_Num, 10)}}元</div>
        </div>
    </li>
</script>
// 参考价
<script id="baseReferencePriceSet_temp" type="text/x-jsrender">
    <li class="setpriceWrap-item">
        <div class="setpriceWrap-item-left">
            <div class="setpriceWrap-item-left-up" style="display:flex;align-items:center;">
                <span class="setpriceWrap-item-left-up-title" style="display:flex;align-items:center;">
                    参考价<s class="sumIcon">=</s>SKU最高单买价<s class="sumIcon">+</s>
                    <div class="n-inputWrap">
                        <input type="text" class="layui-input n-layui-input" style="width:100px;" placeholder="0" value='{{:data.ReferencePrice_Num}}' onchange="changeBasePrice.bind(this)('ReferencePrice_Num')">
                        <span class="input-num">元</span>
                    </div>
                </span>
            </div>
            <div class="setpriceWrap-item-left-down" style="padding-left: 0px;">例：SKU最高单买价为10元，则参考价为：10 +{{:data.ReferencePrice_Num?data.ReferencePrice_Num:0}}  ={{:~PriceAddCount(data.ReferencePrice_Num, 10)}}元</div>
        </div>
    </li>
</script>

<script type="text/javascript">

    $.views.helpers({
        PriceCount: function (Percent, Num) {
            var Percent = Percent ? Percent : 100;
            var Num = Num ? Num : 0;
            var Sum = (Percent / 100 * 10) - 0 + (Num - 0);
            return Sum;
        },
        PriceAddCount: function (Num,Num2) {
            var Sum = parseFloat(Num) + parseFloat(Num2);
            return Sum || Num2;
        },
    });

</script>