@{
    ViewBag.Title = "编辑平台商品";
    ViewBag.MenuId = "CreateBasePlatformProductKs";
    ViewBag.MenuActive = "CreateBasePlatformProductKs";
    Layout = "~/Views/Shared/_CloudPlatformLayout.cshtml";
}
@Styles.Render("~/css/CreateBasePlatformProduct")
@section Header {
    <style type="text/css">
    </style>
}
<div class="newCreateProductWrap">
    <div class="n-writtenWrp" id="">
        <div class="n-writtenWrp-title">填写提示</div>
        <ul class="n-writtenWrp-main" id="writtenWarnContent"></ul>
    </div>
    @*<div class="newCreateProductWrap-left" onclick="closeCretaeProductShow()"></div>*@
    <div class="newCreateProductWrap-left"></div>
    <div class="full-mask-content-wrapper" style="width: 888px; background-color: #edf0f4; min-width: 888px;">
        <div class="full-mask-content" id="createBasePlatformProductContent">
            <div class="full-mask-main" style="padding-bottom:72px;">
                <div class="createBaseProductWrap">

                    <div class="n-alert n-alert-03 isUpdateTip" style=" margin: 16px 16px 0 16px;position:relative;display:none;">
                        <i class="iconfont icon-gantan"></i><i class="iconfont icon-a-close1x"></i>
                        您的基础资料规格发生了变更，是否同步最新规格信息到平台资料  <span class="n-dColor hover" onclick="CreateBasePlatformProductModule.GetTranBaseProductSku()">立即同步更新</span>
                    </div>
                    <div class="n-main" style="padding-bottom: 0;" id="category">
                        <div class="n-layui-mywrap" style="width: 100%;">
                            <div class="n-layui-mywrap-title" style="justify-content:space-between;display:flex;align-items:center;">
                                <span class="n-font4"><span class="n-sColor requiredIcon f12">*</span>商品类目</span>
                                <!-- <a class="n-dColor" style="font-size:14px;" target="_blank" href="https://school.jinritemai.com/doudian/web/article/101830?from=shop_table&btm_ppre=a4977.b4369.c0.d0&btm_pre=a4977.b6444.c0.d0&btm_show_id=2406dd1e-2c4d-47d1-bd34-4e347a3c230a">类目资质要求</a> -->
                            </div>
                            <ul class="layui-content-ul">
                                <li class="categoryMeuItem">
                                    <div class="categoryMeu-wrap" id="categoryMeu"></div>
                                    <div class="input-warnTitle">该项为必填项，请完善</div>
                                    <div class="intelligence-categoryMeu" id="intelligence-categoryMeu" style="display:none;">
                                        <div class="intelligence-categoryMeu-left">
                                            <div class="intelligence-title">智能推荐类目</div>
                                            <div class="intelligence-categoryMeuInfo"></div>
                                        </div>
                                        <div class="intelligence-categoryMeu-right n-mButton n-sActive" onclick="CreateBasePlatformProductModule.useCategory()">立即使用</div>
                                    </div>
                                </li>

                                <li class="allCatePropsWrap" id="allCatePropsWrap" style="display:none;">
                                    <div id="requiredCateProps">
                                        <span class="layui-content-ul-title" style="margin-bottom:0;">重要属性 <i id="hasCatePropsTotal">0</i>/<i class="catePropsTotal" id="catePropsTotal">0</i></span>
                                        <div class="catePropsWrap" id="catePropsWrap"></div>
                                        <div class="catePropsBtnWrap" id="addCatePropsBtns">
                                            <span class="n-mButton n-pActive" onclick="CreateBasePlatformProductModule.addCateProps()"><i class="iconfont icon-a-add1x"></i>添加材质</span>
                                        </div>
                                    </div>

                                    <div style="margin-top:16px;" id="otherCateProps">
                                        <span class="layui-content-ul-title" style="margin-bottom:0;">其他属性 <i id="hasCatePropsTotal02">0</i>/<i class="catePropsTotal" id="catePropsTotal02">0</i></span>
                                        <div class="catePropsWrap hideMorewCatePropsWrap" id="catePropsWrap02"></div>
                                        <div class="showMoreAddCatePropsBtnsWrap" id="showMoreAddCatePropsBtnsWrap" style="display: block;">
                                            <div class="showMoreAddCatePropsBtns n-dColor hover" onclick="showMoreCateProps.bind(this)()"><span>展开</span><i class="iconfont icon-down"></i></div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="n-main" style="padding-bottom: 0;" id="productInfo">
                        <div class="n-layui-mywrap" style="width:100%">
                            <div class="n-layui-mywrap-title">
                                <span class="n-font4">商品信息 </span>
                            </div>
                            <ul class="layui-content-ul">
                                <li>
                                    <span class="layui-content-ul-title"><span class="n-sColor requiredIcon f12">*</span>商品名称</span>
                                    <div class="n-inputWrap">
                                        <input type="text" class="layui-input n-layui-input"
                                               style="width: 100%;padding-right:60px;"
                                               oninput="CreateBasePlatformProductModule.inpuListenInput.bind(this)('productFullName')"
                                               onfocus="CreateBasePlatformProductModule.inpuFormInputVal.bind(this)('productFullName')"
                                               onblur="CreateBasePlatformProductModule.checkFormInputVal.bind(this)('productFullName')"
                                               name="productFullName" placeholder="请输入内容">
                                        <span class="input-warnTitle">请输入15-120个字符（8-30个汉字）</span>
                                        <span class="input-num">
                                            <i class="titleLength" id="productFullNameLen">0</i>/60
                                        </span>
                                    </div>
                                </li>
                                <li>
                                    <span class="layui-content-ul-title"><span class="n-sColor requiredIcon f12">*</span>主图</span>
                                    <div style="display:flex;align-items:center;">
                                        <div class="productPicWrap" id="newProductPicShow">

                                        </div>
                                        <span class="c04">主图最多上传10张</span>
                                    </div>
                                    <div class="input-warnTitle productPicWrap-inputWarnTitle" style="margin-top:8px;">最少1张主图</div>
                                </li>

                                <li style="align-items: flex-start;">
                                    <span class="layui-content-ul-title"><span class="n-sColor requiredIcon f12">*</span>详情</span>
                                    <div class="decorateWrap">
                                        <div class="decorateContent">
                                            <div class="decorateContent-up">
                                                <span contenteditable="true"
                                                      class="decorateContent-up-btn n-mButton n-sActive" id="productPics"
                                                      style="display: flex; align-items: center;">
                                                    <i class="iconfont icon-a-image-add1x1 upPic-icon"
                                                       style="margin-bottom:0;"></i>
                                                    <img class="onloadPic-icon"
                                                         src="/Content/images/loading_small.gif" />
                                                    <i>上传图片</i>
                                                </span>
                                                <div class="decorateContent-up-aXinc-ImgEditDesc">
                                                    <span class="c04">已上传</span>
                                                    <span class="c09" style="padding:0 3px;">
                                                        <i id="readDetailsPicsNumShow">0</i>
                                                        <span>/50</span>
                                                    </span>
                                                    <span class="c04">张，单张图大小 5M 以内</span>
                                                </div>
                                            </div>
                                            <div class="decorateContent-down" id="productDetailsPicWrap"></div>
                                        </div>
                                        <div class="productDesc n-inputWrap">
                                            <textarea class="layui-input n-layui-input" placeholder="请输入商品描述" maxlength="2000"  
                                                oninput="CreateBasePlatformProductModule.inpuListenInput.bind(this)('productDesc')"
                                                value="" name="productDesc" 
                                            >
                                            </textarea>
                                            <span class="input-num"><i class="titleLength">0</i>/2000</span>
                                        </div>
                                    </div>
                                    <div class="input-warnTitle decorateWrap-inputWarnTitle" style="margin-top:8px;">最少1张详情图</div>
                                </li>
                            </ul>
                        </div>

                    </div>

                    <div class="n-main" id="productSku">
                        <div class="n-layui-mywrap" style="width: 100%; padding: 0;">
                            <div class="n-layui-mywrap-title" style="margin:0 16px;">
                                <span class="n-sColor requiredIcon f12">*</span><span class="n-font4">商品规格</span>
                            </div>
                            <ul class="addSkuAttributes-ul" id="setSkuAttr_content" style="margin:0 16px;">
                                <li class="addSku-wrap" style="align-items: flex-start;">
                                    <div id="addSkuWrap"></div>
                                </li>
                                <li class="addSku-wrap" id="addSkuBtnWrap">
                                    <div class="addSkuAttributesWrap">
                                        <span class="layui-content-ul-title"></span>
                                        <div class="addSkuBtn n-dColor hover" id="addSkuBtn"
                                             onclick="CreateBasePlatformProductModule.addSkuAttributes()">
                                            <i class="iconfont icon-jia-copy1"
                                               style="margin-bottom:0;margin-right:5px;"></i><span>添加尺寸或颜色等规格类型</span>
                                        </div>
                                    </div>
                                </li>
                                <li class="addSkuBtnWarn">新增/删除规格请点击<i class="n-dColor hover" onclick="goToBaseProductEdmitFun()">前往基础资料</i>操作，操作完成后点击保存并返回完善资料即可。</li>
                            </ul>
                            @*<div class="skuTableWrap" id="skuTableWrap"></div>*@

                            <div class="skuTableWrap" id="newSkuTableWrap" style="margin-bottom:0;"></div>
                            @* <div class="referencePriceInputWrap">
                                <span class="n-inputTitle">商品参考价</span>
                                <i class="iconfont icon-a-help-circle1x"><span class="n-tooltip n-leftdown">参考价即吊牌价、市场价、需高于商品单买价，并且不得超过单买价的5倍</span></i>
                                <div class="priceInputWrap n-inputWrap">
                                    <input type="text" class="n-layui-input priceInput" name="ReferencePrice" onfocus="CreateBasePlatformProductModule.focusReSkuForm.bind(this)('ReferencePrice')" onchange="CreateBasePlatformProductModule.changeSkuForm.bind(this)(0,0,'ReferencePrice')" placeholder="0.00" />
                                    <i class="priceIcon">￥</i>
                                </div>
                            </div> *@
                        </div>
                    </div>

                    <div class="n-main">
                        <div class="n-layui-mywrap" style="width: 100%;">
                            <div class="n-layui-mywrap-title" style="justify-content:space-between;display:flex;">
                                <span class="n-font4">隐私设置</span>
                                <div class="n-layui-mywrap-title-right"></div>
                            </div>
                            <ul class="layui-content-ul">
                                <li class="setPrivacy" style="flex-direction:row;align-items:center;">
                                    <div class="setPrivacy-left">分销商使用快手资料</div>
                                    <div class="setPrivacy-right">
                                        <label>
                                            <input type="radio" name="IsPublic" value="1" checked />
                                            <span>允许</span>
                                        </label>
                                        <i class="iconfont icon-a-help-circle1x"><span class="n-tooltip n-leftdown">下游商家铺货时可以优先使用您维护好的快手资料，铺货更方便。</span></i>
                                        <label>
                                            <input type="radio" name="IsPublic" value="0" />
                                            <span>不允许</span>
                                        </label>
                                        <i class="iconfont icon-a-help-circle1x"><span class="n-tooltip n-leftdown">下游商家铺货时只能使用您发布的货盘资料，无法查看您编辑的快手资料。</span></i>

                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>




                </div>
                <div class="adialog-bindsupplier layui-layer-wrap" id="batchMappingSupplier">
                    <div class="bindsupplier-title">
                        <div>
                            选择供货类型<i class="hasProductBindTip"
                                     style="display: none;">该厂家已绑定商品，请先更换厂家，直接解除绑定，系统默认为自营供货</i>
                        </div>
                    </div>
                    <div class="bindsupplier-main" style="margin-bottom: 5px;">
                        <label>
                            <input type="radio" name="supplier-type-rdo" value="self"
                                   onchange="CreateBasePlatformProductModule.ChangeSupplierType()"><span>自营供货</span>
                        </label>
                        <label>
                            <input type="radio" name="supplier-type-rdo" value="supplier"
                                   onchange="CreateBasePlatformProductModule.ChangeSupplierType()"><span>厂家供货</span>
                        </label>
                    </div>
                    <div class="select-supplier" id="select_supplier">
                        <div id="supplier_name_select" class="Supplier selectWrap" name="supplier-name-select"></div>
                    </div>

                    <div class="bindsupplier-main-ul" id="ul_self">
                        <div class="bindsupplier-main-ul-title">自营供货:已关联的同款商品订单推送规则</div>
                        <label class="bindsupplier-main-ul-li">
                            <input type="radio" name="self-config-rdo" value="all">
                            <span>已关联的同款商品订单推送规则</span>
                        </label>
                        <label class="bindsupplier-main-ul-li">
                            <input type="radio" name="self-config-rdo" value="oneself">
                            <span>推送我的店铺同款订单为自己发货</span>
                        </label>
                        <label class="bindsupplier-main-ul-li">
                            <input type="radio" name="self-config-rdo" value="other">
                            <span>只推送下游商家的同款商品订单为自己发货</span>
                        </label>
                    </div>

                    <div class="bindsupplier-main-ul" id="ul_supplier">
                        <div class="bindsupplier-main-ul-title">厂家供货:已关联的同款商品代发订单推送规则</div>
                        <label class="bindsupplier-main-ul-li">
                            <input type="radio" name="supplier-config-rdo" value="all">
                            <span>推送我的自营店铺同款和下游商家的同款商品订单</span>
                        </label>
                        <label class="bindsupplier-main-ul-li">
                            <input type="radio" name="supplier-config-rdo" value="oneself">
                            <span>只推送我下游商家的同款商品订单代发</span>
                        </label>
                        <label class="bindsupplier-main-ul-li">
                            <input type="radio" name="supplier-config-rdo" value="other">
                            <span>只推送我绑定自营店铺的同款商品订单代发</span>
                        </label>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="createBase-footer" style="justify-content:flex-start">
        <span class="n-mButton" style="margin-right:10px;" onclick="CreateBasePlatformProductModule.submitCreateData.bind(this)()" id="submitCreateDataBtn">确认新建</span>

        <span class="n-mButton n-sActive" style="margin-right:10px;"
              onclick="CreateBasePlatformProductModule.goBack()">取消</span>
    </div>
</div>

<script id="choosePlatformDailog_temp" type="text/x-jsrender">
    <div class="choosePlatformDailog">
        <ul class="choosePlatformWrap" id="choosePlatformWrap">
            {{for allPlatformData}}
            <li class="choosePlatformWrap-item">
                <label>
                    <input type="checkbox" name="name" value="" {{:IsCheck?'checked':''}} onchange="changePlatformStatus.bind(this)('{{:Name}}')" />
                    <span class="choosePlatformWrap-icon"></span>
                </label>
            </li>
            {{/for}}
        </ul>
    </div>
</script>


<!-- 添加商品规格 -->
<script id="addSkuWrap_Skus" type="text/x-jsrender">
    <ul id="addSkuWrap_Skus_ul">
        {{for meuResultData ~isShowImg=isShowImg ~operateType=operateType ~attribute=attribute ~isSelectSkuType=isSelectSkuType}}
        {{if IsEdit}}
        <li class="addSkuWrap-Skus" data-index="{{:#getIndex()}}" data-mapped="{{:SelectItem.AttributeName?SelectItem.AttributeName:''}}">
            <div class="addSkuWrap-Skus-main">
                <div class="addSkuWrap-Skus-main-title n-font5">规格类型</div>
                <div class="addSkuWrap-Skus-main-select">
                    <span class="handle iconfont icon-a-move1x1"></span>

                    {{if ~operateType=="edmit" && !~isSelectSkuType }}

                    {{for newWareHouseSkuAttributeNameData ~sId=SelectItem.Id ~pId=Id}}
                    {{if ~sId==Id}}
                    <div class="addSkuWrap-Skus-main-title2">
                        {{:Name}}
                    </div>
                    @*<input type="text" class="layui-input n-layui-input" placeholder="请输入" value="{{:Name}}"
                        onblur="CreateBasePlatformProductModule.edmitSkuValue.bind(this)('{{:~pId}}','{{:Name}}','{{:OldName?OldName:Name}}')">*@
                    {{/if}}
                    {{/for}}
                    {{else ~isSelectSkuType}}
                    {{for newWareHouseSkuAttributeNameData ~sId=SelectItem.Id ~pId=Id}}
                    {{if ~sId==Id}}
                    <input type="text" class="layui-input n-layui-input" placeholder="请输入" value="{{:Name}}"
                        onblur="CreateBasePlatformProductModule.edmitSkuValue.bind(this)('{{:~pId}}','{{:Name}}','{{:OldName?OldName:Name}}')">
                    {{/if}}
                    {{/for}}
                   @*<select class="notMapped n-select" name="" id="mapped_{{:Id}}" onchange='CreateBasePlatformProductModule.newMappingSelectChange(this,"{{:Id}}")'
                            onclick='CreateBasePlatformProductModule.clickSelectChange(this,"{{:Id}}")'>
                        {{for newWareHouseSkuAttributeNameData ~sId=SelectItem.Id}}
                        {{if ~sId==Id}}
                        <option selected data-id='{{:Id}}' value="{{:AttributeName}}">
                            {{:Name}}
                        </option>
                        {{else !isCheck}}
                        {{if AttributeName!="custom"}}
                        <option data-id='{{:Id}}' value="{{:AttributeName}}" name="{{:Name}}">
                            {{:Name}}
                        </option>
                        {{else}}
                        <option data-id='{{:Id}}' value="{{:AttributeName}}" style="color:#0888FF" name="{{:Name}}">
                            {{:Name}}
                        </option>
                        {{/if}}
                        {{/if}}
                        {{/for}}
                    </select>*@

                    {{/if}}
                </div>
            </div>

            <div class="addSkuWrap-Skus-main">
                {{if SelectItem.special_values && SelectItem.special_values.length>0}}
                <div class="addSkuWrap-Skus-main-radioBox" style="display: flex;align-items: center;">
                    {{for SelectItem.special_values ~Pid=Id ~index=#getIndex()}}
                    <label>
                        <input type="radio" name="IsSpecialSelect" value="{{:id}}" {{:~index==0 ? checked : ''}} />
                        <span>{{:name}}</span>
                    </label>
                    {{/for}}
                </div>
                {{/if}}
                {{if SelectItem.AttributeName}}
                <div class="addSkuWrap-Skus-main-title n-font5">
                    <span class="mR16">规格值</span>
                    {{if #getIndex()==0}}
                    <div style="display:flex;align-items:center;">
                        <span class="c04" style="margin-right:3px">添加规格图片</span>
                        <div class="n-form-switch m-switch {{:~isShowImg?'active':''}}"
                             onclick="CreateBasePlatformProductModule.switchBtn.bind(this)()">
                            <i class="n-form-switch-icon"></i>
                        </div>
                    </div>
                    {{/if}}
                </div>
                {{/if}}

                <ul class="showSkuValue" id="showSkuValue_{{:Id}}" data-id="{{:Id}}">
                    {{for SelectItem.AttributeData ~Pid=Id ~index=#getIndex() ~isUnity=SelectItem.isUnity ~IsAllow=IsAllow}}
                    <li class="showSkuValue-item" data-index="{{:#getIndex()}}">
                        <div class="showSkuValue-item-up">
                            <span class="cHandle iconfont icon-a-move1x1"></span>
                            {{if ~index==0 && ~isShowImg}}
                            <div class="showSkuValue-item-down n-active02">
                                {{if Img}}
                                <div class="newSkuPicShow-item">
                                    <img class="showSkuValue-item-down-img" src="{{:~transformImgSrc(Img)}}" alt="">
                                    <span class="iconfont icon-laji"
                                          onclick="CreateBasePlatformProductModule.delSkuProductPic('{{:Id}}')"></span>
                                </div>
                                {{else}}
                                <div class="showSkuValue-item-down-upimg" id="{{:Id}}">
                                    <span class="iconfont icon-a-image-add1x1 upPic-icon c09"></span>
                                </div>
                                {{/if}}
                                <div class="img-warnTitle">必填</div>
                            </div>
                            {{/if}}
                            {{if ~isUnity}}
                            {{if support_remark}}
                            <div id="{{:Id+'input-box'}}" style="width:100%;">
                                <input type="text" class="layui-input n-layui-input hasData-input {{:IsRequiredTip?'addWarnInput':''}}" id="{{:Id+'input'}}" value="{{:IsRequiredTip ? '':Value}}" onblur="CreateBasePlatformProductModule.inputSkuValue(this,'{{:Value }}','{{:IsRequiredTip }}','{{:~index}}')" onclick="CreateBasePlatformProductModule.setSkuAttributes(this, '{{:~Pid}}', '{{:Id}}', '{{:~index}}','{{:#getIndex()}}')">
                            </div>

                            <input type="text" style="width:240px;margin-left:4px;" class="layui-input n-layui-input " placeholder="备注" data-index="{{:#getIndex()}}" value="{{:Remark ? Remark :remark}}" id="{{:Id+'inputRemark'}}" onblur="CreateBasePlatformProductModule.inputSkuAttributes(this, '{{:~Pid}}', '{{:Id}}', '{{:~index}}','{{:#getIndex()}}',true)">

                            {{else}}
                            <div id="{{:Id+'input-box'}}" style="width:100%;">
                                <input type="text" class="layui-input n-layui-input hasData-input {{:IsRequiredTip?'addWarnInput':''}}" onblur="CreateBasePlatformProductModule.inputSkuValue(this,'{{:Value }}','{{:IsRequiredTip }}','{{:~index}}')" placeholder="{{:IsRequiredTip ? Value : ''}}" id="{{:Id+'input'}}" value="{{:IsRequiredTip ? '':Value}}" onclick="CreateBasePlatformProductModule.setSkuAttributes(this, '{{:~Pid}}', '{{:Id}}', '{{:~index}}','{{:#getIndex()}}')">
                            </div>
                            {{/if}}
                            {{else}}
                            <input type="text" class="layui-input n-layui-input hasData-input" value="{{:Value}}" onblur="CreateBasePlatformProductModule.edmitSkuAttributes(this, '{{:~Pid}}', '{{:Id}}', '{{:#getIndex()}}','{{:OldValue?OldValue:Value}}')">
                            {{/if}}


                            @** onblur="CreateBasePlatformProductModule.edmitSkuAttributes(this, '{{:~Pid}}', '{{:Id}}', '{{:#getIndex()}}','{{:OldValue?OldValue:Value}}')"*@
                            @*<input type="text" style="width:240px;" class="layui-input n-layui-input hasData-input" value="{{:Value}}" onblur="CreateBasePlatformProductModule.edmitSkuAttributes(this, '{{:~Pid}}', '{{:Id}}', '{{:#getIndex()}}','{{:OldValue?OldValue:Value}}')">*@
                            <span class="iconfont icon-icon_shanchu-" onclick="CreateBasePlatformProductModule.delSkuAttributesValue(this,'{{:~Pid}}','{{:Id}}')"></span>
                        </div>
                        {{if IsRequiredTip}}
                        <div id="IsRequiredTip_{{:Id}}" class="isRequiredTip n-sColor {{:~index == 0 && ~isShowImg? "n-mySelect-showContentImg":"n-mySelect-showContentBox"}}">匹配快手规格失败，源规格：{{:sourceValues}}</div>
                        {{else}}
                        <div id="IsRequiredTip_{{:Id}}" class="isRequiredTip c04 {{:~index == 0 && ~isShowImg? "n-mySelect-showContentImg":"n-mySelect-showContentBox"}}">源规格：{{:sourceValues}}</div>
                        {{/if}}
                        <div style="display: none;  position: unset;" class="n-mySelect-showContent {{:~index == 0 && ~isShowImg? "n-mySelect-showContentImg":"n-mySelect-showContentBox"}}" id="{{:Id+'attributes'}}"></div>

                    </li>
                    {{/for}}
                </ul>
                {{if IsSelectSku}}
                <div class="showSkuValue-item addNewSkuValue {{:SelectItem.AttributeData.length==0?'frist-item':''}}">
                    <div class="showSkuValue-item-up">
                        <input class="layui-input n-layui-input" type="text" value="{{:Value}}" placeholder="请输入要添加规格值"
                               onblur="CreateBasePlatformProductModule.addAttributeValue(this,'{{:#getIndex()}}','{{:Id}}')">
                    </div>
                </div>
                {{/if}}
                <div class="showSkuValue-item showSkuValue-btns">
                    {{if IsSelectSku}}
                    <span class="n-sButton n-pActive" onclick="CreateBasePlatformProductModule.sureDelSkuAttributes(this,'{{:Id}}','{{:SelectItem.AttributeName}}')">删除</span>
                    {{else}}
                    <span class="n-sButton n-pActive" onclick="CreateBasePlatformProductModule.sureDelSkuAttributes(this,'{{:Id}}')">删除</span>
                    {{/if}}
                    <span class="n-sButton" onclick="CreateBasePlatformProductModule.changeShowAttributesStatus(this,'{{:Id}}',{{:IsSelectSku}})">完成</span>
                </div>
            </div>
        </li>
        {{else}}
        <li class="addSkuWrap-noEdit addSkuWrap-Skus" data-index="{{:#getIndex()}}" data-mapped="{{:SelectItem.AttributeName?SelectItem.AttributeName:''}}">
            <div class="addSkuWrap-Skus-main">
                <div class="addSkuWrap-Skus-main-title n-font5">{{:SelectItem.Name}}</div>
                <div class="addSkuWrap-Skus-main-select">
                    <span class="handle iconfont icon-a-move1x1"></span>
                    <div class="noEdit-show">
                        {{for SelectItem.AttributeData ~Pid=Id ~index=#getIndex() ~IsAllow=IsAllow}}
                        <div class="noEdit-showSkuValue-item">
                            {{if ~index==0 && ~isShowImg}}
                            <div class="showSkuValue-item-down">
                                {{if Img}}
                                <div class="newSkuPicShow-item">
                                    <img class="showSkuValue-item-down-img" src="{{:~transformImgSrc(Img)}}" alt="">
                                </div>
                                {{else}}
                                <div class="showSkuValue-item-down-upimg n-active" id="{{:Id}}">
                                    <span class="iconfont icon-a-image-add1x1 upPic-icon"></span>
                                </div>
                                {{/if}}
                            </div>
                            {{/if}}
                            <span class="noEdit-showSkuValue-item-text">{{:Value}}</span>
                        </div>
                        {{/for}}
                    </div>
                    <span class="n-sButton" onclick="CreateBasePlatformProductModule.changeHideAttributesStatus(this,'{{:Id}}')">编辑</span>

                </div>
            </div>
        </li>
        {{/if}}
        {{/for}}
    </ul>
</script>

<!-- 列表模样 -->
<script id="orderList_data_tr" type="text/x-jsrender">
    {{for rows}}
    <tr>
        <td>
            {{if ImageUrl}}
            <div id="picWrap{{:Id}}" class="table-tbody-skuImg">
                <img id="pic{{:Id}}" src="{{:ImageUrl}}">
                <i class="iconfont icon-laji" onclick="CreateBasePlatformProductModule.delSkuPic('{{:Id}}')"></i>
            </div>
            <span id="addSkuPic{{:Id}}" style="display: none;" class="iconfont icon-tubiaolunkuo- skuPic"
                  onclick='CreateBasePlatformProductModule.clickSkuPic(this,"{{:Id}}")'></span>
            {{else}}
            <div id="picWrap{{:Id}}" class="table-tbody-skuImg" style="display: none;">
                <img id="pic{{:Id}}" src="">
                <i class="iconfont icon-laji" onclick="CreateBasePlatformProductModule.delSkuPic('{{:Id}}')"></i>
            </div>
            <span id="addSkuPic{{:Id}}" class="iconfont icon-tubiaolunkuo- skuPic"
                  onclick='CreateBasePlatformProductModule.clickSkuPic(this,"{{:Id}}")'></span>
            {{/if}}
            <input style="display: none;" type="file" name="file" accept="image/jpeg, image/png, image/gif"
                   onchange='CreateBasePlatformProductModule.changeSkuPic(this,"{{:Id}}")'>

        </td>
        <td class="tableSku-skuShow">
            {{for Attributes}}
            <span><i>{{:AttributeName}}：</i>{{:AttributeValue}}</span>
            {{/for}}
        </td>
        <td>
            <input type="text" name="SkuCargoNumber" data-id="{{:Id}}" value="{{:SkuCargoNumber}}">
        </td>
        <td>
            <input type="text" name="CostPrice" data-id="{{:Id}}" value="{{:CostPrice}}">
        </td>
    </tr>
    {{/for}}

</script>

<!-- 列表模样 -->
<script id="table_data_temp" type="text/x-jsrender">
    {{if newWareHouseSkuLists.length>0}}
    <div class="skuTableWrap-title">
        <div>
            <span class="skuTableWrap-title-left">价格与库存</span>
            <div class="n-mySelect n-single">
                <div class="n-mySelect-title">
                    <div class="n-mySelect-title-left">
                        <span class="n-mySelect-title-placeholder">批量编辑</span>
                    </div>
                    <span class="iconfont icon-a-chevron-down1x"></span>
                </div>

            </div>

        </div>

        <span class="n-dColor hover" onclick="CreateBasePlatformProductModule.fullScreen()">
            <i class="iconfont icon-a-fullsreen1x"></i>
            <i class="iconfont icon-a-fullscreen-exit1x" style="display:none"></i>
            <span id="fullscreenText">全屏编辑</span>
        </span>
    </div>
    <div id="skuTable">
        <table class="n-table edmitTable">
            <thead>
                <tr>
                    {{if isShowImg}}
                    <th style="min-width:60px;max-width:60px">图片</th>
                    {{/if}}
                    {{for meuResultData}}

                    {{if IsSelectSku && SelectItem.AttributeData.length>0}}
                    <th style="width:100px">{{:SelectItem.Name}}</th>
                    {{/if}}
                    {{/for}}
                    <th style="width:100px">规格名</th>

                    {{for tableTdData}}
                    {{if AttributeName=="SkuCode"}}
                    <th style="width:150px" class="{{:AttributeName}}"><i class="tColor mR5">*</i>{{:Name}}<span class="hover dColor systemCreateCode" style="margin-left:5px" onclick='CreateBasePlatformProductModule.createSkuOuterId()'>系统生成</span></th>
                    {{else AttributeName=="StockCount"}}
                    <th style="width:150px" class="{{:AttributeName}}">{{:Name}}</th>
                    {{else}}
                    <th style="width:100px" class="{{:AttributeName}}">{{:Name}}</th>
                    {{/if}}
                    {{/for}}
                </tr>
            </thead>
            <tbody id="table_tbody_orderList">
                {{for newWareHouseSkuLists ~tableTdData=tableTdData ~isShowImg=isShowImg ~operateType=operateType}}
                <tr>
                    {{for #data ~index=#getIndex()}}
                    {{if AttributeName!='Attribute' }}
                    {{if AttributeName=="ImageUrl"}}
                    <td class="{{:AttributeName}}">
                        {{if Img}}
                        <img class="table-sku-img" src="{{:~transformImgSrc(Img)}}">
                        {{/if}}
                    </td>
                    {{else AttributeName=="customAttribute"}}
                    <td class="{{:AttributeName}} popoverCommon popoverCommon03">
                        <span>{{:Value}}</span>
                        <span class="popoverCommon-warn" style="width:260px;">无法修改规格名,请修改规格类型与规格值</span>
                    </td>
                    {{else AttributeName=="BaseProductSkuSupplierConfig"}}
                    <td class="{{:AttributeName}}">
                        {{if Value==""}}
                        <span class="dColor hover" onclick="CreateBasePlatformProductModule.BatchMappingSupplier('{{:~index}}')">设置</span>
                        {{else}}

                        {{if Value[0].IsSelf}}
                        <span class="dColor hover" onclick="CreateBasePlatformProductModule.BatchMappingSupplier('{{:~index}}')">自营</span>
                        {{else}}
                        <span class="dColor hover" onclick="CreateBasePlatformProductModule.BatchMappingSupplier('{{:~index}}')">厂家:{{:Value[0].SupplierFxUserName}}</span>
                        {{/if}}

                        {{/if}}
                    </td>
                    {{else AttributeName=="StockCount"}}
                    <td class="{{:AttributeName}}">
                        {{if ~operateType=="edmit"}}
                        <div class="edmitStockCountWrap">
                            <i class="dColor popoverCommon hover setStockCountWrap">
                                设置库存
                                <span class="popoverCommon-warn" style="width:80px;">
                                    <span class="setStockCount-warn">
                                        <span class="dColor hover" style="margin-bottom:5px" onclick="CreateBasePlatformProductModule.setWareHouseNum.bind(this)(true,'{{:~index}}')">手动入库</span>
                                        <span class="sColor hover" onclick="CreateBasePlatformProductModule.setWareHouseNum.bind(this)(false,'{{:~index}}')">手动出库</span>
                                    </span>
                                </span>
                            </i>
                            <div class="operateStockCount">
                                {{:Value}}

                                {{if inOrOutObj}}
                                <span class="wareHouseNum active">
                                    <i class="wareHoustyle">{{:inOrOutObj.InOrOut?'+':'-'}}</i>
                                    {{if inOrOutObj.InOrOut}}
                                    <input class="wareHouseNum-input" name="EntryIn" value="{{:inOrOutObj.ChangeStockCount}}" style="display:inline-block" type="text" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.EntryInOrOut.bind(this)('{{:~index}}',true);">
                                    <input class="wareHouseNum-input" name="StockOut" value="{{:inOrOutObj.ChangeStockCount}}" type="text" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.EntryInOrOut.bind(this)('{{:~index}}',false);">
                                    {{else}}
                                    <input class="wareHouseNum-input" name="EntryIn" value="{{:inOrOutObj.ChangeStockCount}}" type="text" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.EntryInOrOut.bind(this)('{{:~index}}',true);">
                                    <input class="wareHouseNum-input" name="StockOut" value="{{:inOrOutObj.ChangeStockCount}}" style="display:inline-block" type="text" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.EntryInOrOut.bind(this)('{{:~index}}',false);">
                                    {{/if}}
                                    {{if inOrOutObj.InOrOut}}
                                    <s class="wareHouseTitle" style="color: #99cc00">入库</s>
                                    {{else}}
                                    <s class="wareHouseTitle" style="color: #f29a1a">出库</s>
                                    {{/if}}
                                </span>
                                {{else}}
                                <span class="wareHouseNum">
                                    <i class="wareHoustyle">+</i>
                                    <input class="wareHouseNum-input" name="EntryIn" style="display:inline-block" type="text" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.EntryInOrOut.bind(this)('{{:~index}}',true);">
                                    <input class="wareHouseNum-input" name="StockOut" type="text" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.EntryInOrOut.bind(this)('{{:~index}}',false);">
                                    <s class="wareHouseTitle">入库</s>
                                </span>

                                {{/if}}

                            </div>
                        </div>

                        {{else}}
                        <input type="text" name="{{:AttributeName}}" id="" value="{{:Value}}" oninput="CreateBasePlatformProductModule.checkWareHouseStatus.bind(this)();" onchange="CreateBasePlatformProductModule.changeSkuForm.bind(this)('{{:~index}}','{{:AttributeName}}')" />
                        {{/if}}
                    </td>
                    {{else AttributeName=="skuJurisdiction"}}
                    <td class="{{:AttributeName}}">
                        <select name="" id="" class="skuJurisdictionSelect">
                            <option value="1">默认公开</option>
                            <option value="2">私密(仅自己可见，不对外分销)</option>
                        </select>
                    </td>
                    {{else AttributeName=="SkuCode"}}
                    <td class="{{:AttributeName}}">
                        <input type="text" data-type="{{:~operateType}}" name="{{:AttributeName}}" data-oldskucode="{{:Value}}" id="" value="{{:Value}}" onchange="CreateBasePlatformProductModule.changeSkuCodeForm.bind(this)('{{:~index}}','{{:AttributeName}}')" />
                    </td>
                    {{else}}
                    <td class="{{:AttributeName}}">
                        {{if isCustom}}
                        <span>{{:Value}}</span>
                        {{else}}
                        <input type="text" data-type="{{:~operateType}}" name="{{:AttributeName}}" id="" value="{{:Value}}" onchange="CreateBasePlatformProductModule.changeSkuForm.bind(this)('{{:~index}}','{{:AttributeName}}')" />
                        {{/if}}
                    </td>
                    {{/if}}
                    {{/if}}
                    {{/for}}
                </tr>
                {{/for}}
            </tbody>
        </table>
    </div>
    {{/if}}
</script>

@*初始化主图*@
<script id="newProductPicShow_temp" type="text/x-jsrender">
    <ul class="newProductPicShow" id="newProductPicShow_ul">
        {{for productImages}}
        <li class="newProductPicShow-item" data-index="{{:#getIndex()}}">
            <img src="{{:~transformImgSrc(TransitUrl)}}" alt="">
            <div class="pic-operateWrap">
                <div class="pic-operateWrap-up">
                    <span class="handle iconfont icon-yidongshu"></span>
                    <span class="iconfont icon-icon_shanchu-" onclick="CreateBasePlatformProductModule.delProductPic('{{:#getIndex()}}')"></span>
                </div>

            </div>

        </li>
        {{/for}}
    </ul>
    {{if productImages.length<5}}
    <div class="newProductPicShow-item addnewProductPicShow_item n-active" contenteditable="true" id="productPic_1">
        <span class="iconfont icon-a-image-add1x1 upPic-icon"></span>
        <img class="onloadPic-icon" src="/Content/images/loading_small.gif" />
        <span class="newProductPicShow-item-title">上传主图</span>
    </div>
    {{/if}}
</script>

<script id="detailsPics_temp" type="text/x-jsrender">
    <ul class="newProductPicShow" id="detailsPicsShow_ul">
        {{for productDetailsPics}}
        <li class="newProductPicShow-item" data-index="{{:#getIndex()}}">
            <img src="{{:~transformImgSrc(TransitUrl)}}">
            <div class="pic-operateWrap">
                <div class="pic-operateWrap-up">
                    <span class="handle iconfont icon-yidongshu"></span>
                    <span class="iconfont icon-icon_shanchu-" onclick="CreateBasePlatformProductModule.delDetailsPic('{{:#getIndex()}}')"></span>
                </div>

            </div>
        </li>

        {{/for}}
    </ul>

</script>

<script id="rawResultSkus_render" type="text/x-jsrender">
    <div class="rawSkusWrap" id="rawSkusWrap">
        <ul class="rawSkusWrap-main">
            {{for rawResultSkus ~index=index ~cIndex=cIndex}}
            {{if !isCheck}}
            <li class="rawSkusWrap-main-item" onclick="CreateBasePlatformProductModule.chooseRawResultSku.bind(this)('{{:Id}}','{{:~index}}','{{:~cIndex}}')">
                <div class="n-productInfo-img">
                    {{if ImageUrl}}
                    <img src="{{:~transformImgSrc(ImageUrl)}}" alt="" />
                    {{else}}
                    <img src="/Content/images/nopic.gif" alt="" />
                    {{/if}}
                </div>
                <div class="rawSkusWrap-main-text">
                    <div class="n-font5">{{:AttributesName}}</div>
                    <div class="n-font6">SKU 编码 {{:SkuCode}}｜采购价 ¥{{:SettlePrice}}｜成本价 ¥{{:CostPrice}}｜分销价 ¥{{:DistributePrice}}｜库存 {{:StockCount}}</div>
                </div>
            </li>
            {{/if}}
            {{/for}}
        </ul>
        {{if isCheck}}
        <div class="chooseSku">
            <div class="chooseSku-title">已匹配</div>
            <ul class="rawSkusWrap-main">
                {{for rawResultSkus}}
                {{if isCheck}}
                <li class="rawSkusWrap-main-item">
                    <div class="n-productInfo-img">
                        <img src="{{:~transformImgSrc(ImageUrl)}}" alt="" />
                    </div>
                    <div class="rawSkusWrap-main-text">
                        <div class="n-font5">{{:AttributesName}}</div>
                        <div class="n-font6">SKU 编码 {{:SkuCode}}｜采购价 ¥{{:SettlePrice}}｜成本价 ¥{{:CostPrice}}｜分销价 ¥{{:DistributePrice}}｜库存 {{:StockCount}}</div>
                    </div>
                </li>
                {{/if}}
                {{/for}}
            </ul>
        </div>
        {{/if}}
    </div>
</script>



<!-- 新建仓库弹窗 -->
<div class="createStoreDialog">
    <ul class="createStoreDialog-content">
        <li>
            <div class="createStoreDialog-title">仓库类型：</div>
            <div class="createStoreDialog-main"><span>自营仓库</span></div>
        </li>
        <li>
            <div class="createStoreDialog-title">仓库名称：</div>
            <div class="createStoreDialog-main">
                <input id="warehouseName-input" type="text" style="width: 353px;"
                       class="layui-input" placeholder="请输入仓库名称">
            </div>
        </li>

        <li>
            <div class="createStoreDialog-title">仓库地址：</div>
            <div class="createStoreDialog-main address">
                <input id="contact-input" type="text" class="layui-input" placeholder="联系人姓名">
                <input id="mobile-input" type="text" class="layui-input" placeholder="手机号码">
                <input id="telphone-input" type="text" class="layui-input" placeholder="固定电话">
                <span class="default-address">
                    <input onchange="CreateBasePlatformProductModule.DefaultAddressChange(this)"
                           type="checkbox">默认地址
                </span>
            </div>
        </li>
        <li class="lispantop">
            <div class="createStoreDialog-title"></div>
            <div class="createStoreDialog-main createStoreDialog-main-select">
                <label>
                    <select name="" id="province-select" nextcontrolid="city-select" deep="1" defaultopttext="==省==">
                        <option value="0" data-value="0">==省==</option>
                    </select>
                </label>
                <label>
                    <select name="" id="city-select" nextcontrolid="county-select" deep="2" defaultopttext="==市==">
                        <option value="0" data-value="0">==市==</option>
                    </select>
                </label>
                <label>
                    <select name="" id="county-select" deep="3" defaultopttext="==区==">
                        <option value="0" data-value="0">==区==</option>
                    </select>
                </label>
                <div class="adialog-addSupplier-textarea">
                    <textarea id="address-textarea" placeholder="详细地址"></textarea>
                </div>
            </div>
        </li>
    </ul>
</div>

@*目录属性----------------------------*@
@{ Html.RenderPartial("~/Views/BaseProduct/PlatformParth/Ks_CateProps.cshtml"); }


<script id="data_list_skuAttributes" type="text/x-jsrender">

    <div class="skuAttributes-warp {{:attribute.is_required?'required':'noRequired'}}" style="display:flex;align-items:center;">
        {{if attribute.is_required}}
        @*<span class="n-sColor requiredIcon">*</span>*@
        {{/if}}
        {{if attribute.value_display_style == "measure"}}
        {{for attribute.measure_templates ~index=#getIndex() ~attribute=attribute}}
        {{for value_modules ~sIndex=#getIndex()}}
        {{if input_type=="input"}}
        <div class="skuAttributes-warp-input">
            {{if units.length ==0}}
            <input class="n-mySelect-input layui-input n-layui-input n-mySelect-input-{{:module_id}}" style="width:120px;" placeholder="请输入" type="text" value="{{:value}}" name="name" data-index="{{:#getIndex()}}" />
            {{else}}
            <div class="n-mySelect flex" style="padding: 0 8px; width: 120px; " id="n-mySelect-{{:module_id}}">
                <input class="n-mySelect-input n-mySelect-input-{{:module_id}}" style="width: 100%; height: 100%; border: none;" placeholder="请输入" type="text" name="name" data-index="{{:#getIndex()}}" />

                <div class="flex hover unitsPropsItemWrap" data-moduleid="{{:module_id}}">
                    <span class="n-mySelect-title">{{:units[0].unit_name}}</span>
                    <span class="iconfont icon-a-chevron-down1x"></span>
                </div>
                <div class="n-mySelect-showContent n-mySelect-showContent-{{:module_id}}">
                    <ul class="n-mySelect-showContent-ul">
                        {{for units ~Value=''}}
                        <li class="n-mySelect-showContent-ul-li {{:~unit_index == #getIndex()?'activeItem':''}}" data-idx="{{:~index}}" data-value="{{:unit_name}}" data-unitid="{{:unit_id}}">{{:unit_name}}</li>
                        {{/for}}

                        @*<li class="n-mySelect-showContent-ul-li n-dColor" onclick="createBrandFun()"><i class="iconfont icon-a-add1x"></i><span>新建品牌</span></li>*@
                    </ul>
                </div>
            </div>
            {{/if}}
        </div>
        {{/if}}
        {{if suffix}}
        <span class="" style="padding:0 4px;">{{:suffix}}</span>
        {{/if}}
        {{/for}}
        {{/for}}
        {{else attribute.value_display_style == "text"}}
        <div class="skuAttributes-warp-input">
            <input class="n-mySelect-input layui-input n-layui-input" style="width:120px;" placeholder="请输入" type="text" name="name" data-index="{{:#getIndex()}}" />
        </div>
        {{else}}

        <div class="n-mySelect n-mySelect-property n-single catePropsItem {{:Value!=null && Value!=""?'hasActive':''}}">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-placeholder">请选择</span>
                    <span class="n-mySelect-title-chooseItem">{{:attribute.sell_property_name}}</span>
                </div>
                <span class="iconfont icon-a-chevron-down1x"></span>
            </div>
            <div class="n-mySelect-showContent">
                <ul class="n-mySelect-showContent-ul">
                    {{if attribute.property_values.length>0}}
                    {{for attribute.property_values ~index=#getIndex() ~attribute=attribute}}
                    <li class="n-mySelect-showContent-ul-li" data-idx="{{:~index}}" data-value="{{:sell_property_value_name}}" data-unitid="{{:sell_property_value_id}}">{{:sell_property_value_name}}</li>
                    {{/for}}
                    {{else}}
                    <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                    {{/if}}
                </ul>
            </div>
        </div>

        {{/if}}

    </div>
    {{if attribute.support_diy && attribute.value_display_style != "text" && attribute.value_display_style != "measure"}}
    <div class="addAttribute">
        <div class="addAttribute_addbtn n-dColor hover"><i class="iconfont icon-a-add1x"></i><span>新建规格属性</span> </div>
        <div class="addAttribute_box flex">
            <input class=" layui-input n-layui-input" style="width:120px;" placeholder="请输入规格值" type="text" value="" />
            <i class="iconfont icon-a-check1x addBtn hover" style="color: #5ad095;"></i>
            <i class="iconfont icon-a-close1x closeBtn hover" style="color: #ff9da5;"></i>
        </div>
    </div>
    {{/if}}

</script>
<script id="data_list_skuAttributesEdit_measure_templates" type="text/x-jsrender">
    {{if attribute.measure_templates && attribute.measure_templates.length >1}}
    <div class="n-mySelect n-mySelect-property n-single catePropsItem hasActive {{:Value!=null && Value!=""?'hasActive':''}}" style="margin-bottom:4px;">
        <div class="n-mySelect-title">
            <div class="n-mySelect-title-left templatesPropsItemWrap">
                <span class="n-mySelect-title-left-title ">选择规则</span>
                {{if attribute.template_id}}
                <span class="n-mySelect-title-chooseItem">{{:attribute.display_name}}</span>
                {{else}}
                <span class="n-mySelect-title-placeholder">请选择</span>
                {{/if}}
            </div>
            <span class="iconfont icon-a-chevron-down1x"></span>
        </div>
        <div class="n-mySelect-showContent n-mySelect-showContent-templates">
            <ul class="n-mySelect-showContent-ul">
                {{if attribute.measure_templates.length>0}}
                {{for attribute.measure_templates ~attribute=attribute}}
                <li class="n-mySelect-showContent-ul-li" data-idx="{{:#getIndex()}}" value="{{:template_id}}">{{:display_name}}</li>
                {{/for}}
                {{else}}
                <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                {{/if}}
            </ul>
        </div>
    </div>
    {{/if}}
</script>
<script id="data_list_skuAttributesEdit" type="text/x-jsrender">
    <div class="skuAttributes-warp " style="display: flex; align-items: center; margin-left: -4px;">
        {{if attribute.value_display_style == "measure"}}
        {{for attribute.value_modules ~sIndex=#getIndex() ~index=index }}
        {{if input_type=="input"}}
        <div class="skuAttributes-warp-input" style="margin-left:4px;">
            {{if units.length ==0}}
            <input class="n-mySelect-input layui-input n-layui-input n-mySelect-input-{{:module_id}}" style="width:120px;" placeholder="请输入" type="text" value="{{:values}}" name="name" data-index="{{:#getIndex()}}" />
            {{else}}
            <div class="n-mySelect flex" style="padding: 0 8px; width: 120px; " id="n-mySelect-{{:module_id}}">
                <input class="n-mySelect-input n-mySelect-input-{{:module_id}}" style="width: 100%; height: 100%; border: none;" placeholder="请输入" type="text" name="name" data-index="{{:#getIndex()}}" value="{{:values}}" />

                <div class="flex hover unitsPropsItemWrap" data-moduleid="{{:module_id}}">
                    {{if unit_name}}
                    <span class="n-mySelect-title">{{:unit_name }}</span>
                    {{else}}
                    <span class="n-mySelect-title">{{:units[0].unit_name}}</span>
                    {{/if}}
                    <span class="iconfont icon-a-chevron-down1x"></span>
                </div>
                <div class="n-mySelect-showContent n-mySelect-showContent-{{:module_id}}">
                    <div class="n-mySelect-search" onclick="event.stopPropagation()">
                        <input type="text" class="n-input searchInput" placeholder="请输入搜索内容" oninput="CreateBasePlatformProductModule.searchCateProps.bind(this)()">
                        <span class="iconfont icon-a-search1x1"></span>
                    </div>
                    <ul class="n-mySelect-showContent-ul">
                        {{for units ~Value=''}}
                        <li class="n-mySelect-showContent-ul-li {{:~unit_index == #getIndex()?'activeItem':''}}" data-idx="{{:~index}}" data-value="{{:unit_name}}" data-unitid="{{:unit_id}}">{{:unit_name}}</li>
                        {{/for}}

                        @*<li class="n-mySelect-showContent-ul-li n-dColor" onclick="createBrandFun()"><i class="iconfont icon-a-add1x"></i><span>新建品牌</span></li>*@
                    </ul>
                </div>
            </div>
            {{/if}}
        </div>
        {{/if}}
        {{if suffix}}
        <span class="" style="padding-left: 4px;">{{:suffix}}</span>
        {{/if}}
        {{/for}}
        {{else attribute.value_display_style == "text"}}
        <div class="skuAttributes-warp-input">
            <input class="n-mySelect-input layui-input n-layui-input" style="width:120px;" placeholder="请输入" type="text" name="name" data-index="{{:#getIndex()}}" />
        </div>
        {{else attribute.value_display_style == "cascader_multi_select" && navCateData && navCateData.length}}
        <div class="categoryMeu-wrap" id="categoryMeuWrap">
            <div class="categoryMeu-main" style="position:unset;flex-direction:row;">
                <div class="selectCategoryWrap">
                    <ul class="selectCategoryWrap-ul">
                        {{for navCateData }}
                        <li class="selectCategoryWrap-ul-li" data-valueid="{{:PropertyValueId}}" data-idx="{{:#index}}">
                            <span>{{:PropertyValueName}}</span>
                            <i class="iconfont icon-down"></i>
                        </li>
                        {{/for}}
                    </ul>
                </div>
                <div id="selectCategory_level"></div>
            </div>
        </div>

        {{else}}

        <div class="n-mySelect n-mySelect-property n-single catePropsItem {{:Value!=null && Value!=""?'hasActive':''}}">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    {{if attribute.sell_property_value_name}}
                    <span class="n-mySelect-title-chooseItem" style="display:block">{{:attribute.sell_property_value_name }}</span>
                    {{else}}
                    <span class="n-mySelect-title-placeholder">请选择</span>
                    {{/if}}
                </div>
                <span class="iconfont icon-a-chevron-down1x"></span>
            </div>
            <div class="n-mySelect-showContent">
                <div class="n-mySelect-search" onclick="event.stopPropagation()">
                    <input type="text" class="n-input searchInput" placeholder="请输入搜索内容" oninput="CreateBasePlatformProductModule.searchCateProps.bind(this)()">
                    <span class="iconfont icon-a-search1x1"></span>
                </div>
                <ul class="n-mySelect-showContent-ul">
                    {{if attribute.property_values.length>0}}
                    {{for attribute.property_values ~index=#getIndex() ~attribute=attribute}}
                    <li class="n-mySelect-showContent-ul-li" data-idx="{{:~index}}" data-value="{{:sell_property_value_name}}" data-unitid="{{:sell_property_value_id}}">{{:sell_property_value_name}}</li>
                    {{/for}}
                    {{else}}
                    <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                    {{/if}}
                </ul>
            </div>
        </div>

        {{/if}}
    </div>
    {{if attribute.support_diy && attribute.value_display_style != "text" && attribute.value_display_style != "measure"}}
    <div class="addAttribute">
        <div class="addAttribute_addbtn n-dColor hover"><i class="iconfont icon-a-add1x"></i><span>新建规格属性</span> </div>
        <div class="addAttribute_box flex">
            <input class=" layui-input n-layui-input" style="width:120px;" placeholder="请输入规格值" type="text" value="" />
            <i class="iconfont icon-a-check1x addBtn hover" style="color: #5ad095;"></i>
            <i class="iconfont icon-a-close1x closeBtn hover" style="color: #ff9da5;"></i>
        </div>
    </div>
    {{/if}}

</script>


<script id="data_list_skuAttributesEdit_categoryMeuWrap" type="text/x-jsrender">
    <div class="selectCategoryWrap">
        <ul class="selectCategoryWrap-ul">
            {{for navCateData }}
            <li class="selectCategoryWrap-ul-li" data-valueid="{{:property_value_id}}" data-idx="{{:#index}}">
                <span>{{:property_value_name}}</span>

            </li>
            {{/for}}
        </ul>
    </div>
</script>
@Scripts.Render("~/bundles/CreateBasePlatformProductKs");

@section scripts {
    <script type="text/javascript">
        var Suppliers = @(Html.Raw(ViewBag.Suppliers ?? "[]"));
        var WareHouseStatus = @(Html.Raw(ViewBag.WareHouseStatus));
        commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
        $.views.helpers({
            transformImgSrc: function (src) {
                var imgSrc = "/Content/images/nopic.gif";
                if (src) {
                    if (src.indexOf("http") == 0) {
                        imgSrc = src;
                    } else {
                        if (src != "//.") {
                            commonModule.CloudPlatformType = "Alibaba";
                            var token = $("#token_input").val() ? $("#token_input").val() : '';
                            imgSrc = src + '&platform=' + commonModule.CloudPlatformType + '&token=' + token;
                        } else {
                            imgSrc = "/Content/images/nopic.gif"
                        }
                    }
                }
                return imgSrc;
            },
            checkboxActiveFun: function (Value, data) {
                var ValueArray = Value;
                if (!Array.isArray(ValueArray)) {
                    ValueArray = Value.split(',');
                }

                var isHas = false;
                ValueArray.forEach(function (item, index) {
                    if (item == data) {
                        isHas = true;
                    }

                })
                return isHas;
            }


        });

    </script>

}

