@{
    ViewBag.Title = "供货管理";
    ViewBag.MenuId = "DistributionLog";
    ViewBag.MenuActive = "ProductManagement";
}

@section Header {
    <link rel="stylesheet" href="~/Content/css/product/SupplyManagement.css" />
}

<div class="wu-wrape wu-flex wu-column" style="height: 100vh;">
    <div class="wu-Breadcrumb">
        <div class="wu-Breadcrumb-left wu-f20 wu-flex" style="align-items: flex-end;">
            <span class="wu-weight600">供货管理</span>
            <i class="wu-f14 wu-c06 wu-mL8">管理您提供给分销商的所有货源</i>
        </div>
        <div class="wu-flex wu-yCenter" style="gap: 12px;">
            <span class="wu-btn wu-btn-mid wu-primary" onclick="gotoBaseProductPage()">从基础商品导入</span>
            <span class="wu-btn wu-btn-mid wu-primary" onclick="gotoShopProductPage()">从商品列表导入</span>
            <span class="wu-btn wu-btn-mid wu-icon-left" onclick="createSupplyProduct()">
                <i class="iconfont wu-btn-icon icon-a-magic1x"></i>
                创建货源商品
            </span>
        </div>
    </div>
    <div class="wu-flex-1 wu-flex wu-column">
        <div class="wu-flex-1 wu-mL16 wu-mR16">
            <div class="wu-8radius" style="background-color: #fff;">
                <ul class="wu-tabNav" id="product_listing_status_tabs">
                    <li class="wu-tabNav-li wu-active" data-type="0">
                        <span class="wu-tabNav-li-span">全部(<i>1024</i>)</span>
                    </li>
                    <li class="wu-tabNav-li" data-type="1">
                        <span class="wu-tabNav-li-span">上架中</span>
                    </li>
                    <li class="wu-tabNav-li" data-type="2">
                        <span class="wu-tabNav-li-span">已下架</span>
                    </li>
                </ul>
                <div class="wu-flex wu-p12" style="gap: 8px;">
                    <div class="wu-btn wu-btn-mid wu-primary wu-icon-right">
                        上新时间
                        <span class="wu-flex wu-column ascAndDescIconWrap">
                            <i class="iconfont icon-xiangxia" data-type="timeAsc" onclick="releaseTimeAscAndDesc.bind(this)()"></i>
                            <i class="iconfont icon-xiangxia" data-type="timeDesc" onclick="releaseTimeAscAndDesc.bind(this)()"></i>
                        </span>
                    </div>
                    <div class="wu-btn wu-btn-mid wu-primary wu-icon-right">
                        供货价
                        <span class="wu-flex wu-column ascAndDescIconWrap">
                            <i class="iconfont icon-xiangxia" data-type="priceAsc" onclick="supplyPriceAscAndDesc.bind(this)()"></i>
                            <i class="iconfont icon-xiangxia" data-type="priceDesc" onclick="supplyPriceAscAndDesc.bind(this)()"></i>
                        </span>
                    </div>
                    <div class="wu-inputWrap wu-form-mid wu-formCombine" style="width: 200px;">
                        <div class="wu-inputWrap-content">
                            <div class="wu-formCombine-left">
                                <input class="wu-input" type="text" placeholder="请输入商品名称" />
                            </div>
                            <i class="iconfont icon-a-search1x wu-pR8 wu-hover" onclick="searchData()"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wu-flex-1">
                <ul>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                    <li>111111</li>
                </ul>
            </div>
        </div>
        <!-- 底部操作栏 -->
        <div class="compageWrap-module wu-pL16 wu-pR16 wu-radius-BL-8 wu-radius-BR-8">
            <div class="compageWrap-wrap">
                <div class="wu-checkboxWrap">
                    <div class="wu-my-checkboxWrap wu-my-allCheck" onclick="">
                        <span class="wu-my-checkbox"></span>
                    </div>
                    <span class="c09">单页全选</span>
                    <span class="c09 pL8">已选<i class="wu-my-checkboxNum">0</i>条</span>
                    <span class="wu-btn wu-btn-mid wu-primary wu-mL24">批量上架</span>
                    <span class="wu-btn wu-btn-mid wu-primary wu-mL8">批量下架</span>
                </div>
                <div class="layui-myPage" id="supply_table_paging"></div>
            </div>
            <div class="art-sticky-scroll wu-scrollbar">
                <div class="art-sticky-scroll-item" style="width: 844px;"></div>
            </div>
        </div>
    </div>
</div>

<script id="table_body_data" type="text/x-jsrender">
    {{if data.length>0}}
    {{for data}}
    <li onclick="">
        <div>

        </div>
    </li>
    {{/for}}
    {{else}}
    <li>
        <span class="tableNoDataShow-title">暂无数据！</span>
    </li>
    {{/if}}
</script>
@section scripts {
<script>
     // 表单数据
    var formData = {
         Tag: 5,
         //排序字段 上新时间PublicTime 供货价 发货量ShipmentsCount 退货率 RefundRate ,
         OrderByField: 'PublicTime',
         IsOrderDesc: true,   //是否排序
         ProductName: '',  // 商品名称
         isPublic: '',
         PageIndex:1,
		 PageSize:50,
    }

    function gotoBaseProductPage() {

    }
    function gotoShopProductPage() {

    }
    function createSupplyProduct() {

    }
    function releaseTimeAscAndDesc() {

    }
    function supplyPriceAscAndDesc() {

    }
    function searchData() {
        console.log('查询')
        LoadList();

    }
    function LoadList() {
        commonModule.Ajax({
            url: '/api/SupplierProduct/GetList',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            loading: true,
            loadingMessage: "查询中...",
            success: function (rsp) {

            }
        });
    }

    $(function () {
        wuFormModule.navActive("#product_listing_status_tabs", function (index, item) {
            console.log("index", index)
            console.log("item", item)
        }, 'wu-active');
        LoadList();

        var list = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {},];
        var tplt = $.templates("#table_body_data")
        var html = tplt.render({
            data: list,
        });
        $("#wu-tbody-list").html(html);
        layui.laypage.render({
            elem: 'supply_table_paging',
            theme: ' wu-page',
            count: 50,
            limit: 20,
            curr: 1,
            limits: [50, 100, 200, 300, 400, 500],
            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
            jump: function (obj, first) {
                if (!first) {
                    searchParamObj.PageIndex = obj.curr;
                    LoadList();
                }
            }
        });
    });
</script>
}
