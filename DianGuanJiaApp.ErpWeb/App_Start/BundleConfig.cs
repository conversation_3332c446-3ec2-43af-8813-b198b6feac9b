using DianGuanJiaApp.Utility;
using System;
using System.Web;
using System.Web.Optimization;

namespace DianGuanJiaApp.ErpWeb
{
    public class BundleConfig
    {
        // 有关捆绑的详细信息，请访问 https://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            #region JS Bundle
            bundles.Add(new ScriptBundle("~/bundles/common").Include(
                        "~/Scripts/jquery-1.12.4.min.js",
                        "~/Scripts/jquery.cookie.js",
                        "~/Scripts/newCalender/js/newcalenderTime.js",
                        "~/Scripts/jsrender/jsrender.min.js",
                        "~/Scripts/noviceIntro/noviceIntro.js",
                        "~/Scripts/layout.js",
                        "~/Scripts/animate.js",
                        "~/Scripts/countdownTimer/countdownTimer.js",
                        "~/Scripts/categoryMeu/wu_categoryMeu.js"

            ));
            bundles.Add(new ScriptBundle("~/bundles/layout").Include(
                        //"~/Scripts/layui/layui.all.js",
                        "~/Scripts/select2/select2.js",
                        "~/Scripts/initMyKu.js",
                        "~/Scripts/aliyunCaptchaCommonFn.js",
                        "~/Scripts/CommonModule.js",
                        "~/Scripts/comViewBag.js",
                        "~/Scripts/allPlatformFun.js",
                        "~/Scripts/selectbox/selectbox2.js",
                        "~/Scripts/selectbox/cascadeSelectbox.js",
                        "~/Scripts/selectbox/selectboxRemarksFlag.js",
                        "~/Scripts/selectbox/selectboxLeaveMessageFlag.js",
                         "~/Scripts/qrcode.min.js",
                        "~/Scripts/my-moreSelect/my-moreSelect.js",
                        "~/Scripts/wuDesign/wuFormModule.js",
                        "~/Scripts/wuDesign/wuTemplateModule.js",
                        "~/Scripts/guidelayer/guideSteplayer.js",
                        "~/Scripts/guidelayer/guideDailogLayer.js"
                        ));
            //bundles.Add(new ScriptBundle("~/bundles/layuijs").Include(
            //            "~/Scripts/layui/layui.all.js"
            //            ));
            bundles.Add(new ScriptBundle("~/bundles/partner").Include(
                         "~/Scripts/Partner/PartnerModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/crossborder").Include(
                         "~/Scripts/Partner/CrossBorderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/pddshop").Include(
                         "~/Scripts/Partner/PddShopModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/mysupplier").Include(
                        "~/Scripts/Partner/MySupplierModule.js",
                        "~/Scripts/qrcode.min.js"
           ));
            bundles.Add(new ScriptBundle("~/bundles/myagent").Include(
                         "~/Scripts/Partner/MyAgentModule.js",
                         "~/Scripts/qrcode.min.js"
             ));
            bundles.Add(new ScriptBundle("~/bundles/allorder").Include(
                        "~/Scripts/ajaxfileupload.js",
                        "~/Scripts/Order/ErpOrderTableBuilder.js",
                        "~/Scripts/Order/ExportOrderModule.js",
                        "~/Scripts/progress/ProgressModule.js",
                        "~/Scripts/Order/CustomerValidModule.js",
                        "~/Scripts/upPicFileModule.js",
                        "~/Scripts/Order/OrderListModule.js" 
            ));

            var waitOrder = new ScriptBundle("~/bundles/waitorder").Include(
                        //"~/Scripts/daterangepicker/calenderTime.js",
                        "~/Scripts/Order/ErpOrderTableBuilder.js",
                        "~/Scripts/Order/WaitOrderModule.js",
                        "~/Scripts/upPicFileModule.js",
                        "~/Scripts/Order/OrderListModule.js",
                        "~/Scripts/TemplateSet/TemplateSetCommonModule.js",
                        "~/Scripts/TemplateSet/AddTemplateModule.js",
                        "~/Scripts/orderlist/AddTemplateInOrderListModule.js",
                        "~/Scripts/orderlist/KeywordFilterRepalceModule.js",
                        "~/Scripts/orderlist/PrintContentFormatSetModule.js",
                        "~/Scripts/AddressSpliter.js",
                        "~/Scripts/CaiNiaoPrinter.js",
                        "~/Scripts/PinduoduoPrinter.js",
                        "~/Scripts/TouTiaoPrinter.js",
                        "~/Scripts/KuaiShouPrinter.js",
                        "~/Scripts/JingDongPrinter.js",
                        "~/Scripts/XiaoHongShuPrinter.js",
                        "~/Scripts/NewXiaoHongShuPrinter.js",
                        "~/Scripts/WxVideoPrinter.js",
                        "~/Scripts/LodopPrinter.js",
                        "~/Scripts/SCPPrint.js",
                        "~/Scripts/FengqiaoPrinter.js",
                        "~/Scripts/ExpressPrinter.js",
                        "~/Scripts/SendLogistic.js",
                        "~/Scripts/SendGoodTemplate.js",
                        "~/Scripts/Order/ExportOrderModule.js",
                        "~/Scripts/progress/ProgressModule.js",
                        "~/Scripts/Order/CustomerValidModule.js",
                        "~/Scripts/WaybillCodeList/WaybillCodeListModule.js",
                        "~/Scripts/SetInfo/ReachCompareModule.js",
                        "~/Scripts/Sortable.min.js",
                        "~/Scripts/Order/ScanPrint.js"

            );
            if (CustomerConfig.IsCrossBorderSite)
            {
                waitOrder.Include("~/Scripts/Order/TkOrderPrintModule.js");
            }
            bundles.Add(waitOrder);

            bundles.Add(new ScriptBundle("~/bundles/sendorder").Include(
                       "~/Scripts/Order/ErpOrderTableBuilder.js",
                       "~/Scripts/Order/ExportOrderModule.js",
                       "~/Scripts/progress/ProgressModule.js",
                       "~/Scripts/Order/CustomerValidModule.js",
                       "~/Scripts/SendOrder/SendOrderModule.js"
           ));
            bundles.Add(new ScriptBundle("~/bundles/waybillcodelist").Include(
                "~/Scripts/LodopPrinter.js",
                "~/Scripts/CaiNiaoPrinter.js",
                "~/Scripts/PinduoduoPrinter.js",
                "~/Scripts/TouTiaoPrinter.js",
                "~/Scripts/KuaiShouPrinter.js",
                "~/Scripts/JingDongPrinter.js",
                "~/Scripts/XiaoHongShuPrinter.js",
                "~/Scripts/NewXiaoHongShuPrinter.js",
                "~/Scripts/WxVideoPrinter.js",
                "~/Scripts/SCPPrint.js",
                "~/Scripts/FengqiaoPrinter.js",
                "~/Scripts/ExpressPrinter.js",
                "~/Scripts/WaybillCodeList/WaybillCodeListModule.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/proudctlist").Include(
                "~/Scripts/Proudctlist/ProudctlistModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/sRegister").Include(
                "~/Scripts/Partner/sRegisterCommonModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/product_recycle").Include(
                "~/Scripts/Proudctlist/RecycleProductModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/product_basics").Include(
                "~/Scripts/layui/layui.all.js",
                "~/Scripts/Proudctlist/ProductBasicsModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/BaseOfPtSkuRelationDetail").Include(
                "~/Scripts/Proudctlist/BaseOfPtSkuRelationDetail.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/printhistory").Include(
                "~/Scripts/LodopPrinter.js",
                "~/Scripts/CaiNiaoPrinter.js",
                "~/Scripts/PinduoduoPrinter.js",
                "~/Scripts/TouTiaoPrinter.js",
                "~/Scripts/KuaiShouPrinter.js",
                "~/Scripts/JingDongPrinter.js",
                "~/Scripts/XiaoHongShuPrinter.js",
                "~/Scripts/NewXiaoHongShuPrinter.js",
                "~/Scripts/WxVideoPrinter.js",
                "~/Scripts/SCPPrint.js",
                "~/Scripts/FengqiaoPrinter.js",
                "~/Scripts/ExpressPrinter.js",
                "~/Scripts/WaybillCodeList/WaybillCodeListModule.js",
                "~/Scripts/PrintHistory/PrintHistoryModule.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/printhistorysecond").Include(
                "~/Scripts/PrintHistory/PrintHistorySecondModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/systemSet").Include(
                "~/Scripts/systemSet/BasicsetModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/distributeSet").Include(
                "~/Scripts/systemSet/DistributeSetModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/bindWxUser").Include(
             "~/Scripts/systemSet/BindWxUser.js"
         ));
            bundles.Add(new ScriptBundle("~/bundles/sendhistory").Include(
                "~/Scripts/SendHistory/SendHistoryModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/sellerinfo").Include(
                "~/Scripts/AddressSpliter.js",
                "~/Scripts/SellerInfoSet/SellerInfoManagerModule.js",
                "~/Scripts/ajaxfileupload.js",
                "~/Scripts/orderlist/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/sharewaybillaccount").Include(
                "~/Scripts/ajaxfileupload.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/orderlist/CustomerValidModule.js",
                "~/Scripts/selectbox/selectbox2.js",
                "~/Scripts/ShareWayBillAccount/shareWayBillAccountModule.js",
                "~/Scripts/progress/ProgressModule.js",
               "~/Scripts/daterangepicker/calenderTime.js"

            ));
            bundles.Add(new ScriptBundle("~/bundles/sharewaybillaccountcheck").Include(
                "~/Scripts/ajaxfileupload.js",
                "~/Scripts/orderlist/ExportOrderModule.js",
                "~/Scripts/SharedWaybillAccountCheck/sharedWaybillAccountCheckModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/offineorder").Include(
                "~/Scripts/AddressSpliter.js",
                "~/Scripts/ajaxfileupload.js",
                "~/Scripts/Order/OfflineOrderModule.js",
                "~/Scripts/Proudctlist/ProudctlistModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/offlineProduct").Include(
                "~/Scripts/Proudctlist/OfflineProductModule.js"
                ));
            bundles.Add(new ScriptBundle("~/bundles/sendgoodtemplate").Include(
               "~/Scripts/jquery.slimscroll.min.js",
               "~/Scripts/artDialog.js",
               "~/Scripts/sendTemplate/dialog.js",
               "~/Scripts/sendTemplate/setSendList.js",
               "~/Scripts/LodopPrinter.js",
               "~/Scripts/sendTemplate/jquery.slimscroll.min.js"
               ));
            bundles.Add(new ScriptBundle("~/bundles/sendgoodtemplateedit").Include(
               "~/Scripts/jquery.slimscroll.min.js",
               "~/Scripts/artDialog.js",
               "~/Scripts/qrcode.js",
               "~/Scripts/sendTemplate/setSendTemplate.js",
               "~/Scripts/sendTemplate/setSendTemplate2.js",
               "~/Scripts/sendTemplate/dialog.js",
               "~/Scripts/sendTemplate/Uploader.js",
               "~/Scripts/sendTemplate/json2.js",
               "~/Scripts/sendTemplate/qrcode.min.js"

               ));
            bundles.Add(new ScriptBundle("~/bundles/expressreach").Include(
                "~/Scripts/ajaxfileupload.js",
                "~/Scripts/SetInfo/ExpressReachModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/purchase").Include(
                 "~/Scripts/LodopPrinter.js",
                 "~/Scripts/Order/ExportOrderModule.js",
                 "~/Scripts/Order/CustomerValidModule.js",

               "~/Scripts/Purchase/PurchaseModule.js",
                 //"~/Scripts/Purchase/NahuoLabelModule.js",
                 "~/Scripts/Purchase/newpurchases.js",
                 "~/Scripts/Purchase/purchasesSet.js",
                 "~/Scripts/progress/ProgressModule.js"
               ));

            bundles.Add(new ScriptBundle("~/bundles/stockdetail").Include(
                "~/Scripts/StockControl/StockDetailModule.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/changedetail").Include(
                "~/Scripts/StockControl/ChangeDetailModule.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/storemanagement").Include(
                "~/Scripts/StockControl/StoreManagementModule.js"
                ));
            bundles.Add(new ScriptBundle("~/bundles/warehouseproduct").Include(
                "~/Scripts/StockControl/WareHouseProductModule.js",
                "~/Scripts/StockControl/CombinationProductModule.js"
                ));
            bundles.Add(new ScriptBundle("~/bundles/addproduct").Include(
                "~/Scripts/StockControl/AddProductModule.js"
                ));
            bundles.Add(new ScriptBundle("~/bundles/addproductbyshop").Include(
              "~/Scripts/StockControl/AddProductByShopModule.js"
              ));
            bundles.Add(new ScriptBundle("~/bundles/editproduct").Include(
              "~/Scripts/StockControl/EditProductModule.js"
              ));
            bundles.Add(new ScriptBundle("~/bundles/skubind").Include(
                "~/Scripts/jquery-1.12.4.min.js",
                "~/Scripts/jquery.cookie.js",
                "~/Scripts/newCalender/js/newcalenderTime.js",
                "~/Scripts/jsrender/jsrender.min.js",
                "~/Scripts/noviceIntro/noviceIntro.js",
                 "~/Scripts/layui/layui.all.js",
                 "~/Scripts/CommonModule.js"
                ));
            bundles.Add(new ScriptBundle("~/bundles/skuunbind").Include(
                "~/Scripts/jquery-1.12.4.min.js",
                "~/Scripts/jquery.cookie.js",
                "~/Scripts/newCalender/js/newcalenderTime.js",
                "~/Scripts/jsrender/jsrender.min.js",
                "~/Scripts/noviceIntro/noviceIntro.js",
                "~/Scripts/layui/layui.all.js",
                "~/Scripts/CommonModule.js"
                ));

            bundles.Add(new ScriptBundle("~/bundles/pricesetting_supplier").Include(
                "~/Scripts/FinancialSettlement/PriceSettingCommon.js",
                "~/Scripts/FinancialSettlement/PriceSettingSupplier.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/pricesetting_agent").Include(
                "~/Scripts/FinancialSettlement/PriceSettingCommon.js",
                "~/Scripts/FinancialSettlement/PriceSettingAgent.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/pricesetting_shops").Include(
                "~/Scripts/FinancialSettlement/MyShops.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/pricesettingclound").Include(
                 "~/Scripts/ajaxfileupload.js",
                "~/Scripts/FinancialSettlement/PriceSettingClound.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/billManagement").Include(
                "~/Scripts/FinancialSettlement/BillManagement.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/billCenter").Include(
                "~/Scripts/FinancialSettlement/BillCenter.js",
                "~/Scripts/Order/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/outaccountproduct").Include(
                "~/Scripts/FinancialSettlement/OutAccountProduct.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/skuRecycleSupplier").Include(
                "~/Scripts/FinancialSettlement/SkuRecycleSupplier.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/skuRecycleAgent").Include(
                "~/Scripts/FinancialSettlement/SkuRecycleAgent.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/generalizeindex").Include(
                "~/Scripts/Partner/PartnerModule.js",
                "~/Scripts/Partner/CrossBorderModule.js",
                "~/Scripts/GeneralizeIndex/GeneralizeIndexModule.js"

            ));
            bundles.Add(new ScriptBundle("~/bundles/expressbill").Include(
                "~/Scripts/ExpressBill/WaybillCodeCheckModule.js",
                "~/Scripts/orderlist/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/aftersale").Include(
                "~/Scripts/AfterSale/AfterSaleModule.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/manualaftersale").Include(
                "~/Scripts/AfterSale/ManualAfterSale.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/sendfail").Include(
                "~/Scripts/WaybillCodeList/WaybillCodeListModule.js",
                "~/Scripts/Order/SendFailModule.js",
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/orderfail").Include(
                "~/Scripts/Order/ErpOrderTableBuilder.js",
                "~/Scripts/Order/WaitOrderModule.js",
                "~/Scripts/Order/OrderFailModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/abnormalorder").Include(
                "~/Scripts/Order/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/supplySet1688skubind").Include(
                "~/Scripts/jquery-1.12.4.min.js",
                "~/Scripts/jquery.cookie.js",
                "~/Scripts/newCalender/js/newcalenderTime.js",
                "~/Scripts/jsrender/jsrender.min.js",
                "~/Scripts/noviceIntro/noviceIntro.js",
                 "~/Scripts/layui/layui.all.js",
                 "~/Scripts/CommonModule.js",
                "~/Scripts/supplySet1688/SkuBindModule.js"
                ));
            bundles.Add(new ScriptBundle("~/bundles/supplySet1688skuunbind").Include(
                "~/Scripts/jquery-1.12.4.min.js",
                "~/Scripts/jquery.cookie.js",
                "~/Scripts/newCalender/js/newcalenderTime.js",
                "~/Scripts/jsrender/jsrender.min.js",
                "~/Scripts/noviceIntro/noviceIntro.js",
                 "~/Scripts/layui/layui.all.js",
                 "~/Scripts/CommonModule.js",
                "~/Scripts/supplySet1688/SkuUnBindModule.js"
                ));

            bundles.Add(new ScriptBundle("~/bundles/sendhistoryreturnrecord").Include(
                "~/Scripts/Order/ExportOrderModule.js",
                "~/Scripts/progress/ProgressModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/fundsmanagement").Include(
                 "~/Scripts/Order/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/paymentfailurelist").Include(
                 //"~/Scripts/Order/OrderPayModule.js",
                 "~/Scripts/Order/ErpOrderTableBuilder.js",
                 "~/Scripts/Order/OrderListModule.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/ShippingFeeSet").Include(
                "~/Scripts/supplySet1688/ShippingFeeSetModule.js"
            ));
            // 收单设置
            bundles.Add(new ScriptBundle("~/bundles/SupplierSetBy1688").Include(
               "~/Scripts/Sortable.min.js",
               "~/Scripts/upPicFileModule.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/scanprint").Include(
                        "~/Scripts/Order/ErpOrderTableBuilder.js",
                        "~/Scripts/Order/WaitOrderModule.js",
                        //"~/Scripts/daterangepicker/calenderTime.js",
                        "~/Scripts/TemplateSet/TemplateSetCommonModule.js",
                        "~/Scripts/TemplateSet/AddTemplateModule.js",
                        "~/Scripts/orderlist/AddTemplateInOrderListModule.js",
                        "~/Scripts/orderlist/KeywordFilterRepalceModule.js",
                        "~/Scripts/orderlist/PrintContentFormatSetModule.js",
                        "~/Scripts/CaiNiaoPrinter.js",
                        "~/Scripts/PinduoduoPrinter.js",
                        "~/Scripts/TouTiaoPrinter.js",
                        "~/Scripts/KuaiShouPrinter.js",
                        "~/Scripts/JingDongPrinter.js",
                        "~/Scripts/XiaoHongShuPrinter.js",
                        "~/Scripts/NewXiaoHongShuPrinter.js",
                        "~/Scripts/WxVideoPrinter.js",
                        "~/Scripts/LodopPrinter.js",
                        "~/Scripts/SCPPrint.js",
                        "~/Scripts/FengqiaoPrinter.js",
                        "~/Scripts/ExpressPrinter.js",
                        "~/Scripts/SendLogistic.js",
                        "~/Scripts/Order/ScanPrint.js",
                        "~/Scripts/selectbox/funSelectbox.js"

            ));
            bundles.Add(new ScriptBundle("~/bundles/OrderLifeCycleTool").Include(
                "~/Scripts/Order/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/CreateBaseProduct").Include(
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/CreateBaseProduct.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/exportExcelIndex").Include(
                "~/Scripts/layui/layui.all.js",
                "~/Scripts/exportExcelIndex/exportExcelIndex.js",
                "~/Scripts/orderlist/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/rightContentRender").Include(
                "~/Scripts/layui/layui.all.js",
                "~/Scripts/exportExcelIndex/rightContentRender.js",
                "~/Scripts/orderlist/ExportOrderModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/quickOutAccount").Include(
                "~/Scripts/FinancialSettlement/quickOutAccount"
            ));
			bundles.Add(new ScriptBundle("~/bundles/LoadOpenTelemetry").Include(
				"~/Scripts/LoadOpenTelemetry.js"
			));
            bundles.Add(new ScriptBundle("~/bundles/ArmsTool").Include(
                "~/Scripts/arms-tool.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/CreateBaseProduct").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/CreateBaseProduct.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/BusinessCardCreateProduct").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/BusinessCardCreateProduct.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/CreateBasePlatformProduct").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/CreateBasePlatformProduct.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/CreateBasePlatformProductPdd").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/CreateBasePlatformProductPdd.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/CreateBasePlatformProductKs").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/CreateBasePlatformProductKs.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/CreateBasePlatformProductDyRetails").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/CreateBasePlatformProductDyRetail.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/PrepareDistribution").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/Proudctlist/DistributionParth/DistributionCommon.js",
                "~/Scripts/Proudctlist/DistributionParth/SelectShop_parth.js",
                "~/Scripts/Proudctlist/DistributionParth/BaseSet_parth.js",
                "~/Scripts/Proudctlist/DistributionParth/PrepareList_parth.js"
            ));
            // 小站名片js
            bundles.Add(new ScriptBundle("~/bundles/MyStationCard").Include(
                "~/Scripts/GeneralizeIndex/MyStationCard.js",
                "~/Scripts/upPicFileModule.js"
            ));
            // 新应用首页js
            bundles.Add(new ScriptBundle("~/bundles/ApplicationIndexModule").Include(
               "~/Scripts/Partner/PartnerModule.js",
               "~/Scripts/GeneralizeIndex/ApplicationIndexModule.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/FreightTemplate/NewShippingFeeSet").Include(
               "~/Scripts/FreightTemplate/ShippingFeeSet1688.js",
               "~/Scripts/FreightTemplate/ShippingFeeTemplateSet.js",
               "~/Scripts/FreightTemplate/AddressTemplateAddOrEdmit.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/TkPrintProcessStateRecord").Include(
               "~/Scripts/TkPrintProcessState/TkPrintProcessStateModule.js"
            ));
            // 商品采集编辑
            bundles.Add(new ScriptBundle("~/bundles/EditCollectProduct").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/collectbox/EditCollectProduct.js"
            ));
            // 全球商品编辑
            bundles.Add(new ScriptBundle("~/bundles/EditGlobalProduct").Include(
                "~/Scripts/Sortable.min.js",
                "~/Scripts/upPicFileModule.js",
                "~/Scripts/collectbox/EditGlobalProduct.js"
            ));
			#endregion

			#region CSS Bundle
			bundles.Add(new StyleBundle("~/css/layout").Include(
                        //"~/Scripts/laypage/laypage.css",
                        "~/Scripts/layui/css/layui.css",
                        "~/Content/css/common.css",
                        "~/Content/css/skinCommon.css",
                        "~/Content/css/wuDesign/wu-skin.css",
                        "~/Content/css/wuDesign/wu-skin2.css",
                        "~/Content/css/wuDesign/wu-skin3.css",
                        "~/Content/css/wuDesign/wu-platform.css",
                        "~/Content/css/wuDesign/wu-skin-ls.css",
                        "~/Content/css/fonts/iconfont.css",
                        "~/Scripts/select2/select2.css",
                        "~/Scripts/newCalender/css/newcalenderTime.css",
                        "~/Content/css/selectbox2.css",
                        "~/Content/css/selectboxRemarksFlag.css",
                        "~/Scripts/noviceIntro/noviceIntro.css",
                        "~/Scripts/my-moreSelect/commonSearch.css",
                        "~/Content/css/searchOrderWrap.css",
                        "~/Content/css/editPswdLayout.css",
                        "~/Scripts/guidelayer/guidelayer.css",
                        "~/Content/css/newLayout.css",
                        "~/Content/css/concatLayout.css",
                        "~/Content/css/editPswdLayout.css",
                        "~/Content/css/abnormalProduct/abnormal-product-content.css"
            ));
            bundles.Add(new StyleBundle("~/css/orderlist").Include(
                        "~/Content/css/orderlist/ordercommon.css",
                        "~/Content/css/orderlist/orderList.css"
            ));
            bundles.Add(new StyleBundle("~/css/waitorder").Include(
                        "~/Content/css/orderlist/ordercommon.css",
                        "~/Content/css/orderlist/orderList.css",
                        "~/Content/css/orderlist/waitorder.css"
            ));
            bundles.Add(new StyleBundle("~/css/allOrder").Include(
                        "~/Content/css/orderlist/allOrder.css"
            ));
            bundles.Add(new StyleBundle("~/css/sendgoodtemplate").Include(
                        "~/Content/css/SendGoodTemplate/SendGoodTemplate.css"
            ));
            bundles.Add(new StyleBundle("~/css/setSendTemplate").Include(
                        "~/Content/css/SendGoodTemplate/setSendTemplate.css"
            ));

            bundles.Add(new StyleBundle("~/css/puchase").Include(
                        "~/Content/css/purchases/purchases.css"
                        , "~/Scripts/layer/theme/default/layer.css"
                        , "~/Scripts/layer/skin/of/style.css"
                        ));
            bundles.Add(new StyleBundle("~/css/skubind").Include(
                        //"~/Scripts/layui/css/layui.css",
                        //"~/Content/css/common.css",
                        //"~/Content/css/fonts/iconfont.css",
                        "~/Content/css/StockControl/StockControl.css"
                        ));
            bundles.Add(new StyleBundle("~/css/skuunbind").Include(
                //"~/Scripts/layui/css/layui.css",
                //"~/Content/css/common.css",
                //"~/Content/css/fonts/iconfont.css",
                "~/Content/css/StockControl/StockControl.css"
            ));
            bundles.Add(new StyleBundle("~/css/aftersale").Include(
                        "~/Content/css/orderlist/allOrder.css",
                        "~/Content/css/aftersale/aftersale.css"
            ));
            bundles.Add(new StyleBundle("~/css/sendfail").Include(
                        "~/Content/css/orderlist/ordercommon.css",
                        "~/Content/css/orderlist/orderList.css",
                        "~/Content/css/orderlist/sendfail.css"
            ));
            bundles.Add(new StyleBundle("~/css/warehouseproduct").Include(
                        "~/Content/css/StockControl/WarehouseProduct.css"
                        ));
            bundles.Add(new StyleBundle("~/css/generalizeIndex").Include(
            "~/Content/css/generalizeIndex/generalizeIndex.css"
            ));
            bundles.Add(new StyleBundle("~/css/BusinessCardCreateProduct").Include(
                "~/Scripts/categoryMeu/wu_categoryMeu.css",
                "~/Content/css/product/BusinessCardCreateProduct.css"
            ));
            bundles.Add(new StyleBundle("~/css/CreateBasePlatformProduct").Include(
                "~/Scripts/categoryMeu/wu_categoryMeu.css",
                "~/Content/css/product/CreateBasePlatformProduct.css"
            ));
            bundles.Add(new StyleBundle("~/css/PrepareDistribution").Include(
                "~/Scripts/categoryMeu/wu_categoryMeu.css",
                "~/Content/css/product/PrepareDistribution.css",
                "~/Content/css/DistributionProduct/DistributionParth/selectShop_parth.css",
                "~/Content/css/DistributionProduct/DistributionParth/BaseSet_parth.css",
                "~/Content/css/DistributionProduct/DistributionParth/PrepareList_parth.css",
                "~/Content/css/DistributionProduct/DistributionParth/Edmit_parth.css"
            ));
            bundles.Add(new StyleBundle("~/css/orderLifeCycleTool").Include(
                "~/Scripts/layui/css/layui.css"
            ));
            bundles.Add(new StyleBundle("~/css/exportExcelIndex").Include(
            "~/Content/css/exportExcelIndex/exportExcelIndex.css"
            ));
            bundles.Add(new StyleBundle("~/css/createBaseProduct").Include(
            "~/Content/css/product/createBaseProduct.css"
            ));

            bundles.Add(new StyleBundle("~/css/BaseOfPtProducts").Include(
            "~/Content/css/product/BaseOfPtProducts.css"
            ));
            // 小站名片样式
            bundles.Add(new StyleBundle("~/css/MyStationCard").Include(
                "~/Scripts/categoryMeu/wu_categoryMeu.css",
                "~/Content/css/generalizeIndex/MyStationCard.css"
            ));
            // 新应用首页样式
            bundles.Add(new StyleBundle("~/css/ApplicationIndex").Include(
               "~/Content/css/generalizeIndex/ApplicationIndex.css"
            ));
            bundles.Add(new StyleBundle("~/css/NewShippingFeeSet").Include(
               "~/Content/css/FreightTemplate/NewShippingFeeSet.css"
            ));
            bundles.Add(new StyleBundle("~/css/SupplierSetBy1688").Include(
               "~/Content/css/FreightTemplate/SupplierSetBy1688.css"
             ));
            // 商品采集编辑
            bundles.Add(new StyleBundle("~/css/EditCollectProduct").Include(
            //    "~/Content/css/product/createBaseProduct.css",
                "~/Scripts/categoryMeu/wu_categoryMeu.css",
                "~/Content/css/product/CreateBasePlatformProduct.css"
            ));
            bundles.Add(new StyleBundle("~/css/distributeSet").Include(
                "~/Content/css/distributionSet/distributionSet.css",
                "~/Content/css/product/ShopBindSupplier.css"
            ));
            bundles.Add(new StyleBundle("~/css/SelectionDistribution").Include(
                "~/Content/css/DistributionProduct/SelectionDistribution.css"
            ));
            bundles.Add(new StyleBundle("~/css/proudctlist").Include(
                "~/Content/css/product/product-index.css",
                "~/Content/css/product/ShopBindSupplier.css"
            ));
            bundles.Add(new StyleBundle("~/css/productBasics").Include(
                "~/Content/css/product/NewproductBasics.css"
            ));
            bundles.Add(new StyleBundle("~/css/DistributionDetails").Include(
                "~/Content/css/DistributionProduct/DistributionDetails.css"
            ));
            

            #endregion
        }
    }
}
