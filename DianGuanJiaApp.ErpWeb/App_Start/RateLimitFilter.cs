using DianGuanJiaApp.Utility.Other;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.ErpWeb
{
    /// <summary>
    /// 
    /// </summary>
    [AttributeUsage(AttributeTargets.Method)]
    public class RateLimitAttribute : ActionFilterAttribute
    {
        private readonly int _limit;
        private readonly TimeSpan _period;
        private readonly bool _isCheck;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="limit"></param>
        /// <param name="minutes"></param>
        public RateLimitAttribute(int limit = 30, int minutes = 1)
        {
            _limit = limit;
            _period = TimeSpan.FromMinutes(minutes);
        }

        /// <summary>
        /// 验证码解除限制
        /// </summary>
        /// <param name="isCheck"></param>
        public RateLimitAttribute(bool isCheck)
        {
            _isCheck = isCheck;
        }

        /// <summary>
        /// 
        /// </summary>
        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            var ip = string.Empty;
            // 获取ip
            if (actionContext.Request.Properties.TryGetValue("MS_HttpContext", out var contextObj))
            {
                ip= ((System.Web.HttpContextWrapper)contextObj)?.Request.UserHostAddress;
            }

            if (string.IsNullOrEmpty(ip))
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.BadRequest,
                    new { Message = "无法识别客户端地址" }
                );
                return;
            }

            // Redis 键格式：rate_limit:{IP}:{方法名}
            var cacheKey = $"rate_limit:{ip}:{actionContext.ActionDescriptor.ActionName}";

            // 是否检查验证码，访问频次过高时要求检查二维码
            if (!_isCheck)
            {
                // 使用 RedisHelper 计数并设置过期时间
                var currentCount = RedisHelper.IncrBy(cacheKey);
                if (currentCount == 1) // 第一次访问时设置过期时间
                {
                    RedisHelper.Expire(cacheKey, _period);
                }

                if (currentCount > _limit)
                {
                    actionContext.Response = actionContext.Request.CreateResponse((HttpStatusCode)429,
                        new { Message = "请求过于频繁，请稍后再试" }
                    );
                    return;
                }
            }
            else
            {
                var code = actionContext.Request.GetQueryNameValuePairs().FirstOrDefault(kv => kv.Key == "code").Value.ToString2();
                if (!VerifyCode.CheckVerifyCode(code))
                {
                    actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Forbidden,
                        new { Message = "二维码输入错误，请重试" }
                    );
                    return;
                }
                RedisHelper.Set(cacheKey, 0);
            }


            base.OnActionExecuting(actionContext);
        }
    }
}