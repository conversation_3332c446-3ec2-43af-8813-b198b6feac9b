using System;
using System.Net;
using System.Net.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.ErpWeb
{
    /// <summary>
    /// 控制器锁定过滤器
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class ControllerLockAttribute : ActionFilterAttribute
    {
        private string _lockKey ;

        /// <summary>
        /// 
        /// </summary>

        public ControllerLockAttribute(string lockKey = null)
        {
            _lockKey = lockKey;
        }

        /// <summary>
        /// 
        /// </summary>
        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            if (_lockKey.IsNullOrEmpty())
            {
                _lockKey = $"DianGuanJiaApp:{actionContext.ControllerContext.ControllerDescriptor.ControllerName}:LockVersions";
            }

            var currentVersion = CustomerConfig.SystemEnvironmentVersion; //string

            var useCache = !CustomerConfig.IsLocalDbDebug;
            var lockList = new CommonSettingService().GetString(_lockKey,0, useCache)?.SplitToList(",");
            if (lockList?.Contains(currentVersion) ?? false)
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Forbidden,new{ Message= "系统繁忙，请稍后再试" } );
                return;
            }

            base.OnActionExecuting(actionContext);
        }
    }
}