using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.ErpWeb
{
    /// <summary>
    /// 系统管理员登录验证
    /// </summary>
    public class FxAdminSessionAuthFilter : AuthorizationFilterAttribute
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public override void OnAuthorization(HttpActionContext actionContext)
        {
            // 检查IP白名单
            string ip;
            if (actionContext.Request.Properties.TryGetValue("MS_HttpContext", out var property))
            {
                var ctx = (HttpContextBase)property;
                ip = ctx.Request.UserHostAddress;
                if (!IpWhiteCheck(ip, ctx.Request.ServerVariables["REMOTE_ADDR"], ctx.Request.Headers["X-Forwarded-For"]))
                {
                    actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Forbidden);
                    return;
                }
            }
            else
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.InternalServerError);
                Log.WriteError("后台管理员登录时，无法获取HttpContextBase对象");
                return;

            }

            var authHeader = actionContext.Request.Headers.Authorization;

            // 检查是否有授权头
            if (authHeader != null && authHeader.Scheme.Equals("Basic", StringComparison.OrdinalIgnoreCase))
            {
                try
                {
                    // 解码并分割用户名和密码
                    var credentials = Encoding.UTF8.GetString(Convert.FromBase64String(authHeader.Parameter)).Split(':');

                    if (credentials.Length == 2)
                    {
                        var username = credentials[0];
                        var password = credentials[1];
                        
                        // 验证用户名和密码
                        if (IsValidUser(username, password,out var admin,ip))
                        {
                            // 同步到HttpContext
                            //if (actionContext.Request.Properties.TryGetValue("MS_HttpContext", out var obj))
                            //{
                            //    if(obj is HttpContextBase httpContextBase)
                            //        httpContextBase.Items["CurrentFxSystemAdmin"] = admin;
                            //}

                            // ReSharper disable once ObjectCreationAsStatement
                            new FxSystemAdminContext(admin); 
                            return; // 验证通过
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"后台管理员登录发生异常：{ex}");
                }
            }

            // 如果验证失败，返回未授权
            actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized);
        }

        /// <summary>
        /// 验证用户名和密码
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="admin"></param>
        /// <param name="ip"></param>
        /// <returns></returns>
        private static bool IsValidUser(string username, string password, out FxSystemAdmin admin,string ip = null)
        {
            admin = null;
            var hashPassword = FxSystemAdminService.HashPassword(password);

            var key = $"FxSystemAdmin:{username}:{hashPassword}";

            admin = MemoryCacheHelper.GetCacheItem<FxSystemAdmin>(key);
            if (admin?.Id == -1)
            {
                return false;
            }
            else if(admin!=null)
            {
                return true;
            }
             

            // 实现用户验证逻辑
            var isValid = new FxSystemAdminService().IsValid(username, password, out admin, ip);

            // 存储到缓存
            if (!isValid)
            {
                admin = new FxSystemAdmin() { Id = -1 };
            }
            MemoryCacheHelper.Set(key, admin, 1800);

            return isValid;
        }


        /// <summary>
        /// ip白名单校验
        /// </summary>
        /// <returns></returns>
        private static bool IpWhiteCheck(string userHostAddress, string remoteAddr, string ipforHeader)
        {
            if (CustomerConfig.IsLocalDbDebug)
                return true;
            try
            {
                //ip白名单控制，读取ip白名单
                var ipWhiteList = new CommonSettingService().Get<List<IpWhiteModel>>("/AllSystem/GetAllowIpList/", -168);
                if (ipWhiteList == null || ipWhiteList.Count == 0)
                    return false;

                if (string.IsNullOrWhiteSpace(ipforHeader) == false && ipWhiteList.Exists(f => ipforHeader.Contains(f.IP)))
                    return true;
                if (string.IsNullOrWhiteSpace(userHostAddress) == false && ipWhiteList.Exists(f => userHostAddress.Contains(f.IP)))
                    return true;
                if (string.IsNullOrWhiteSpace(remoteAddr) == false && ipWhiteList.Exists(f => remoteAddr.Contains(f.IP)))
                    return true;

                var ipStr = $"{userHostAddress}，{remoteAddr},{ipforHeader}";
                Log.WriteError($"非白名单的IP试图进入系统，IP：{ipStr}", "TestSiteIpWhiteLog.txt");
                return false;
            }
            catch (Exception ex)
            {
                Log.WriteError($"测试站点白名单检测报错：{ex.Message}", "TestSiteIpWhiteLog.txt");
            }
            return false;
        }
    }
}