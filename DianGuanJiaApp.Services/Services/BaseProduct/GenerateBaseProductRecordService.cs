using DianGuanJiaApp.Data.Entity.BaseProduct;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Model;
using System.Collections.Concurrent;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Services.Services.BaseProduct
{
    public class GenerateBaseProductRecordService :BaseService<GenerateBaseProductRecord>
    {
        private readonly GenerateBaseProductRecordRepository _repository;
        private readonly string _connectionString;

        /// <summary>
        /// 
        /// </summary>
        public GenerateBaseProductRecordService()
        {
            _repository = new GenerateBaseProductRecordRepository();
        }

        public GenerateBaseProductRecordService(string connectionString):base(connectionString)
        {
            _repository = new GenerateBaseProductRecordRepository(connectionString);
            _connectionString = connectionString;
        }


        /// <summary>
        /// 保存生成基础商品日志
        /// </summary>
        /// <param name="baseProducts"></param>
        public void SaveGenerateBaseProductRecord(List<BaseProductEntity> baseProducts)
        {
            _repository.BulkInsert(CreateRecords(baseProducts));
        }

        /// <summary>
        /// 清理快捷生成基础商品日志
        /// </summary>
        /// <param name="baseProducts"></param>
        public void DeleteGenerateBaseProductRecord(List<BaseProductEntity> baseProducts)
        {
            baseProducts = baseProducts.Where(p => 
                p.FromFxUserId > 0 && 
                p.FromProductUid.IsNotNullOrEmpty() && 
                p.FromShopId > 0)
                .ToList();
            if (!baseProducts.Any())
            {
                return;
            }
            try
            {
                #region 用户信息、数据库信息
                var dbRepository = new DbConfigRepository();
                var fxUserIds = baseProducts
                    .Select(p => p.FxUserId).Distinct().Where(f => f > 0).ToList();
                var sourceFxUserIds = baseProducts
                    .Select(p => p.FromFxUserId).Distinct().Where(f => f > 0).ToList();
                var allFxUserIds = new List<int>(fxUserIds);
                allFxUserIds.AddRange(sourceFxUserIds);
                allFxUserIds = allFxUserIds.Distinct().ToList();
               
                var cloudPlatformTypeList = new List<string> { "TouTiao", "Pinduoduo", "Jingdong", "Alibaba" };
                var allDbConfigs = dbRepository.GetListByFxUserIds(allFxUserIds, cloudPlatformTypeList);
                if (CustomerConfig.IsLocalDbDebug)
                {
                    allDbConfigs = allDbConfigs.Distinct(new DbConfigComparer()).ToList();
                }
                if (allDbConfigs.IsNullOrEmptyList())
                {
                    Log.WriteError($"用户：{allFxUserIds.ToJson()}未查询到DbConfigs！", "DeleteGenerateBaseProductRecord.txt");
                    return;
                }
                #endregion

                // 当前云快捷添加记录
                var currentCloudRecordDict = new ConcurrentDictionary<string, List<GenerateBaseProductRecord>>();

                // 当前删除商品列表
                var removeProductList = new List<BaseProductEntity>();

                // 旧库
                var oldDbConfigs = allDbConfigs.Where(d => d.DbConfig.FromFxDbConfig == 0).ToList();

                if (oldDbConfigs.Any())
                {
                    #region 旧库数据处理（分区）
                    var tempSourceUserIds = oldDbConfigs.Select(d => d.DbConfig.UserId).Distinct().ToList();
                    var sourceUserProducts = baseProducts.Where(p => tempSourceUserIds.Contains(p.FromFxUserId))
                        .ToList();

                    if (sourceUserProducts.Any())
                    {
                        var sourceUserProductDict = new ConcurrentDictionary<int, List<BaseProductEntity>>(sourceUserProducts
                           .GroupBy(r => r.FromFxUserId)
                           .ToDictionary(d => d.Key, d => d.ToList())
                           );

                        foreach (var oldDb in oldDbConfigs)
                        {
                            var cloud = oldDb.DbServer.Location;
                            if (!sourceUserProductDict.TryGetValue(oldDb.DbConfig.UserId, out var tempUserProducts))
                                continue;

                            var shopPlatList = CustomerConfig.CloudPlatWithShopPlatMapper[cloud];
                            var list = tempUserProducts.Where(p => shopPlatList.Contains(p.FromShopPt.ToLower())).ToList();
                            var tempRecords = CreateRecords(list);
                            if (!tempRecords.Any())
                            {
                                continue;
                            }
                            try
                            {
                                if (cloud == CloudPlatformType.Alibaba.ToString())
                                {
                                    currentCloudRecordDict.TryAddValue(oldDb.ConnectionString, tempRecords);
                                }
                                else
                                {
                                    var dbApi = new DbAccessUtility(new ApiDbConfigModel
                                    {
                                        DbNameConfigId = oldDb.DbNameConfig.Id,
                                        Location = oldDb.DbServer.Location,
                                        PlatformType = oldDb.DbServer.Location
                                    });
                                    _repository.BulkDeleteByDbApi(tempRecords, dbApi);
                                    Log.Debug(
                                        $"用户={oldDb.DbConfig.UserId}在旧库={oldDb.DbNameConfig.DbName}，" +
                                        $"跨云清理{tempRecords.Count}条失数据成功", "DeleteGenerateBaseProductRecord.txt");
                                }
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError(
                                    $"用户{oldDb.DbConfig.UserId}在旧库{oldDb.DbNameConfig.DbName}，" +
                                    $"跨云清理{tempRecords.ToJson()}失败，" +
                                    $"失败原因：{ex.ToJson()}", "DeleteGenerateBaseProductRecord.txt");
                            }

                            removeProductList.AddRange(list);
                        }
                    }
                    #endregion
                }
                // 过滤掉已在旧库处理的数据
                //baseProducts = baseProducts.Except(removeProductList).ToList();
                // 新库
                var newDbConfigs = allDbConfigs.Except(oldDbConfigs).ToList();

                if (newDbConfigs.Any())
                {
                    #region 新库数据处理
                    var userRecords = CreateRecords(baseProducts);
                    var userRecordDict =
                        new ConcurrentDictionary<int, List<GenerateBaseProductRecord>>(userRecords
                            .GroupBy(r => r.FxUserId)
                            .ToDictionary(d => d.Key, d => d.ToList())
                            );

                    // 根据业务库名分组
                    var dbConfigDict = newDbConfigs.GroupBy(d => d.DbConfig.UserId)
                        .ToDictionary(d => d.Key, d => d.ToList());

                    foreach (var fxUserId in dbConfigDict.Keys)
                    {
                        if (!dbConfigDict.TryGetValue(fxUserId, out var tempDbConfigs))
                            continue;

                        // 遍历用户的业务库
                        foreach (var dbConfig in tempDbConfigs)
                        {
                            Log.Debug(
                               $"当前fxUserId：{fxUserId}，" +
                               $"dbName：{dbConfig.DbNameConfig.DbName}", "DeleteGenerateBaseProductRecord.txt");
                            var shopPlats =
                                CustomerConfig.CloudPlatWithShopPlatMapper[dbConfig.DbServer.Location];
                            var tempRecords = new List<GenerateBaseProductRecord>();
                            if (!userRecordDict.TryGetValue(fxUserId, out tempRecords))
                            {
                                continue;
                            }
                            tempRecords = tempRecords.Where(d => shopPlats.Contains(d.PlatformType.ToLower())).ToList();
                            if (tempRecords.IsNullOrEmptyList())
                                continue;
                            try
                            {
                                var dbLocation = dbConfig.DbServer.Location;
                                if (dbLocation == CustomerConfig.CloudPlatformType)
                                {
                                    var connectionString = dbConfig.ConnectionString;
                                    currentCloudRecordDict.TryAddValue(connectionString, tempRecords);
                                }
                                else
                                {
                                    var dbApi = new DbAccessUtility(new ApiDbConfigModel
                                    {
                                        DbNameConfigId = dbConfig.DbNameConfig.Id,
                                        Location = dbConfig.DbServer.Location,
                                        PlatformType = dbConfig.DbServer.Location
                                    });
                                    _repository.BulkDeleteByDbApi(tempRecords, dbApi);
                                    Log.WriteLine(
                                        $"跨云清理{tempRecords.Count}条，" +
                                        $"当前fxUserId：{fxUserId}，" +
                                        $"dbName：{dbConfig.DbNameConfig.DbName}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError(
                                    $"用户{fxUserId}在业务库{dbConfig.DbNameConfig.DbName}" +
                                    $"写入{tempRecords.Count}条数据发生异常：{ex.ToJson()}");
                            }
                        }
                    }
                    #endregion
                }

                foreach (var connectionString in currentCloudRecordDict.Keys)
                {
                    try
                    {
                        var tempRecords = new List<GenerateBaseProductRecord>();
                        if (!currentCloudRecordDict.TryGetValue(connectionString,out tempRecords))
                        {
                            continue;
                        }
                        tempRecords = tempRecords.Distinct().ToList();
                        //var tempRecords = currentCloudRecordDict[connectionString].Distinct().ToList();
                        if (tempRecords.Any())
                        {
                            foreach (var item in tempRecords)
                            {
                                item.Status = -1;
                            }
                            new GenerateBaseProductRecordRepository(connectionString)
                              .BatchUpdate(tempRecords,
                                  new List<string> { "Status" },
                                  new List<string> { "UniqueKey" }
                               );
                            Log.Debug($"本云清理{tempRecords.Count}条，connectionString：{connectionString}", "DeleteGenerateBaseProductRecord.txt");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError(
                            $"本云清理GenerateBaseProductRecord发生异常：{ex} \n connectionString:{connectionString},写入数据：{currentCloudRecordDict[connectionString].ToJson()}", "DeleteGenerateBaseProductRecord.txt");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"删除基础商品，跨云清理快捷添加记录异常，异常原因：{ex.ToJson()}");
            }
        }

        private Dictionary<int, List<DbConfigModel>> UserDbConfigDict { get; set; } =
            new Dictionary<int, List<DbConfigModel>>();

        /// <summary>
        /// 通过工具初始化生成记录，仅工具使用
        /// </summary>
        public void InitRecordByTool(long? startDbId = null,long? endDbId = null)
        {
            var lastId = startDbId.ToLong();
            Log.WriteLine($"开始轮询基础商品分库");
            // 查出基础主库中所有的分库配置
            var dbConfigListByBaseMaster = new ProductDbConfigRepository().GetAllDbConfigModel();
            var dbRepository = new DbConfigRepository();
            dbConfigListByBaseMaster?.ForEach(productDbConfig =>
            {
                Log.WriteLine($"基础商品分库：{productDbConfig.ProductDbNameConfig.DbName}");
                try
                {
                    bool hasMore;
                    do
                    {
                        long queryPageSize = 5000;
                        var isEnd = false;
                        if (endDbId!=null &&(lastId + queryPageSize.ToLong() > endDbId.ToLong()))
                        {
                            queryPageSize = endDbId.ToLong() - lastId;
                            isEnd = true;
                        }

                        // 游标轮询基础商品表，构建GenerateBaseProductRecord数据
                        var baseProducts = new BaseProductRepository(productDbConfig?.ConnectionString).GetPageForInitGenerateRecord(lastId, endDbId, queryPageSize);
                        Log.WriteLine($"当前查询baseProductId：{lastId}~{lastId+queryPageSize}，查询结果数：{baseProducts.Count}");
                        if (baseProducts.IsNotNullAndAny() && !isEnd)
                        {
                            lastId = baseProducts.Select(t => t.Id).Max();
                            hasMore = true;
                        }
                        else if(baseProducts.IsNullOrEmptyList()) break;
                        else hasMore = false;

                        
                        // 根据FxUserId获取DbConfig，构造ApiDbConfigModel
                        var fxUserIds = baseProducts.Select(p => p.FxUserId).Distinct().Where(f => f > 0).ToList();
                        var sourceFxUserIds = baseProducts.Select(p => p.FromFxUserId).Distinct().Where(f => f > 0)
                            .ToList();

                        var allFxUserIds = new List<int>(fxUserIds);
                        allFxUserIds.AddRange(sourceFxUserIds);
                        allFxUserIds = allFxUserIds.Distinct().ToList();


                        var cloudPlatformTypeList = new List<string> { "TouTiao", "Pinduoduo", "Jingdong", "Alibaba" };

                        var allDbConfigs = new List<DbConfigModel>();
                        var needQueryIds = new List<int>();
                        // 先从本地缓存找一下
                        allFxUserIds.ForEach(id =>
                        {
                            if (UserDbConfigDict.TryGetValue(id, out var list))
                            {
                                allDbConfigs.AddRange(list);
                            }
                            else
                            {
                                needQueryIds.Add(id);
                            }
                        });

                        var queryDbConfigs = dbRepository.GetListByFxUserIds(needQueryIds, cloudPlatformTypeList);
                        if (CustomerConfig.IsLocalDbDebug)
                        {
                            queryDbConfigs = queryDbConfigs.Distinct(new DbConfigComparer()).ToList();
                        }
                        allDbConfigs.AddRange(queryDbConfigs);

                        if (allDbConfigs.IsNullOrEmptyList())
                        {
                            Log.WriteWarning($"用户{allFxUserIds.ToJson()}未查询到DbConfig！");
                            continue;
                        }
                        // 加入到缓存中
                        var queryDbGroups= queryDbConfigs.GroupBy(d => d.DbConfig.UserId);
                        foreach (var group in queryDbGroups)
                        {
                            if (!UserDbConfigDict.ContainsKey(group.Key))
                            {
                                UserDbConfigDict.Add(group.Key,group.ToList());
                            }
                        }
                        
                        //var dbConfigDict = new ConcurrentDictionary<int, List<DbConfigModel>>(allDbConfigs.GroupBy(d => d.DbConfig.UserId)
                        //        .ToDictionary(d => d.Key, d => d.ToList()));

                        //var userRecordDict = new ConcurrentDictionary<int, List<GenerateBaseProductRecord>>();
                        
                        var currentCloudRecordDict = new ConcurrentDictionary<string, List<GenerateBaseProductRecord>>();
                        var maxParall = 10;
                        if (CustomerConfig.IsLocalDbDebug)
                            maxParall = 1;

                        var removeProductList = new List<BaseProductEntity>(baseProducts.Count/2);
                        var oldDbConfigs = allDbConfigs.Where(d => d.DbConfig.FromFxDbConfig == 0).ToList();
                        
                        if (oldDbConfigs.IsNotNullAndAny())
                        {
                            Log.WriteLine($"过滤出旧库{oldDbConfigs}，需要用源商家过滤基础商品生成记录");
                            var tempSourceUserIds = oldDbConfigs.Select(d => d.DbConfig.UserId).Distinct().ToList();
                            Log.WriteLine($"旧库配置关联的源商家：{tempSourceUserIds.ToJson()}");
                            var sourceUserProducts = baseProducts.Where(p => tempSourceUserIds.Contains(p.FromFxUserId))
                                .ToList();
                            Log.WriteLine($"通过旧库配置的源商家过滤出的商品数：{sourceUserProducts.Count}");

                            if(sourceUserProducts.IsNullOrEmptyList())
                                continue;

                            var sourceUserProductDict =
                                new ConcurrentDictionary<int, List<BaseProductEntity>>(sourceUserProducts.GroupBy(r => r.FromFxUserId)
                                    .ToDictionary(d => d.Key, d => d.ToList()));
                            Log.WriteLine("开始遍历旧库配置");
                            Parallel.ForEach(oldDbConfigs,
                                new ParallelOptions { MaxDegreeOfParallelism = maxParall },
                                c =>
                                {
                                    var cloud = c.DbServer.Location;
                                    if (!sourceUserProductDict.TryGetValue(c.DbConfig.UserId, out var tempUserProducts))
                                        return;

                                    var shopPlatList = CustomerConfig.CloudPlatWithShopPlatMapper[cloud];
                                    var list = tempUserProducts
                                        .Where(p => shopPlatList.Contains(p.FromShopPt.ToLower())).ToList();

                                    var tempRecords = CreateRecords(list);
                                    lock (removeProductList)
                                    {
                                        removeProductList.AddRange(list);
                                    }

                                    if (cloud == CloudPlatformType.Alibaba.ToString())
                                    {
                                        currentCloudRecordDict.TryAddValue(c.ConnectionString,tempRecords);

                                        Log.WriteLine(
                                            $"用户{c.DbConfig.UserId}在旧库{c.DbNameConfig.DbName}，{cloud}云平台生成记录：{tempRecords.Count}条");
                                    }
                                    else
                                    {
                                        // 注意 此方法内部未调用 _repository.DbConnection 所以可以在未初始化上下文的情况下使用_repository
                                        var dbApi = new DbAccessUtility(new ApiDbConfigModel
                                        {
                                            DbNameConfigId = c.DbNameConfig.Id,
                                            Location = c.DbServer.Location,
                                            PlatformType = c.DbServer.Location
                                        });
                                        _repository.BulkMergeByDbApi(tempRecords, dbApi);
                                        Log.WriteLine($"用户{c.DbConfig.UserId}在旧库{c.DbNameConfig.DbName}，跨云写入{tempRecords.Count}条");

                                    }
                                });
                            Log.WriteLine($"旧库配置遍历完成，总写入数据{removeProductList.Count}条");
                        }

                        // 过滤掉已在旧库写入的数据
                        baseProducts = baseProducts.Except(removeProductList).ToList();
                        if (baseProducts.IsNullOrEmptyList())
                        {
                            Log.WriteLine("排除旧库写入商品数据后，剩余商品数据为空");
                            continue;
                        }
                        // 排除旧库
                        allDbConfigs = allDbConfigs.Except(oldDbConfigs).ToList();
                        if (allDbConfigs.IsNullOrEmptyList())
                        {
                            Log.WriteLine("排除旧库配置后，剩余配置为空");
                            continue;
                        }

                        var userRecords = CreateRecords(baseProducts);
                        var userRecordDict =
                            new ConcurrentDictionary<int, List<GenerateBaseProductRecord>>(userRecords.GroupBy(r => r.FxUserId)
                                .ToDictionary(d => d.Key, d => d.ToList()));

                        // 根据业务库名分组
                        var dbConfigDict = allDbConfigs.GroupBy(d => d.DbConfig.UserId)
                            .ToDictionary(d => d.Key, d => d.ToList());


                        Log.WriteLine("开始遍历业务库");
                        Parallel.ForEach(userRecordDict.Keys, new ParallelOptions { MaxDegreeOfParallelism = maxParall },
                            fxUserId =>
                            {
                                if(!dbConfigDict.TryGetValue(fxUserId,out var tempDbConfigs))
                                    return;
                                
                                // 遍历用户的业务库
                                tempDbConfigs.ForEach(dbConfig =>
                                {
                                    Log.WriteLine($"当前fxUserId：{fxUserId}，dbName：{dbConfig.DbNameConfig.DbName}");
                                    // 当前用户在该业务库下应写入的数据
                                    var shopPlats =
                                        CustomerConfig.CloudPlatWithShopPlatMapper[dbConfig.DbServer.Location];
                                    var tempRecords = userRecordDict[fxUserId].Where(d => shopPlats.Contains(d.PlatformType.ToLower())).ToList();
                                    if (tempRecords.IsNullOrEmptyList())
                                        return;
                                    try
                                    {
                                        var dbLocation = dbConfig.DbServer.Location;

                                        // 同云
                                        if (dbLocation == CustomerConfig.CloudPlatformType)
                                        {
                                            var connectionString = dbConfig.ConnectionString;
                                            currentCloudRecordDict.TryAddValue(connectionString, tempRecords);

                                            Log.WriteLine(
                                                $"用户{fxUserId}在业务库{dbConfig.DbNameConfig.DbName}，{dbLocation}云平台生成记录：{tempRecords.Count}条");
                                        }
                                        // 不同云分批次生成sql 进行插入
                                        else
                                        {
                                            // 注意 此方法内部未调用 _repository.DbConnection 所以可以在未初始化上下文的情况下使用_repository
                                            var dbApi = new DbAccessUtility(new ApiDbConfigModel
                                            {
                                                DbNameConfigId = dbConfig.DbNameConfig.Id,
                                                Location = dbConfig.DbServer.Location,
                                                PlatformType = dbConfig.DbServer.Location
                                            });
                                            _repository.BulkMergeByDbApi(tempRecords, dbApi);
                                            Log.WriteLine($"跨云写入{tempRecords.Count}条，当前fxUserId：{fxUserId}，dbName：{dbConfig.DbNameConfig.DbName}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.WriteError($"用户{fxUserId}在业务库{dbConfig.DbNameConfig.DbName}写入{tempRecords.Count}条数据发生异常：{ex}", "RecordInsertError.txt");
                                    }
                                });
                            });

                        // 在当前云下，通过连接字符串构造仓储批量写入
                        Log.WriteLine($"开始本云写入");
                        foreach (var connectionString in currentCloudRecordDict.Keys)
                        {
                            try
                            {
                                var tempRecords = currentCloudRecordDict[connectionString].Distinct().ToList();
                                new GenerateBaseProductRecordRepository(connectionString).BulkMerge(tempRecords);
                                Log.WriteLine($"本云写入{tempRecords.Count}条，connectionString：{connectionString}");
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError(
                                    $"本云写入GenerateBaseProductRecord发生异常：{ex} \n connectionString:{connectionString},写入数据：{currentCloudRecordDict[connectionString].ToJson()}");
                            }
                        }
                        Log.WriteLine($"本云写入完成");
                    } while (hasMore);


                }
                catch (Exception ex)
                {
                    Log.WriteError($"工具生成GenerateBaseProductRecord发生异常：{ex}");
                }
            });
           
            
        }

        /// <summary>
        /// 通过基础商品创建生成记录
        /// </summary>
        /// <param name="baseProducts"></param>
        /// <param name="isFromUser"></param>
        /// <returns></returns>
        private static List<GenerateBaseProductRecord> CreateRecords(List<BaseProductEntity> baseProducts)
        {
            if (baseProducts.IsNullOrEmptyList())
                return new List<GenerateBaseProductRecord>();

            var records = new List<GenerateBaseProductRecord>(baseProducts.Count);
            baseProducts.ForEach(p =>
            {
                records.Add(new GenerateBaseProductRecord()
                {
                    BaseProductUid = p.Uid,
                    FxUserId = p.FxUserId,
                    ProductCode = p.FromProductUid,
                    ShopId = p.FromShopId,
                    PlatformType = p.FromShopPt,
                    CreateTime = DateTime.Now,
                });
            });
            return records;
        }

        private class DbConfigComparer : IEqualityComparer<DbConfigModel>
        {
            public bool Equals(DbConfigModel x, DbConfigModel y)
            {
                if (x == null || y == null)
                    return false;

                return x.ConnectionString == y.ConnectionString && x.DbConfig.ShopId == y.DbConfig.ShopId;
            }

            public int GetHashCode(DbConfigModel obj)
            {
                return (obj.ConnectionString+obj.DbConfig.ShopId).GetHashCode();
            }
        }


        /// <summary>
        /// 根据商品编码列表和分销用户ID获取商品编码列表
        /// </summary>
        /// <param name="productCodes">商品编码列表</param>
        /// <param name="fxUserId">分销用户ID</param>
        /// <returns>符合条件的商品编码列表</returns>
        public List<string> GetListByProductCodes(List<string> productCodes, int fxUserId)
        {
            if (productCodes.IsNullOrEmptyList())
                return new List<string>();
            
            return _repository.GetListByProductCodes(productCodes, fxUserId);
        }
    }

    internal static class ConcurrentUtil
    {
        internal static void TryAddValue<T,TR>(this ConcurrentDictionary<T, List<TR>> dict,T key,List<TR> addValue) 
        {
            lock (dict)
            {
                if (dict.TryGetValue(key, out var oldValue))
                {
                    oldValue.AddRange(addValue);
                    addValue = oldValue.Distinct().ToList();
                    dict.TryUpdate(key, oldValue, addValue);
                }
                else
                {
                    dict.TryAdd(key, addValue);
                }
            }

        }
    }
}
