using System;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Model;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository.BaseProduct
{
    /// <summary>
    /// 从店铺商品生成基础商品记录的仓储
    /// </summary>
    public class GenerateBaseProductRecordRepository : BaseRepository<GenerateBaseProductRecord>
    {
        private string _connectionString = string.Empty;

        #region 构造
        /// <summary>
        /// 仅业务库
        /// </summary>
        public GenerateBaseProductRecordRepository()
        {
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        public GenerateBaseProductRecordRepository(string connectionString):base(connectionString)
        {
            _connectionString = connectionString;
        }

        #endregion

        /// <summary>
        /// 通过UniqueKey查询已存在的
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCode(List<string> code)
        {
            if(code.IsNullOrEmptyList())
                return new List<IdAndCodeModel>();

            var sqlByIn = "SELECT UniqueKey AS Code FROM GenerateBaseProductRecord WITH(NOLOCK) WHERE UniqueKey IN @codes";
            var sqlByTable =
                "SELECT UniqueKey AS Code FROM GenerateBaseProductRecord r WITH(NOLOCK) INNER JOIN FunStringToTable(@codes,',') t ON t.Item = r.UniqueKey";

            return SqlOptimizationHandler.QueryEntities(code,sqlByIn, sqlByTable,
                (sql, param) => DbConnection.Query<IdAndCodeModel>(sql, param).ToList(),"codes");
        }

        /// <summary>
        /// 批量合并
        /// </summary>
        /// <param name="records"></param>
        public void BulkMerge(List<GenerateBaseProductRecord> records)
        {
            if(records.IsNullOrEmptyList())
                return;
            var allKeys = records.Select(r => r.UniqueKey).ToList();
            var existKeys = GetExistIdAndCode(allKeys).Select(m=>m.Code).ToList();
            var addKeys = allKeys.Except(existKeys).ToList();

            var recordDict = records.ToDictionary(r => r.UniqueKey);
            if (addKeys.IsNotNullAndAny())
            {
                var tempRecords = recordDict.Where(r => addKeys.Contains(r.Key)).Select(d => d.Value).ToList();
                BulkInsert(tempRecords);
            }

            if (existKeys.IsNotNullAndAny())
            {
                var tempRecords = recordDict.Where(r => existKeys.Contains(r.Key)).Select(d => d.Value).ToList();
                BatchUpdate(tempRecords,new List<string>(){"UniqueKey"});
            }

        }

        /// <summary>
        /// 跨云批量合并
        /// </summary>
        /// <param name="records"></param>
        /// <param name="dbApi"></param>
        public void BulkMergeByDbApi(List<GenerateBaseProductRecord> records, DbAccessUtility dbApi)
        {
            if (records.IsNullOrEmptyList() || dbApi == null)
                return;
            var allKeys = records.Select(r => r.UniqueKey).ToList();

            const string sqlByTable =
                "SELECT Id, UniqueKey AS Code FROM GenerateBaseProductRecord r WITH(NOLOCK) INNER JOIN FunStringToTable(@codes,',') t ON t.Item = r.UniqueKey";

            var existKeys = dbApi.Query<IdAndCodeModel>(sqlByTable, new { codes = string.Join(",",allKeys) }).Select(m => m.Code).ToList(); ;
            
            var addKeys = allKeys.Except(existKeys).ToList();

            var recordDict = records.ToDictionary(r => r.UniqueKey);
            if (addKeys.IsNotNullAndAny())
            {
                var tempRecords = recordDict.Where(r => addKeys.Contains(r.Key)).Select(d => d.Value).ToList();
                BatchInsertByDbApi(tempRecords, dbApi);
            }

            if (existKeys.IsNotNullAndAny())
            {
                var tempRecords = recordDict.Where(r => existKeys.Contains(r.Key)).Select(d => d.Value).ToList();
                BatchUpdateByDbApi(tempRecords, dbApi);
            }

        }

        /// <summary>
        /// 跨云批量清理
        /// </summary>
        /// <param name="records"></param>
        /// <param name="dbApi"></param>
        public void BulkDeleteByDbApi(List<GenerateBaseProductRecord> records, DbAccessUtility dbApi)
        {
            if (records.IsNullOrEmptyList() || dbApi == null)
                return;
            var allKeys = records.Select(r => r.UniqueKey).Distinct().ToList();
            var allkeyPageSize = 200;
            var allKeyCount = allKeys.Count();
            var allKeyPageTotals = Math.Ceiling(allKeyCount * 1.0 / allkeyPageSize);

            const string sqlByTable =
               "UPDATE GenerateBaseProductRecord SET Status=-1 WHERE UniqueKey IN @uniqueKey";
            for (int i = 0; i < allKeyPageTotals; i++)
            {
                try
                {
                    var tempKey = allKeys.Skip(i * allkeyPageSize).Take(allkeyPageSize).ToList();
                    dbApi.ExecuteScalar(sqlByTable, new { uniqueKey = tempKey });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"第{i + 1}次清理快捷添加记录失败，失败原因：{ex.ToJson()}", "DeleteGenerateBaseProductRecord.txt");
                }
            }
        }

        /// <summary>
        /// 需要写入的字段
        /// </summary>
        private static List<PropertyInfo> WritePropertyInfos => typeof(GenerateBaseProductRecord).GetProperties()
            .Where(f => !f.GetCustomAttributes<NotMappedAttribute>().Any() && !f.GetCustomAttributes<KeyAttribute>().Any())
            .ToList();

        /// <summary>
        /// 通过DbApi跨云批量插入
        /// </summary>
        /// <param name="tempRecords"></param>
        /// <param name="dbApi"></param>
        public void BatchInsertByDbApi(List<GenerateBaseProductRecord> tempRecords, DbAccessUtility dbApi)
        {
            if(tempRecords.IsNullOrEmptyList() || dbApi == null)
                return;

            // 生成批量InsertSql 并通过dbApi执行写入

            const int batchSize = 50;
            for (var i = 0; i * batchSize < tempRecords.Count; i++)
            {
                var batchRecords = tempRecords.Skip(i * batchSize).Take(batchSize).ToList();

                var fields = WritePropertyInfos;
                var batchInsertSql = string.Empty;
                var batchParams = new DynamicParameters();
                var insertFields = string.Join(",", fields.Select(f => $"{f.Name}")) ;
                batchRecords.ForEach(r =>
                {
                    var values = new List<string>();
                    fields.ForEach(f =>
                    {
                        var thisField = $"@{f.Name}_{r.BaseProductUid}";
                        values.Add(thisField);
                        batchParams.Add(thisField,f.GetValue(r));
                    });
                    batchInsertSql += $"\n INSERT INTO GenerateBaseProductRecord ({insertFields}) VALUES({string.Join(",", values)})";
                });

                try
                {
                    dbApi.ExecuteScalar(batchInsertSql, batchParams);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"通过DbApi跨云批量插入GenerateBaseProductRecord发生异常：{ex} \n 插入语句：{GetRealSql(batchInsertSql, batchParams)}", "DbApi-error.txt");
                }
            }
        }

        /// <summary>
        /// 通过DbApi跨云批量插入
        /// </summary>
        /// <param name="tempRecords"></param>
        /// <param name="dbApi"></param>
        public void BatchUpdateByDbApi(List<GenerateBaseProductRecord> tempRecords, DbAccessUtility dbApi)
        {
            if (tempRecords.IsNullOrEmptyList() || dbApi == null)
                return;

            // 生成批量UpdateSql 并通过dbApi执行写入

            const int batchSize = 50;
            for (var i = 0; i * batchSize < tempRecords.Count; i++)
            {
                var batchRecords = tempRecords.Skip(i * batchSize).Take(batchSize).ToList();

                var fields = WritePropertyInfos;
                var batchUpdateSql = string.Empty;
                var batchParams = new DynamicParameters();
                batchRecords.ForEach(r =>
                {
                    var updateFields = new List<string>();
                    fields.ForEach(f =>
                    {
                        var thisField = $"@{f.Name}_{r.BaseProductUid}";
                        batchParams.Add(thisField, f.GetValue(r));

                        if (f.Name != "UniqueKey") 
                            updateFields.Add($"{f.Name} = {thisField}");
                        
                    });
                    batchUpdateSql += $"\n UPDATE GenerateBaseProductRecord SET {string.Join(",", updateFields)} WHERE UniqueKey = @UniqueKey_{r.BaseProductUid}";
                });

                try
                {
                    dbApi.ExecuteScalar(batchUpdateSql, batchParams);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"通过DbApi跨云批量更新GenerateBaseProductRecord发生异常：{ex} \n 插入语句：{GetRealSql(batchUpdateSql, batchParams)}", "DbApi-error.txt");
                }
            }
        }

        /// <summary>
        /// 根据商品编码列表和分销用户ID获取商品编码列表
        /// </summary>
        /// <param name="productCodes">商品编码列表</param>
        /// <param name="fxUserId">分销用户ID</param>
        /// <returns>符合条件的商品编码列表</returns>
        public List<string> GetListByProductCodes(List<string> productCodes, int fxUserId)
        {
            if (productCodes.IsNullOrEmptyList())
                return new List<string>();
            const string sql = "SELECT DISTINCT ProductCode FROM GenerateBaseProductRecord r WITH(NOLOCK) INNER JOIN FunStringToTable(@codes, ',',) t ON t.Item = r.ProductCode WHERE FxUserId=@fxUserId";
            return DbConnection.Query<string>(sql, new { codes = string.Join(",", productCodes), fxUserId }).ToList();
        }
    }
}
