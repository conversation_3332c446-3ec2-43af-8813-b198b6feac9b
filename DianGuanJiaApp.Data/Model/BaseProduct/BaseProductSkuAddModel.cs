using System.Collections.Generic;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    public class BaseProductSkuAddModel
    {
        #region 商品信息
        /// <summary>
        /// 商品编码，由用户填写，用户维度唯一
        /// </summary>
        public string SpuCode { get; set; }
        
        /// <summary>
        /// SPU商品标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 默认空字符，简称
        /// </summary>
        public string ShortTitle { get; set; } = string.Empty;

        /// <summary>
        /// 创建来源，可选值如下：
        /// Manual手工创建
        /// Product来自店铺商品
        /// AlibabaProduct来自1688货源
        /// Import导入
        /// Listing供应链铺货后自动创建
        /// Copy复制
        /// OpenPlatform 开放平台添加
        /// </summary>
        public string CreateFrom { get; set; }

        /// <summary>
        /// 创建来源的对象唯一ID，不直接用于关联
        /// 默认空字符串，创建来源的对象唯一ID，可选值如下
        /// Product.ProductCode
        /// AlibabaProduct.ProductCode
        /// Copy来源的SupplierProduct.Uid
        /// Listing来源的SupplierProduct.Uid
        /// 该字段不能直接用于关联，字段类型和源数据不一样
        /// </summary>
        public string FromProductUid { get; set; } = string.Empty;

        /// <summary>
        /// 默认0，创建来源的用户ID，可选值如下
        /// Product.SourceUserId
        /// AlibabaProduct.SourceUserId
        /// Copy来源的BaseProduct.FxUserId
        /// Listing来源的BaseProduct.FxUserId
        /// </summary>
        public int FromFxUserId { get; set; } = 0;

        /// <summary>
        /// 默认0，创建来源的用户ID，可选值如下
        /// Product.ShopId
        /// AlibabaProduct.ShopId
        /// </summary>
        public int FromShopId { get; set; } = 0;

        /// <summary>
        /// 默认空字符，创建来源的店铺平台类型，可选值来源
        /// Product.PlatformType
        /// AlibabaProduct.PlatformType
        /// </summary>
        public string FromShopPt { get; set; } = string.Empty;

        /// <summary>
        /// 若从货盘复制，则有值
        /// </summary>
        public int? RootNodeFxUserId { get; set; }

        /// <summary>
        /// 若从货盘复制，则有值
        /// </summary>
        public string SharePathCode { get; set; }
        #endregion

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<string> ProductImagesStr { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        public List<string> DescriptionStr { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<BaseProductImageModel> ProductImages { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        public List<BaseProductImageModel> Description { get; set; }

        /// <summary>
        /// 商品SKU
        /// </summary>
        public List<BaseProductSkuModel> ProductSkus { get; set; }

        /// <summary>
        /// 供货厂家配置
        /// </summary>
        public List<BaseProductConfigModel> ConfigModels { get; set; }

        /// <summary>
        /// 路径节点深度，从货盘复制时有值
        /// </summary>
        public int? PathNodeDeep { get; set; }

        /// <summary>
        /// 铺货源商品编码
        /// </summary>
        public string SourceSpuCode { get; set; }
        
        /// <summary>
        /// 分享路径流,从厂家货盘复制时携带
        /// </summary>
        public SharePathFlow SharePathFlow { get; set; }

        /// <summary>
        /// 平台规格模式=0（默认）、自定义规格模式=1
        /// </summary>
        public int SkuModeType { get; set; }

        /// <summary>
        /// 复制时上游的基础商品Uid
        /// </summary>
        public string UpBaseProductUid { get; set; }
        
        /// <summary>
        /// 是否继续生成即时零售商品
        /// </summary>
        public bool? IsTranslateSaleShop { get; set; }
    }

    public class BaseProductSkuModel
    {
        /// <summary>
        /// 规格编码，由用户填写，用户维度唯一
        /// </summary>
        public string SkuCode { get; set; }

        /// <summary>
        /// 主图的URL，来源OssObject.Url
        /// 该字段为冗余字段，可避免关联表查询
        /// </summary>
        public BaseProductImageModel ImageUrl { get; set; }

        /// <summary>
        /// 主图的URL，来源OssObject.Url
        /// 该字段为冗余字段，可避免关联表查询
        /// </summary>
        public string ImageUrlStr { get; set; }

        /// <summary>
        /// SKU简称，默认空字符串
        /// </summary>
        public string ShortTitle { get; set; } = string.Empty;

        /// <summary>
        ///标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 成本价（采购价）
        /// 成本价在供应链中可能是指的上游厂家对该用户
        /// </summary>
        public decimal? CostPrice { get; set; }

        /// <summary>
        /// 默认采购价，对上游厂家设置生效
        /// </summary>
        public decimal? SettlePrice { get; set; }

        /// <summary>
        /// 默认分销价，对下游商家设置生效
        /// </summary>
        public decimal? DistributePrice { get; set; }

        /// <summary>
        /// 默认0，重量，单位克
        /// </summary>
        public int? Weight { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public int? StockCount { get; set; }

        /// <summary>
        /// 是否公开，默认0，不公开，公开后货盘可见
        /// </summary>
        public bool? IsPublic { get; set; } = false;

        /// <summary>
        /// 默认0，上游用户ID，通过供应链创建的，则有值
        /// </summary>
        public int? UpFxUserId { get; set; }

        /// <summary>
        /// 默认空字符串，上游用户的SKU唯一Id
        /// </summary>
        public string UpSkuUid { get; set; } = string.Empty;

        /// <summary>
        /// 默认空字符串，JSON格式，格式如下
        /// 该字段为冗余字段-可避免关联表查询
        /// [{"k":"颜色","v":"白色"},{"k":"尺寸","v":"XL"},{ "k":"材质","v":"棉质"}]
        /// k：对应BaseProductSkuAttribute.AttributeName1/2/3
        /// v：对应BaseProductSkuAttribute.AttributeValue1/2/3
        /// </summary>
        public BaseProductSkuAttributeModel Attribute { get; set; }

        /// <summary>
        /// 来源BaseProductSkuAttribute表Value值，用分号隔开，如：value1;value2;value3
        /// 如果规格是以自定义规格模式进行保存，则这个值表示自定义规格名称
        /// </summary>
        public string AttributeValue { get; set; }

        /// <summary>
        /// 是否组合SKU，默认0,1表示是组合sku，仅当基础商品数据从库存组合货品创建时才为1
        /// </summary>
        public bool? IsCombineSku { get; set; }

        /// <summary>
        /// 厂家绑定
        /// </summary>
        public List<BaseProductSkuSupplierConfigModel> BaseProductSkuSupplierConfig { get; set; }

        /// <summary>
        /// SkuUid,修改时传入
        /// </summary>
        public long SkuUid { get; set; }

        /// <summary>
        /// 是否更新简称
        /// </summary>
        public bool IsUpdateShortTitle { get; set; }
        /// <summary>
        /// 是否更新结算价 对厂家
        /// </summary>
        public bool IsUpdateSettlePrice { get; set; }
        /// <summary>
        /// 是否更新成本价
        /// </summary>
        public bool IsUpdateCostPrice { get; set; }
        /// <summary>
        /// 是否更新分销价 对商家
        /// </summary>
        public bool IsUpdateDistributePrice { get; set; }

        /// <summary>
        /// 是否更新结算价到已关联的平台商品 
        /// </summary>
        public bool IsUpdatePtSkuSettlePrice { get; set; }
        /// <summary>
        /// 是否更新成本价到已关联的平台商品 
        /// </summary>
        public bool IsUpdatePtSkuCostPrice { get; set; }
        /// <summary>
        /// 是否更新分销价到已关联的平台商品 
        /// </summary>
        public bool IsUpdatePtSkuDistributePrice { get; set; }

        /// <summary>
        /// 入库或出库，true入库，false出库
        /// </summary>
        public bool InOrOut { get; set; }

        /// <summary>
        /// 变动库存数量
        /// </summary>
        public int ChangeStockCount { get; set; } = 0;


        /// <summary>
        /// 是否是新规格 true=使用新编码创建一条基础商品sku，false=旧规格修改编码 null-未修改
        /// </summary>
        public bool? IsNewSkuCode { get; set; }
        /// <summary>
        /// 旧规格编码 IsNewSkuCode = false时才有意义
        /// </summary>
        public string OldSkuCode { get; set; }

        /// <summary>
        /// 规格图id 新图=0
        /// </summary>
        public string ImageId { get; set; }

        /// <summary>
        /// 若从货盘复制，则有值
        /// </summary>
        public int? RootNodeFxUserId { get; set; }

        /// <summary>
        /// 若从货盘复制，则有值
        /// </summary>
        public string SharePathCode { get; set; }

        /// <summary>
        /// 路径节点深度，从货盘复制时有值
        /// </summary>
        public int? PathNodeDeep { get; set; }

        /// <summary>
        /// 铺货源规格编码
        /// </summary>
        public string SourceSkuCode { get; set; }

        /// <summary>
        /// 分享路径流,从厂家货盘复制时携带
        /// </summary>
        public SharePathFlow SharePathFlow { get; set; }

        /// <summary>
        /// 子货品信息
        /// </summary>
        public List<BaseProductSkuChildModel> ChildSkuList { get; set; }
    }
    
    public class BaseProductImageModel
    {
        /// <summary>
        /// 图片相对路径
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 图片域名，冗余字段，在对象表中有
        /// </summary>
        public string Domain { get; set; }

        /// <summary>
        /// 文件后缀
        /// </summary>
        public string Suffix { get; set; }

        ///// <summary>
        ///// 是否是主图
        ///// </summary>
        //public bool IsMain { get; set; }

        /// <summary>
        /// Oss对象的ID，来自oss接口
        /// </summary>
        public string ImageObjectId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// BaseProductImage.Id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullUrl => string.IsNullOrEmpty(Suffix) ? $"{Domain}/{Url}/{Name}" : $"{Domain}/{Url}/{Name}.{Suffix}";

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDelete { get; set; }
    }

    public class BaseProductSkuSupplierConfigModel
    {
        /// <summary>
        /// 厂家Id
        /// </summary>
        public int? SupplierFxUserId { get; set; }

        /// <summary>
        /// 详细配置信息，可为空
        /// </summary>
        public string Config { get; set; }

        /// <summary>
        /// 匹配规则：0默认；1按地址；2按下单时间
        /// 为0时，Config为空，同一SkuUid最多只有一条为0的
        /// </summary>
        public int ConfigType { get; set; }

        /// <summary>
        /// 应用范围，共三种：0应用到自营+商家推送，1仅应用到自营，2仅应用到商家推送
        /// </summary>
        public int ApplyScope { get; set; }

        /// <summary>
        /// 是否是自营
        /// </summary>
        public bool IsSelf { get; set; }
    }

    public class BaseProductConfigModel
    {
        /// <summary>
        /// 厂家Id
        /// </summary>
        public int SupplierFxUserId { get; set; }

        /// <summary>
        /// 关联类型：Product/Sku，为空时即为Sku
        /// </summary>
        public string RefType { get; set; }

        /// <summary>
        /// 对应BaseProductSku.Uid，对BaseProduct.Uid
        /// </summary>
        public long RefUid { get; set; }

        /// <summary>
        /// 基础商品Uid
        /// </summary>
        public long ProductUid { get; set; }

        /// <summary>
        /// 基础商品SkuUid
        /// </summary>
        public long SkuUid { get; set; }

        /// <summary>
        /// 匹配规则：0默认；1按地址；2按下单时间
        /// 为0时，Config为空，同一SkuUid最多只有一条为0的
        /// </summary>
        public int ConfigType { get; set; } = 0;

        /// <summary>
        /// 应用范围，共三种：0应用到自营+商家推送，1仅应用到自营，2仅应用到商家推送
        /// </summary>
        public int ApplyScope { get; set; } = 0;

        /// <summary>
        /// 唯一编码
        /// </summary>
        public string RefCode { get; set; }
    }


    public class BaseProductSkuAttributeModel
    {
        /// <summary>
        /// 图片的对应属性序号，可选值：0，1、2、3，0表示没有图片，1对应AttributeValue1，以此类推
        /// </summary>
        public int ImgAttributeValueNo { get; set; }

        /// <summary>
        /// 属性名称1
        /// </summary>
        public string AttributeName1 { get; set; }

        /// <summary>
        /// 属性名称2，属性1不为空值时，属性2才可能有值
        /// </summary>
        public string AttributeName2 { get; set; }

        /// <summary>
        /// 属性名称3，属性2不为空值时，属性3才可能有值
        /// </summary>
        public string AttributeName3 { get; set; }

        /// <summary>
        /// 属性值1
        /// </summary>
        public string AttributeValue1 { get; set; }

        /// <summary>
        /// 属性值2，属性1不为空值时，属性2才可能有值
        /// </summary>
        public string AttributeValue2 { get; set; }

        /// <summary>
        /// 属性值3，属性2不为空值时，属性3才可能有值
        /// </summary>
        public string AttributeValue3 { get; set; }

        public string ValueUrl { get; set; }
        public string ValueUrlKey { get; set; }

        public string AttributeCode1 { get; set; }

        public string AttributeCode2 { get; set; }

        public string AttributeCode3 { get; set; }

        /// <summary>
        /// 将类型转为Json字符串
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static string FromClass(BaseProductSkuAttributeModel model)
        {
            var attributes = new List<Dictionary<string, string>>();
            if (model == null) return attributes.ToJson();

            var attribute = new Dictionary<string, string>
            {
                { "k", model.AttributeName1 },
                { "v", model.AttributeValue1 }
            };
            attributes.Add(attribute);

            if (!string.IsNullOrEmpty(model.AttributeName2))
            {
                attribute = new Dictionary<string, string>
                {
                    { "k", model.AttributeName2 },
                    { "v", model.AttributeValue2 }
                };
                attributes.Add(attribute);
            }

            if (!string.IsNullOrEmpty(model.AttributeName3))
            {
                attribute = new Dictionary<string, string>
                {
                    { "k", model.AttributeName3 },
                    { "v", model.AttributeValue3 }
                };
                attributes.Add(attribute);
            }

            return attributes.ToJson();
        }
    }

}
