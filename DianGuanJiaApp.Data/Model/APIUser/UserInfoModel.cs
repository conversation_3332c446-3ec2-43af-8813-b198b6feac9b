using System.Collections.Generic;
using DianGuanJiaApp.Data.Entity;

namespace DianGuanJiaApp.Data.Model.APIUser
{
    /// <summary>
    /// 
    /// </summary>
    public class UserInfoModel
    {
        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int FxUserId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MainMobile { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string RegisteredDays { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Mobile { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string FxUserNickName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsShowCrossBorder { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string UserFlag { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsWhiteUser { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsUseColdDb { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CurrentShopId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PageVersionClassName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TraceId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public FxUserAddress FxUserAddres { get; set; }

        public dynamic SiteContextCurrent { get; set; }
        public dynamic CurrentLoginShopModel { get; set; }
        public string NickName { get; set; }
        public string ShopName { get; set; }
        public List<FenDanSystemNav> FenDanSystemNavs { get; set; }
        public Dictionary<string, bool> TopBtnPermDict { get; set; }
        public bool IsShow1688Menu { get; set; }
        public bool HasModifyRemarkApi { get; set; }
        public bool IsTouTiaoXi { get; set; }
        public string Token { get; set; }
        public bool IsShowSiteMessage { get; set; }
        public string AllShopsModel { get; set; }
        public List<FenDanSystemNav> NavQuickEntry { get; set; }
        public List<string> NavQuickEntrySort { get; set; }
        public int ShopId { get; set; }

        /// <summary>
        /// 是否即时零售商家
        /// </summary>
        public bool IsSaleShopAgent { get; set; }
    }
}