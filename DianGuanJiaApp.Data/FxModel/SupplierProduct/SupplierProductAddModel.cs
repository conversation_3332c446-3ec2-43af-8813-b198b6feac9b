using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using DianGuanJiaApp.Data.Model.BaseProduct;

namespace DianGuanJiaApp.Data.FxModel.SupplierProduct
{
    /// <summary>
    /// 货盘商品添加模型
    /// </summary>
    public class SupplierProductAddModel
    {
        /// <summary>
        /// 类目信息列表
        /// </summary>
        public List<CategoryInfo> CategoryInfoList { get; set; }
        
        /// <summary>
        /// 类目属性，JSON格式
        /// </summary>
        public string CategoryAttribute { get; set; }
        
        /// <summary>
        /// SPU商品标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 默认空字符，简称
        /// </summary>
        public string ShortTitle { get; set; } = string.Empty;

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<string> ProductImagesStr { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        public List<string> DescriptionStr { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<BaseProductImageModel> ProductImages { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        public List<BaseProductImageModel> Description { get; set; }

        /// <summary>
        /// 商品SKU
        /// </summary>
        public List<SupplierProductAddSkuModel> ProductSkus { get; set; } 
        
        /// <summary>
        /// 发货信息
        /// </summary>
        public ShipmentsInfo ShipmentsInfo { get; set; }
        
        /// <summary>
        /// 售后信息
        /// </summary>
        public AfterSalesInfo AfterSalesInfo { get; set; }

        /// <summary>
        /// 平台规格模式=0（默认）、自定义规格模式=1
        /// </summary>
        public int SkuModeType { get; set; }
        
        /// <summary>
        /// 商品类型；为空表示小站商品；"SaleShop" 表示即时零售
        /// </summary>
        public string ProductType { get; set; }
        
        /// <summary>
        /// 是否使用名片中的发货信息。true：忽略前端传入的发货仓库信息
        /// </summary>
        public bool? IsUseCardInfo { get; set; }
    }

    /// <summary>
    /// Sku添加模型
    /// </summary>
    public class SupplierProductAddSkuModel
    {
        /// <summary>
        /// 默认空字符串，JSON格式，格式如下
        /// 该字段为冗余字段-可避免关联表查询
        /// [{"k":"颜色","v":"白色"},{"k":"尺寸","v":"XL"},{ "k":"材质","v":"棉质"}]
        /// k：对应BaseProductSkuAttribute.AttributeName1/2/3
        /// v：对应BaseProductSkuAttribute.AttributeValue1/2/3
        /// </summary>
        public BaseProductSkuAttributeModel Attribute { get; set; } 
        
        /// <summary>
        /// 图片地址
        /// </summary>
        public string ImageUrlStr { get; set; }
        
        /// <summary>
        /// 规格编码，由用户填写
        /// </summary>
        public string SkuCode { get; set; }
        
        /// <summary>
        /// 默认分销价，对下游商家设置生效
        /// </summary>
        public decimal? DistributePrice { get; set; } 
        
        /// <summary>
        /// 默认0，重量，单位克
        /// </summary>
        public int? Weight { get; set; }

        /// <summary>
        /// 如果规格是以自定义规格模式进行保存，则这个值表示自定义规格名称
        /// </summary>
        public string AttributeValue { get; set; }
    }

    /// <summary>
    /// 类目信息
    /// </summary>
    public class CategoryInfo
    {
        /// <summary>
        /// 来源类目接口CateId
        /// </summary>
        public string CateId { get; set; }

        /// <summary>
        /// 来源类目接口ParentId
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 来源类目接口Level
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 类目名称
        /// </summary>
        public string Name { get; set; }
    }

    /// <summary>
    /// 货盘商品图片模型
    /// </summary>
    public class SupplierProductImgModel
    {
        /// <summary>
        /// 图片对象Id
        /// </summary>
        public int ImageObjectId { get; set; }

        /// <summary>
        /// 图片地址
        /// </summary>
        public string FullUrl { get; set; }

        /// <summary>
        /// 是否主图
        /// </summary>
        public bool IsMain { get; set; }
    }
}
