using Dapper;
using DianGuanJiaApp.Data.Model.BaseProduct;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    public partial class BaseProductEntity
    {
        /// <summary>
        /// 基础商品规格
        /// </summary>
        [NotMapped]
        public List<BaseProductSku> Skus { get; set; }

        /// <summary>
        /// 基础商品图片
        /// </summary>
        [NotMapped]
        public List<BaseProductImage> Images { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        [NotMapped]
        public List<BaseProductImage> Descriptions { get; set; }

        /// <summary>
        /// 属性种类
        /// </summary>
        [NotMapped]
        public List<AttributeTypeModel> AttributeTypes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public string UidStr => Uid.ToString();

        /// <summary>
        /// 默认0，主图的对象ID
        /// </summary>
        [NotMapped]
        public string MainImageUrl { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        [NotMapped]
        public long? StockCount { get; set; }
        /// <summary>
        /// 关联数量
        /// </summary>
        [NotMapped]
        public int? RelationCount { get; set; }

        /// <summary>
        /// 是否是多厂家 : true 多厂家 false 未设置
        /// </summary>
        [NotMapped]
        public bool? IsMultipleSupplierUser { get; set; }

        /// <summary>
        /// 成本价（采购价）范围
        /// </summary>
        [NotMapped]
        public string CostPrice { get; set; }

        /// <summary>
        /// 默认结算价范围
        /// </summary>
        [NotMapped]
        public string SettlePrice { get; set; }

        /// <summary>
        /// 默认分销价范围
        /// </summary>
        [NotMapped]
        public string DistributePrice { get; set; }

        /// <summary>
        /// 规格数量
        /// </summary>
        [NotMapped]
        public decimal? SkuCount { get; set; }

        /// <summary>
        /// 基础商品图片
        /// </summary>
        [NotMapped]
        public List<string> ImagesStr { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        [NotMapped]
        public List<string> DescriptionsStr { get; set; }

        /// <summary>
        /// 基础商品图片
        /// </summary>
        [NotMapped]
        public List<string> ImagesKeyStr { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，JSON格式
        /// </summary>
        [NotMapped]
        public List<string> DescriptionsKeyStr { get; set; }

        /// <summary>
        /// 展示更多
        /// </summary>
        [NotMapped]
        public bool IsMore { get; set; }

        /// <summary>
        /// 供货方式 0: 未设置， 1：自营、2：多厂家、3:自营+厂家供货、4：自营+多厂家
        /// </summary>
        [NotMapped]
        public int? SupplyMethod { get; set; }

        /// <summary>
        /// 厂家
        /// </summary>
        [NotMapped]
        public string SupplierName { get; set; }

        /// <summary>
        /// 厂家Id
        /// </summary>
        [NotMapped]
        public int? ProductSupplierUserId { get; set; }

        /// <summary>
        /// 资料标识
        /// </summary>
        [NotMapped]
        public List<PlatformInfo> PlatformInfoTags { get; set; }

        /// <summary>
        /// 平台资料
        /// </summary>
        [NotMapped]
        public List<PtProductInfo> PtProductInfos { get; set; }

        /// <summary>
        /// 铺货源商品编码
        /// </summary>
        [NotMapped]
        public string SourceSpuCode { get; set; }

        /// <summary>
        /// 是否组合货品，0=非组合货品；1=组合货品
        /// </summary>
        [NotMapped]
        public int? IsCombination { get; set; }

        /// <summary>
        /// 复制时上游的基础商品Uid
        /// </summary>
        [NotMapped]
        public string UpBaseProductUid { get; set; }
        
        /// <summary>
        /// uid存储的Sku字典
        /// </summary>
        [NotMapped]
        public Dictionary<long, BaseProductSku> SkuDic { get; set; } 
        
        /// <summary>
        /// 基础商品图片
        /// </summary>
        [NotMapped]
        public BaseProductImage MainImg { get; set; }
        
        /// <summary>
        /// 转为即时零售货源商品类型
        /// </summary>
        [NotMapped]
        public string ProductType { get; set; }
    }
}
