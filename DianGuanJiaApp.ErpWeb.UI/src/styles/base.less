@import "./unit.less";

body {
    font-family: <PERSON> YaHei, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif, WenQuanYi Micro Hei;
    background-color: #edf0f4;
}
.ant-layout{
    background-color: #edf0f4;  
    font-family: Microsoft YaHei, <PERSON><PERSON>, <PERSON>m<PERSON>un, sans-serif, WenQuanYi Micro Hei;

}

* {
    padding: 0;
    margin: 0;
}

ul,
ol {
    list-style: none;
    margin: 0;
    padding: 0;
}

i,
s,
em {
    font-style: normal;
    text-decoration: none;
}

.wu-weight400 {
    font-weight: 400;
}

.wu-weight500 {
    font-weight: 500;
}

.wu-weight600 {
    font-weight: 600;
}

.wu-weight700 {
    font-weight: 700;
}

.wu-lineH16 {
    line-height: 16px;
}

.wu-lineH20 {
    line-height: 20px;
}

.wu-lineH24 {
    line-height: 24px;
}

.wu-lineH28 {
    line-height: 28px;
}

.wu-lineH32 {
    line-height: 32px;
}

.wu-color-c9 {
    color: @c09;
}

.wu-color-c6 {
    color: @c06;
}

.wu-color-c4 {
    color: @c04;
}

.wu-color-c3 {
    color: @c03;
}

.wu-color-c2 {
    color: @c02;
}

.wu-color-c1 {
    color: @c01;
}

.wu-color-a {
    color: @AColor;
}

.wu-color-ab {
    color: @ABColor;
}

.wu-color-a8 {
    color: @A8Color;
}

.wu-color-a3 {
    color: @A3Color;
}

.wu-color-a2 {
    color: @A2Color;
}

.wu-color-a1 {
    color: @A1Color;
}

.wu-2radius {
    border-radius: @2radius;
}

.wu-4radius {
    border-radius: @4radius;
}

.wu-6radius {
    border-radius: @6radius;
}

.wu-8radius {
    border-radius: @8radius;
}

.wu-c09 {
    color: @c09;
}

.wu-c06 {
    color: @c06;
}

.wu-c04 {
    color: @c04;
}

.wu-c03 {
    color: @c03;
}

.wu-f36 {
    font-size: @f36;
}

.wu-f24 {
    font-size: @f24;
}

.wu-f20 {
    font-size: @f20;
}

.wu-f18 {
    font-size: @f18;
}

.wu-f16 {
    font-size: @f16;
}

.wu-f14 {
    font-size: @f14;
}

.wu-f12 {
    font-size: @f12;
}

.wu-border {
    border: 1px solid @c02;
}

.wu-borderL {
    border-left: 1px solid @c02;
}

.wu-borderR {
    border-right: 1px solid @c02;
}

.wu-borderT {
    border-top: 1px solid @c02;
}

.wu-borderB {
    border-bottom: 1px solid @c02;
}

.wu-p2 {
    padding: 2px !important;
}

.wu-pL2 {
    padding-left: 2px !important;
}

.wu-pR2 {
    padding-right: 2px !important;
}

.wu-pT2 {
    padding-top: 2px !important;
}

.wu-pB2 {
    padding-bottom: 2px !important;
}

.wu-p4 {
    padding: 4px !important;
}

.wu-pL4 {
    padding-left: 4px !important;
}

.wu-pR4 {
    padding-right: 4px !important;
}

.wu-pT4 {
    padding-top: 4px !important;
}

.wu-pB4 {
    padding-bottom: 4px !important;
}

.wu-p8 {
    padding: 8px !important;
}

.wu-pL8 {
    padding-left: 8px !important;
}

.wu-pR8 {
    padding-right: 8px !important;
}

.wu-pT8 {
    padding-top: 8px !important;
}

.wu-pB8 {
    padding-bottom: 8px !important;
}

.wu-p12 {
    padding: 12px !important;
}

.wu-pL12 {
    padding-left: 12px !important;
}

.wu-pR12 {
    padding-right: 12px !important;
}

.wu-pT12 {
    padding-top: 12px !important;
}

.wu-pB12 {
    padding-bottom: 12px !important;
}

.wu-p16 {
    padding: 16px !important;
}

.wu-pL16 {
    padding-left: 16px !important;
}

.wu-pR16 {
    padding-right: 16px !important;
}

.wu-pT16 {
    padding-top: 16px !important;
}

.wu-pB16 {
    padding-bottom: 16px !important;
}

.wu-p20 {
    padding: 20px !important;
}

.wu-pL20 {
    padding-left: 20px !important;
}

.wu-pR20 {
    padding-right: 20px !important;
}

.wu-pT20 {
    padding-top: 20px !important;
}

.wu-pB20 {
    padding-bottom: 20px !important;
}

.wu-p24 {
    padding: 24px !important;
}

.wu-pL24 {
    padding-left: 24px !important;
}

.wu-pR24 {
    padding-right: 24px !important;
}

.wu-pT24 {
    padding-top: 24px !important;
}

.wu-pB24 {
    padding-bottom: 24px !important;
}

.wu-p36 {
    padding: 36px !important;
}

.wu-pL36 {
    padding-left: 36px !important;
}

.wu-pR36 {
    padding-right: 36px !important;
}

.wu-pT36 {
    padding-top: 36px !important;
}

.wu-pB36 {
    padding-bottom: 36px !important;
}

.wu-p48 {
    padding: 48px !important;
}

.wu-pL48 {
    padding-left: 48px !important;
}

.wu-pR48 {
    padding-right: 48px !important;
}

.wu-pT48 {
    padding-top: 48px !important;
}

.wu-pB48 {
    padding-bottom: 48px !important;
}


.wu-p0 {
    padding: 0 !important;
}

.wu-pL0 {
    padding-left: 0 !important;
}

.wu-pR0 {
    padding-right: 0 !important;
}

.wu-pT0 {
    padding-top: 0 !important;
}

.wu-pB0 {
    padding-bottom: 0 !important;
}


.wu-m2 {
    margin: 2px !important;
}

.wu-mL2 {
    margin-left: 2px !important;
}

.wu-mR2 {
    margin-right: 2px !important;
}

.wu-mT2 {
    margin-top: 2px !important;
}

.wu-mB2 {
    margin-bottom: 2px !important;
}

.wu-m4 {
    margin: 4px !important;
}

.wu-mL4 {
    margin-left: 4px !important;
}

.wu-mR4 {
    margin-right: 4px !important;
}

.wu-mT4 {
    margin-top: 4px !important;
}

.wu-mB4 {
    margin-bottom: 4px !important;
}

.wu-m8 {
    margin: 8px !important;
}

.wu-mL8 {
    margin-left: 8px !important;
}

.wu-mR8 {
    margin-right: 8px !important;
}

.wu-mB8 {
    margin-bottom: 8px !important;
}

.wu-mT8 {
    margin-top: 8px !important;
}

.wu-m12 {
    margin: 12px !important;
}

.wu-mL12 {
    margin-left: 12px !important;
}

.wu-mR12 {
    margin-right: 12px !important;
}

.wu-mB12 {
    margin-bottom: 12px !important;
}

.wu-mT12 {
    margin-top: 12px !important;
}

.wu-m16 {
    margin: 16px !important;
}

.wu-mL16 {
    margin-left: 16px !important;
}

.wu-mR16 {
    margin-right: 16px !important;
}

.wu-mB16 {
    margin-bottom: 16px !important;
}

.wu-mT16 {
    margin-top: 16px !important;
}

.wu-m20 {
    margin: 20px !important;
}

.wu-mL20 {
    margin-left: 20px !important;
}

.wu-mR20 {
    margin-right: 20px !important;
}

.wu-mB20 {
    margin-bottom: 20px !important;
}

.wu-mT20 {
    margin-top: 20px !important;
}

.wu-m24 {
    margin: 24px !important;
}

.wu-mL24 {
    margin-left: 24px !important;
}

.wu-mR24 {
    margin-right: 24px !important;
}

.wu-mB24 {
    margin-bottom: 24px !important;
}

.wu-mT24 {
    margin-top: 24px !important;
}

.wu-m36 {
    margin: 36px !important;
}

.wu-mL36 {
    margin-left: 36px !important;
}

.wu-mR36 {
    margin-right: 36px !important;
}

.wu-mT36 {
    margin-top: 36px !important;
}

.wu-mB36 {
    margin-bottom: 36px !important;
}

.wu-m48 {
    margin: 48px !important;
}

.wu-mL48 {
    margin-left: 48px !important;
}

.wu-mR48 {
    margin-right: 48px !important;
}

.wu-mT48 {
    margin-top: 48px !important;
}

.wu-mB48 {
    margin-bottom: 48px !important;
}

.wu-m0 {
    margin: 0 !important;
}

.wu-mL0 {
    margin-left: 0 !important;
}

.wu-mR0 {
    margin-right: 0 !important;
}

.wu-mT0 {
    margin-top: 0 !important;
}

.wu-mB0 {
    margin-bottom: 0 !important;
}

ul,
ol {
    list-style: none;
    margin: 0;
    padding: 0;
}



.wu-color-e {
    color: @EColor;

    &.hover {
        color: @E8Color;
    }

    &.active {
        color: @EBColor;
    }

    &.disabled {
        color: @E3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @E8Color;
        }

        &:active {
            color: @EBColor;
        }
    }

    &.wu-border {
        border: 1px solid @E2Color;
    }

    &.wu-background {
        background-color: @E1Color;
    }
}

.wu-color-m {
    color: @c06;

    &.hover {
        color: @c04;
    }

    &.active {
        color: @c09;
    }

    &.disabled {
        color: @c03;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @c04;
        }

        &:active {
            color: @c09;
        }
    }

    &.wu-border {
        border: 1px solid @c02;
    }

    &.wu-background {
        background-color: @c01;
    }
}

.wu-color-a {
    color: @AColor;

    &.hover {
        color: @A8Color;
    }

    &.active {
        color: @ABColor;
    }

    &.disabled {
        color: @A3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @A8Color;
        }

        &:active {
            color: @ABColor;
        }

        &.disabled {
            color: @A3Color;
            cursor: not-allowed;
        }
    }

    &.wu-border {
        border: 1px solid @A2Color;
    }

    &.wu-background {
        background-color: @A1Color;
    }
}

.wu-color-b {
    color: @BColor;

    &.hover {
        color: @B8Color;
    }

    &.active {
        color: @BBColor;
    }

    &.disabled {
        color: @B3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @B8Color;
        }

        &:active {
            color: @BBColor;
        }

        &.disabled {
            color: @B3Color;
            cursor: not-allowed;
        }
    }

    &.wu-border {
        border: 1px solid @B2Color;
    }

    &.wu-background {
        background-color: @B1Color;
    }
}

.wu-color-c {
    color: @CColor;

    &.hover {
        color: @C8Color;
    }

    &.active {
        color: @CBColor;
    }

    &.disabled {
        color: @C3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @C8Color;
        }

        &:active {
            color: @CBColor;
        }

        &.disabled {
            color: @C3Color;
            cursor: not-allowed;
        }
    }

    &.wu-border {
        border: 1px solid @C2Color;
    }

    &.wu-background {
        background-color: @C1Color;
    }
}

.wu-color-d {
    color: @DColor;

    &.hover {
        color: @D8Color;
    }

    &.active {
        color: @DBColor;
    }

    &.disabled {
        color: @D3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @D8Color;
        }

        &:active {
            color: @DBColor;
        }

        &.disabled {
            color: @D3Color;
            cursor: not-allowed;
        }
    }

    &.wu-border {
        border: 1px solid @D2Color;
    }

    &.wu-background {
        background-color: @D1Color;
    }
}

.wu-color-f {
    color: @FColor;

    &.hover {
        color: @F8Color;
    }

    &.active {
        color: @FBColor;
    }

    &.disabled {
        color: @F3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @F8Color;
        }

        &:active {
            color: @FBColor;
        }
    }

    &.wu-border {
        border: 1px solid @F2Color;
    }

    &.wu-background {
        background-color: @F1Color;
    }
}

.wu-color-g {
    color: @GColor;

    &.hover {
        color: @G8Color;
    }

    &.active {
        color: @GBColor;
    }

    &.disabled {
        color: @G3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @G8Color;
        }

        &:active {
            color: @GBColor;
        }
    }

    &.wu-border {
        border: 1px solid @G2Color;
    }

    &.wu-background {
        background-color: @G1Color;
    }
}

.wu-color-j {
    color: @JColor;

    &.hover {
        color: @J8Color;
    }

    &.active {
        color: @JBColor;
    }

    &.disabled {
        color: @J3Color;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @J8Color;
        }

        &:active {
            color: @JBColor;
        }
    }

    &.wu-border {
        border: 1px solid @J2Color;
    }

    &.wu-background {
        background-color: @J1Color;
    }
}

.wu-color-n {
    color: @fff07;

    &.hover {
        color: @fff05;
    }

    &.active {
        color: @fff09;
    }

    &.disabled {
        color: @fff03;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @fff05;
        }

        &:active {
            color: @fff09;
        }
    }

    &.wu-border {
        border: 1px solid @fff02;
    }

    &.wu-background {
        background-color: @fff;
    }
}

.wu-color-k {
    color: @c04;

    &.hover {
        color: @AColor;
    }

    &.active {
        color: @ABColor;
    }

    &.disabled {
        color: @c03;
        cursor: not-allowed;
    }

    &.wu-operate {
        cursor: pointer;

        &:hover {
            color: @AColor;
        }

        &:active {
            color: @ABColor;
        }

        &.disabled {
            color: @c03;
            cursor: not-allowed;
        }
    }

    &.wu-border {
        border: 1px solid @D2Color;
    }

    &.wu-background {
        background-color: @D1Color;
    }
}

.wu-hide {
    display: none !important;
}

.wu-hover {
    cursor: pointer;
}

.wu-flex {
    display: flex;

    &.wu-xCenter {
        justify-content: center;
    }

    &.wu-yCenter {
        align-items: center;
    }

    &.wu-center {
        align-items: center;
        justify-content: center;
    }

    &.wu-betteen {
        justify-content: space-between;
    }

    &.wu-left {
        justify-content: flex-start;
    }

    &.wu-right {
        justify-content: flex-end;
    }

    &.wu-column {
        flex-direction: column;

        &.wu-xCenter {
            align-items: center;
        }

        &.wu-yCenter {
            justify-content: center;
        }

        &.wu-betteen {
            align-items: space-between;
        }

        &.wu-left {
            align-items: flex-start;
        }

        &.wu-right {
            align-items: flex-end;
        }
    }
}

.wu-yCenter {
    align-items: center;
}

.wu-center {
    align-items: center;
    justify-content: center;
}

.wu-betteen {
    justify-content: space-between;
}

.wu-endA {
    align-items: flex-end;
}

.wu-endB {
    justify-content: flex-end;
}

.wu-flex-1 {
    flex: 1;
}

.wu-flex-2 {
    flex: 2;
}

.wu-flex-3 {
    flex: 3;
}

.wu-radius-4 {
    border-radius: 4px;
}

.wu-radius-BL-4 {
    border-bottom-left-radius: 4px;
}

.wu-radius-BR-4 {
    border-bottom-right-radius: 4px;
}

.wu-radius-TR-4 {
    border-top-right-radius: 4px;
}

.wu-radius-TL-4 {
    border-top-left-radius: 4px;
}

.wu-radius-6 {
    border-radius: 6px;
}

.wu-radius-BL-6 {
    border-bottom-left-radius: 6px;
}

.wu-radius-BR-6 {
    border-bottom-right-radius: 6px;
}

.wu-radius-TR-6 {
    border-top-right-radius: 6px;
}

.wu-radius-TL-6 {
    border-top-left-radius: 6px;
}

.wu-radius-8 {
    border-radius: 8px;
}

.wu-radius-BL-8 {
    border-bottom-left-radius: 8px;
}

.wu-radius-BR-8 {
    border-bottom-right-radius: 8px;
}

.wu-radius-TR-8 {
    border-top-right-radius: 8px;
}

.wu-radius-TL-8 {
    border-top-left-radius: 8px;
}

.wu-bg-fff {
    background-color: #fff;
}

.wu-operateWrap {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    align-items: center;
}


.wu-radius2 {
    border-radius: 2px;
}

.wu-radius4 {
    border-radius: 4px;
}

.wu-radius6 {
    border-radius: 6px;
}

.wu-radius8 {
    border-radius: 8px;
}

.wu-background-white {
    background-color: #fff;
}

.wu-background-blue {
    background-color: @AColor;
}

.wu-background-red {
    background-color: @BColor;
}

.wu-background-yellow {
    background-color: @CColor;
}

.wu-background-green {
    background-color: @DColor;
}

.wu-width100 {
    width: 100%;
}

.wu-heigt100 {
    height: 100%;
}

.wu-fileWrap {}

.wu-file-status {
    display: inline-flex;
    color: @c09;
    font-size: 14px;

    .wu-file-status-title {
        max-width: 240px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .icon-a-delete1x {
        display: none;
    }

    &:hover {
        background-color: @c01;

        .icon-a-delete1x {
            display: inline-block;
        }
    }
}

.wu-filePicWrap {
    width: 80px;
    height: 80px;

    &.wu-mid {
        width: 56px;
        height: 56px;

        .wu-filePic-title {
            display: none;
        }
    }

    &.wu-small {
        width: 32px;
        height: 32px;

        .wu-filePic-title {
            display: none;
        }
    }

}

.wu-up-filePic {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.wu-filePic-conent {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 6px;
    /* 中性色/Gray1 */
    background: @c01;
    box-sizing: border-box;
    /* 中性色/Gray3 */
    border: 0.5px dashed rgba(0, 0, 0, 0.14);
    color: @c09;
    font-size: 12px;
    cursor: pointer;

    &:hover {
        color: @AColor;
        border: 0.5px dashed @AColor;
    }

    .wu-show-filePic {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        display: none;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .wu-file-operate {
        display: flex;
        justify-content: space-around;

        .wu-file-operate-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            &:hover {
                color: @AColor;
            }
        }
    }
}

.wu-tooltip {
    position: relative;
}


.fendanContent-main-module {
    background-color: #fff;
}

.compageWrap-module {
    display: flex;
    flex-direction: column;
    box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    position: sticky;
    bottom: 0;

    .compageWrap-wrap {
        display: flex;
        justify-content: space-between;
    }

    .wu-checkboxWrap {
        color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;

        .wu-my-checkboxWrap {
            margin-right: 8px;
        }

        .pL8 {
            padding-left: 8px;
        }

        .wu-my-checkboxNum {
            color: #0888FF;
            padding: 0 4px;
        }
    }

    .layui-myPage {
        .layui-box {
            margin: 12px 0 8px 0;
        }
    }
}


// 滚动条样式
.art-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    color: #dddddd;
}

.art-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.14);
}

.art-scrollbar::-webkit-scrollbar-track {
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.09);
}

.art-sticky-scroll {
    overflow: auto;
    position: sticky;
    bottom: 65px;

    .art-sticky-scroll-item {
        height: 1px;
        visibility: hidden;
    }
}



.wu-searchWrap.wu-searchWrap-container {
    display: grid;
    gap: 0 8px;
    background-color: #fff;

    .wu-searchWrap-contactTime {
        min-width: 160px;
    }

    .wu-searchWrap-item {
        min-width: 160px;
        padding-right: 0px;
        height: 32px;
    }

    &.wu-initSearchWrap {
        position: relative;
        display: flex;

        .wu-searchWrap-item {
            width: unset;
        }

        &::after {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-color: #fff;
            content: "";
            z-index: 1000;

        }
    }
}

.wu-table {

    &.stickyOperateTable {}

    .wu-header-Sticky-th {
        position: sticky;
        right: 0;
        background-color: @c05;
    }
}

.wu-stickyTable-wrap {
    border-radius: 6px;
    border: 1px solid @c02;
    width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;

    .stickyLast {
        thead tr{

            th:last-child {
                position: sticky;
                right: 0;
                background-color: #f5f5f5;
                &::after {
                    position: absolute;
                    content: "";
                    height: 100%;
                    left: 0;
                    display: block;
                    top: 0;
                    width: 8px;
                    opacity: 1;
                    left: -8px;
                    background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
                }
            }
        }
        tbody tr{
            td:last-child {
                position: sticky;
                right: 0;
                background-color: #fff;
                &::after {
                    position: absolute;
                    content: "";
                    height: 100%;
                    left: 0;
                    display: block;
                    top: 0;
                    width: 8px;
                    opacity: 1;
                    left: -8px;
                    background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
                }
            }
            &:hover{
                td:last-child{
                    background-color: #f5f5f5;
                } 
            }
        }

    }
}

.wu-table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    box-sizing: border-box;
    border-collapse: separate;

    thead tr th {
        border-right: none;
        // border-top: 1px solid @c02;
        border-bottom: 1px solid @c02;
        padding: 8px 12px;
        text-align: left;
        font-weight: 500;
        color: @c09;
        background-color: @c01;
        font-size: 14px;
        font-weight: 500;
        box-sizing: border-box;
    }

    tbody tr {
        td {
            border-right: none;
            border-bottom: 1px solid @c02;
            padding: 12px;
            box-sizing: border-box;
            font-size: 12px;
        }

        &:last-child {
            td {
                border-bottom: unset;
            }
        }

        &:hover {
            background-color: @c01;
            .wu-header-Sticky-td {
                background-color: #f5f5f5;
            }

        }

        &.wu-active {
            background-color: @A1Color;
            td:last-child {
                background-color: #ebf6ff!important;
            }
        }
    }

    .wu-header-Sticky-th {
        position: sticky;
        right: 0;
        background-color: #f5f5f5;
    }

    .wu-header-Sticky-td {
        position: sticky;
        right: 0;
        background-color: #fff;
    }
}

.wu-radius-4 {
    border-radius: 4px;
}

.wu-radius-BL-4 {
    border-bottom-left-radius: 4px;
}

.wu-radius-BR-4 {
    border-bottom-right-radius: 4px;
}

.wu-radius-TR-4 {
    border-top-right-radius: 4px;
}

.wu-radius-TL-4 {
    border-top-left-radius: 4px;
}

.wu-radius-6 {
    border-radius: 6px;
}

.wu-radius-BL-6 {
    border-bottom-left-radius: 6px;
}

.wu-radius-BR-6 {
    border-bottom-right-radius: 6px;
}

.wu-radius-TR-6 {
    border-top-right-radius: 6px;
}

.wu-radius-TL-6 {
    border-top-left-radius: 6px;
}

.wu-radius-8 {
    border-radius: 8px;
}

.wu-radius-BL-8 {
    border-bottom-left-radius: 8px;
}

.wu-radius-BR-8 {
    border-bottom-right-radius: 8px;
}

.wu-radius-TR-8 {
    border-top-right-radius: 8px;
}

.wu-radius-TL-8 {
    border-top-left-radius: 8px;
}

.wu-footer-operation {
    flex-direction: column;
    box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    position: sticky;
    bottom: 0;
    display: flex;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    .wu-operation-wrap{
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
    }
}
.wu-breadcrumb{
    margin: 24px 16px 8px 16px;
    display: flex;
    justify-content: space-between;
    .ant-breadcrumb{
        font-size: 20px;
    }
}


.wu-searchWrap {
    display: flex;
    padding: 16px 16px 8px 16px;
    box-sizing: border-box;
    width: 100%;
    flex-wrap: wrap;
    background-color: unset;

    > * {
        margin-bottom: 8px;
    }

    .wu-searchWrap-item {
        min-width: 226px;
        width: 20%;
        padding-right: 8px;
        box-sizing: border-box;

    }
}
.wu-badge {
    display: inline-flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.9);
    font-size: 12px;

    .wu-badge-dot {
        width: 6px;
        height: 6px;
        display: inline-block;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.3);
        margin-right: 4px;
    }

    &.wu-processing {
        .wu-badge-dot {
            background-color: @AColor;
        }
    }

    &.wu-error {
        .wu-badge-gan {
            font-weight: 700;
            color: @BColor;
            font-size: 14px;
        }

        .wu-badge-dot {
            background-color: @BColor;
        }
    }

    &.wu-warning {
        .wu-badge-dot {
            background-color: @CColor;
        }
    }

    &.wu-success {
        .wu-badge-dot {
            background-color: @DColor;
        }
    }
}
.wu-tag {
    padding: 0 2px;
    line-height: 16px;
    border-radius: @2radius;
    background: #f5f5f5;
    font-size: 12px;
    font-weight: normal;
    color: @c09;
    display: inline-flex;
    align-items: center;
    text-align: center;
    box-sizing: border-box;
    border-radius: @2radius;

    .iconfont {
        font-size: 14px;
    }

    &.wu-dot {
        padding: 0 1px;
    }

    &.wu-default {
        &.wu-dot {
            border: 0.5px solid rgba(66, 74, 87, 0.2);
        }
    }

    &.wu-default-dev {
        background-color: @c09;
        color: #fff;

        &.wu-dot {
            background-color: #fff;
            color: @c09;
            border: 0.5px solid rgba(66, 74, 87, 0.2);
        }
    }


    &.wu-processing {
        background-color: @A1Color;
        color: @AColor;

        &.wu-dot {
            border: 0.5px solid @A2Color;
        }
    }

    &.wu-processing-dev {
        background-color: @AColor;
        color: #fff;

        &.wu-dot {
            border: 0.5px solid @A2Color;
            background-color: #fff;
            color: @AColor;
        }
    }

    &.wu-error {
        background-color: @B1Color;
        color: @BColor;

        .wu-badge-gan {
            font-size: 700;
            color: @B2Color;
        }

        &.wu-dot {
            border: 0.5px solid @B2Color;
        }
    }

    &.wu-error-dev {
        background-color: @BColor;
        color: #fff;

        &.wu-dot {
            border: 0.5px solid @B2Color;
            background-color: #fff;
            color: @BColor;
        }
    }

    &.wu-warning {
        background-color: @C1Color;
        color: @CColor;

        &.wu-dot {
            border: 0.5px solid @C2Color;
        }
    }

    &.wu-warning-dev {
        background-color: @CColor;
        color: #fff;

        &.wu-dot {
            border: 0.5px solid @C2Color;
            background-color: #fff;
            color: @CColor;
        }
    }

    &.wu-success {
        background-color: @D1Color;
        color: @DColor;

        &.wu-dot {
            border: 0.5px solid @D2Color;
        }
    }

    &.wu-success-dev {
        background-color: @DColor;
        color: #fff;

        &.wu-dot {
            border: 0.5px solid @D2Color;
            background-color: #fff;
            color: @DColor;
        }
    }
}
.wu-alert {
    padding: 8px 12px;
    border-radius: @radius;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    position: relative;
    box-sizing: border-box;

    .iconfont {
        display: inline-block;
        margin-right: 4px;
        position: relative;
        top: 2px;
    }

    .wu-alert-title {
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.9);
    }

    &.wu-processing {
        background-color: @A1Color;
        color: @AColor;
        border: 0.5px solid @A2Color;
    }

    &.wu-error {
        background-color: @B1Color;
        color: @BColor;
        border: 0.5px solid @B2Color;
    }

    &.wu-warning {
        background-color: @C1Color;
        color: @CColor;
        border: 0.5px solid @C2Color;
    }

    &.wu-success {
        background-color: @D1Color;
        color: @DColor;
        border: 0.5px solid @D2Color;
    }

    .icon-a-close1x {
        position: absolute;
        right: 10px;
        top: 10px;
        color: rgba(0, 0, 0, 0.6);
        cursor: pointer;
    }
}
.ant-empty-image{
    text-align: center;
    height: unset!important;
    margin-bottom: 0!important;
    .tableNoDataShow-img {
        width: 100px;
        height: 100px;
        display: inline-block;
        content: "";
        background-size: 800px 300px;
        background-position: -200px -200px;
      }

}
