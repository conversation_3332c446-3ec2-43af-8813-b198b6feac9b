var orderFroms = [{
    FieldType: "input", //input输入框
    Label: "输入框",
    Name: "input_01",
    Value: "",
    Placeholder: "请输入",
    ClassName: "antt-input",
    Sort: 1,
    Id: 0,
    Options: [],
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:1,//点多少格
    Display: true,
},
{
    FieldType: "singleCheck", //input输入框
    Label: "",
    Name: "singleCheck_01",
    Value: "",
    Placeholder: "请选择",
    ClassName: "antt-input",
    Sort: 1,
    Id: 1,
    Options: [
        { value: '0', label: 'Jack' },
        { value: '1', label: 'Lucy' },
        { value: '2', label: 'yiminghe' },
    ],
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:1,//点多少格
    Display: true,
},
{
    FieldType: "checkbox", //input输入框
    Label: "选择店铺",
    Name: "checkbox_01",
    Value: [],
    Placeholder: "选择店铺",
    ClassName: "antt-checkbox",
    Sort: 2,
    Id: 2,
    Options: [
        { value: '0', label: '店铺A' },
        { value: '1', label: '店铺B' },
        { value: '2', label: '店铺C' },
    ],
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:1,//点多少格
    Display: true,
},
{
    FieldType: "cascader", //级联选择
    Label: "选择店铺",
    Name: "checkbox_01",
    Value: [],
    Placeholder: "选择店铺",
    ClassName: "antt-cascader",
    Sort: 2,
    Id: 3,
    Options: [
        {
            value: '0', label: '店铺A',
            children: [
                {
                    label: 'Toy Fish',
                    value: 'fish',
                },
                {
                    label: 'Toy Cards',
                    value: 'cards',
                },
                {
                    label: 'Toy Bird',
                    value: 'bird',
                },
            ],
        },
        { value: '1', label: '店铺B' },
        { value: '2', label: '店铺C' },
    ],
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:1,//点多少格
    Display: true,
},
{
    FieldType: "measure", //组合
    Label: "",
    Name: "measure_01",
    Value: "",
    Placeholder: "请输入",
    ClassName: "antt-input",
    Sort: 13,
    Id: 3,
    Options: [],
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:2,//点多少格
    Display: true,
    MeasureTemplates: [
        {
            FieldType: "input", //input输入框
            Label: "",
            Name: "measure_01_input",
            Value: "",
            Placeholder: "子请输入",
            ClassName: "antt-input",
            Options: [],
            Width: "250px",
            Height: "",
            Display: true,

        },
        {
            FieldType: "checkbox", //input输入框
            Label: "选择店铺",
            Name: "checkbox_01",
            Value: [],
            Placeholder: "选择店铺accccc",
            ClassName: "antt-checkbox",
            Sort: 2,
            Id: 2,
            Options: [
                { value: '0', label: '店铺A' },
                { value: '1', label: '店铺B' },
                { value: '2', label: '店铺C' },
            ],
            Ext1: null,
            IsRequired: false, //是否为必填
            Width: "",
            Height: "",
            Display: true,
        }
    ]
},
{
    FieldType: "date", //时间选择
    Label: "",
    Name: "date_01",
    Value: '2025-03-24 00:00:00',
    Placeholder: "",
    ClassName: "antt-date",
    Sort: 4,
    Id: 4,
    Options: null,
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:1,//点多少格
    Display: true,
},
{
    FieldType: "measure", //组合
    Label: "",
    Name: "measure_02",
    Value: "",
    Placeholder: "",
    ClassName: "antt-date-measure",
    Sort: 6,
    Id: 6,
    Options: [],
    Ext1: null,
    IsRequired: false, //是否为必填
    Width: "",
    Height: "",
    cell:2,//点多少格
    Display: true,
    MeasureTemplates: [
        {
            FieldType: "singleCheck", //input输入框
            Label: "",
            Name: "measure_01_singleCheck",
            Value: "",
            Placeholder: "订单状态",
            ClassName: "antt-input",
            Options: [
                { value: '0', label: '下单时间' },
                { value: '1', label: '付款时间' },
                { value: '2', label: '发货时间' },
            ],
            Width: "150px",
            Height: "",
            Display: true,

        },
        {
            FieldType: "dateRange", //时间段
            Label: "",
            Name: "dateRange",
            Value: ["2025-03-24 00:00:00", "2025-12-30 00:00:00"],
            FormValue: [],
            Placeholder: "",
            ClassName: "antt-date",
            Sort: 5,
            Id: 5,
            Options: null,
            Ext1: null,
            IsRequired: false, //是否为必填
            Width: "",
            Height: "",
            Display: true,
        }
    ]
}
]

export default orderFroms;