.wu-container.GeneralizeIndex {
background-color: unset;
    margin-top: 0;
}
.wu-container {
    box-sizing: border-box;
    min-width: 1250px;
    
    .card {
        background-color: #fff; 
        .card-header {
            display: flex;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.09);
            .title {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.9);
                font-weight: 600;
            }
        }
    }
    .wu-warp-left {
        display: flex;
        flex-direction: row;
        flex: 3;
        flex-direction: column;
        min-width: calc(100% - 366px);
        .guidanceWrap {
            flex: 1;
            
            .guidanceWrap-left {
                flex: 5;
                margin-right: 16px;
                background-color: #fff;
                .guidanceWarp-content {
                    padding: 24px 16px;
                    .guidanceWarp-content-item {
                        margin-right: 32px;
                        .guidanceWarp-content-item-left {
                            margin-right: 4px;
                            .step-icon {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 20px;
                                height: 20px;
                                // background-color: #0888ff;
                                border-radius: 50%;
                                color: rgba(255, 255, 255, 0.9);
                            }
                            
                        }
                        .guidanceWarp-content-item-right {
                            .guidanceWarp-content-item-right-title {
                                display: flex;
                                align-items: center;
                                margin-bottom: 4px;
                                font-size: 14px;
                                color: rgba(0, 0, 0, 0.9);
                            }
                            .guidanceWarp-content-item-right-text {
                                width: 166px;
                                font-size: 12px;
                                height: 50px;
                                color: rgba(0, 0, 0, 0.6);
                                margin-bottom: 12px;
                                .guidanceWarp-content-item-right-item {
                                    line-height: 16px;
                                    font-size: 12px;
                                    a {
                                        color: rgba(0, 0, 0, 0.6);
                                    }
                                }
                            }

                        }
                    }
                }
            }
            .guidanceWrap-right {
                flex: 2;
                display: flex;
                flex-direction: column;
                background-color: #fff;
                ul {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;
                    flex: 1;
                    align-items: center;
                    li {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        flex: 1;
                        border-right: 1px solid #e2e2e2;
                    }
                    & li:last-child {
                        border-right: none;
                    }
                }
                .guidanceWrap-right-btn {
                    padding:16px 0 24px 0;
                }
            }
            
        }
    }
    .banner {
        width: 100%;
        height: 322px;
        padding: 15px;
        box-sizing: border-box;
        .carouselItem {
            width: 100%;
            height: 100%;
            img {
                width: 100%;
                height: 100%;
            }
        }
    }
    .wu-warp-right {
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 16px;
        border-radius: 2px;
        min-width: 350px;
        .useInfo {
            padding: 20px 16px;
            justify-content: center;
            box-sizing: border-box;
            margin-bottom: 16px;
            background: linear-gradient(180deg, #D2E9FF -36%, #FFFFFF 100%);
            .useInfo-box {
                margin-bottom: 10px;
                .iconfont.icon-yonghu {
                    width: 56px;
                    height: 56px;
                    margin-top: 6px;
                    margin-right: 8px;
                    font-size: 56px;
                    color: rgba(8, 136, 255, .3);
                    line-height: 1;
                }
                .useInfo-box-right {
                    .newIndexWrap-useInfo-name {
                        font-weight: 700;
                        margin-bottom: 12px;
                        margin-top: 6px;
                        display: flex;
                        align-items: center;
                        line-height: 24px;
                        .iconfont.icon-a-copy1x {
                            font-weight: initial;
                            color: rgba(0, 0, 0, 0.4);
                            cursor: pointer;
                        }
                    }
                    p {
                        line-height: 16px;
                    }
                }
            }
        }
        .help-center {
            margin-bottom: 16px;
            .card-header {
                border-bottom: none;
            }
            .help-center-list {
                display: flex;
                flex: 3;
                flex-direction: column;
                padding: 0 12px 12px;
                .help-center-item {
                    cursor: pointer;
                    min-height: 38px;
                    padding: 0 8px;
                    align-items: center;
                    justify-content: space-between;
                    .iconfont {
                        color:#566a7d;
                    }
                }
            }
        }
        .notice {
            flex: 1;
            .card-header {
                border-bottom: none;
            }
            .notice-cont {
                padding: 15px;
                font-weight: bold;
                font-size: 18px;

            }
        }

    }
    .card-waybill {
        margin-top: 16px;
        .waybill-cont {
            padding: 16px;
            flex-wrap: wrap;
            .waybill-item {
                padding: 8px;
                margin-right: 16px;
                align-items: center;
                .duoIcon {
                    width: 16px;
                    height: 16px;
                    background-color: #50c9da;
                    background-image: unset;
                    border-radius: 50%;
                    color: #fff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 5px;
                }
                .iconClass {
                    margin-right: 5px;
                }
            }
        }
    }
    
    .card-common {
        margin-top: 16px;
        .common-cont {
            padding:16px;
            justify-content: space-evenly;
            .common-cont-item {
                padding: 12px;
                margin-right: 16px;
                justify-content: center;
                .iconfont {
                    width: 60px;
                    height: 60px;
                    color: #fff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 45px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                    position: relative;
                    .puhuo-ad-back {
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 50px;
                        height: 100%;
                        transform: skewX(-20deg);
                        background-image: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
                        pointer-events: none;
                    }
                    &:hover {
                        .puhuo-ad-back {
                            animation: shine 1s ease-in-out 0s 1 alternate forwards;
                        }
                    }
                    @keyframes shine {
                        100% {
                            left: 100%;
                        }
                    }
                }
            }
        }
    }
    
}

.helpVedioWrap {
    height: 180px;
    padding: 25px 15px;
    box-sizing: border-box;
    .helpVedioWrap-title {
        padding: 15px 0;
        display: flex;
        justify-content: center;
        font-size: 17px;
        /* color: #888; */
        font-weight: 700;
    }
    .helpVedioWrap-title2 {
        display: flex;
        flex-direction: column;
        line-height: 25px;
        font-size: 16px;
        /* color: #888; */
        font-weight: 400;
    }
    .helpVedioWrap-content {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-top: 30px;
    }
}
.productHelpDailog {
    width: 1000px;
    background-color: #fff;
    .productHelpDailog-title {
        display: flex;
        flex-direction: column;
        line-height: 30px;
        font-size: 18px;
        color: #888;
        padding: 15px;
    }
    .productHelpDailog-content {
        width: 1000px;
        height: 468px;
        display: inline-block;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .productHelpDailog-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 25px;
        padding-bottom: 0;
    }
}