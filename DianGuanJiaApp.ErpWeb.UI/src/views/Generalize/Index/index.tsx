import { useState, useEffect, useCallback, useRef } from 'react'
import { Carousel, Button, message, Modal } from 'antd';
import type { TableProps } from 'antd';
import './index.less';
import Api from '@/api/Generalize/index';

import IndexApi from '@/api/index'

import PlatformShow from '@/components/PlatformShow/platformShow';

import SupportPlatform from '@/components/SupportPlatform/index';

import BindCooperation from '@/components/BindCooperation/index';

import { platform } from 'os';
import { useStore } from '@/store';
let token = useStore.getState().token || '';

import productHelp from "@/assets/noviceIntroPic/generalizeIndex-product-help.png"

let _fxPermission: any = undefined; // 是否有分销权限

function CommonTemplate(props: any) {

    const contentRef = useRef(null);
    const tableWrap = useRef(null);
    const tableEle = useRef(null);
    const contentScrollRef = useRef(null);
    const [carouseHeight, setCarouseHeight] = useState(322);
    useEffect(() => {
        Initialize();
        window.addEventListener('resize', function () {
            var maxWidth = 1920;
            var documentWidth = document.documentElement.clientWidth > 1200 ? document.documentElement.clientWidth:1200;
            var percent = (documentWidth / maxWidth).toFixed(2);
            var picHeight = 322 * percent;
            setCarouseHeight(picHeight)

        })
        
    }, []);
    const Initialize = () => {
        // 轮播图
        getDistributionslideshow();
        getUserInfo();
        // 获取1688轻应用信息
        getQingAppInfo();
        // 统计概况
        getGeneralizeIndexAllTotalV2();
        getGeneralizeIndexPageInfo();
        // getWaitBindShop();
    }
    const [userInfo, setUserInfo] = useState<any>(null); //用户信息
    // 1688轻应用信息
    const [openInfo1688, setOpenInfo1688] = useState({
        Is1688new: false,
        IsOpen1688Shops: false,
        IsOpenAlibabaOld: false,
        IsOpenQing: false,
    }); //1688新用户信息
    // 获取1688轻应用信息
    const getQingAppInfo = async () => {
        const res:any = await Api.GetQingAppInfo();
        if (res.Success) {
            setOpenInfo1688(res.Data?.OpenInfo1688)
            // setIsOpenQing(res.data.isQingApp);
        }
    }
    const getWaitBindShop = async () => {
        const res:any = await Api.GetWaitBindShop();
        if (res.Success) {
            // setWaitBindShop(res.Data);
        }
    }
    // 视图参数
    const [indexPageInfo, setIndexPageInfo] = useState<any>(null);
    // 获取推广首页信息
    const getGeneralizeIndexPageInfo = async () => {
        const param = {
            "LastRefreshTime": "",
            "IsTouTiaoFxApp": 0,
            "OftenUseShowDict": {},
            "binded":''
        }
        const res:any = await Api.GetGeneralizeIndexPageInfo(param);
        if (res.Success) {
            setIndexPageInfo(res.Data);
            let oftenUseShowDict = res.Data?.OftenUseShowDict;
            if (oftenUseShowDict) {
                commonFunctionData.forEach((item:any) => {
                    if (oftenUseShowDict.hasOwnProperty(item.id)) {
                        item.show = oftenUseShowDict[item.id];
                    }
                })
                setCommonFunctionData(commonFunctionData)
            }
            let binded = res.Data?.binded;
            //微信绑定失败
            if (binded != undefined && binded != "") {
                Modal.confirm({
                    title: '提示',
                    content: (
                        <div className='wu-c06'>
                            <p>绑定失败，您的微信已经绑定了其他分销账号,</p>
                            <p>请您使用其他常用微信绑定本账号登录</p>
                        </div>
                    ),
                    wrapClassName: 'modalconfirm-320 modalcerror',
                    icon: <i className='iconfont confirmicon icon-a-error-circle-filled1x wu-f20 wu-color-b'></i>,
                    onOk() { }, // 跳转子账号管理  /System/BindWxUserIndex?targetUrl=success
                    onCancel() { },
                    closable: true,
                    okText: "重新绑定其他账号",
                    cancelText: "取消"
                })
            }
            
        }
    }
    //系统权限相关
    const _getPermissions = async (callback: any, getNew?: any) => {
        //debugger;
        var lastTime = sessionStorage.getItem('LastGetPermissionTime');
        if (!lastTime || new Date() > new Date(parseInt(lastTime)).setMinutes(new Date(parseInt(lastTime)).getMinutes() + 15)) {
            sessionStorage.setItem('LastGetPermissionTime', new Date().getTime());
            getNew = true;
            //console.log("重设LastGetPermissionTime");
        }
        if (!_fxPermission || getNew == true) {
            let ses = sessionStorage.getItem('FxPermission');
            if (!!ses && getNew != true) {
                _fxPermission = JSON.parse(ses);
                callback(); // 在已有权限信息时执行回调函数
            } else {
                let Params: any = new URLSearchParams();
                Params.append('useCache', !getNew);

                let rsp: any = await IndexApi.getSysPermissionDict(Params);
                if (rsp.Success) {
                    if (!!rsp.Data) {
                        sessionStorage.setItem('FxPermission', JSON.stringify(rsp.Data));
                        _fxPermission = rsp.Data;
                    }
                } else {
                    //console.error(rsp.Message);
                }
                callback(); // 在获取权限信息后执行回调函数
            }
        }
        else {
            callback();
        }
    };

    const FxPermission = (callback: any) => {
        if (typeof callback == "function") {
            if (_fxPermission === undefined) {
                _getPermissions(() => {
                    callback(_fxPermission);
                });
            } else {
                callback(_fxPermission);
            }
        }
    }

    // 指定权限的校验 type: null=默认，校验单条 or = 校验多条，逻辑或
    const CheckPermission = async (callback: any, permission?: any, type?: string, ignoreWarn?: boolean, kind?: number) => {
        if (!permission) {
            // 从接口重新获取权限列表
            _getPermissions(function () {

            }, true);

            //console.error("权限校验异常：未指定权限" + permission);
            ignoreWarn = true;
        }
        let rsp: any = null;
        let params = { permission };
        if (type && type == "or") {
            rsp = await IndexApi.CheckPermissionOR(params);
        } else {
            rsp = await IndexApi.CheckPermission(params);
        }
        if (rsp.Success) {
            if (rsp.Data == true) {
                if (typeof (callback) == 'function')
                    callback(true);
            }
            else if (!ignoreWarn) {

                if (kind == 1) {
                    // basicsetModule.setConfig('cantBindPhone')
                } else if (kind == 2) {
                    // basicsetModule.setConfig('cantBindWx')
                } else if (kind == 3) {
                    // basicsetModule.setConfig('cantBindPhone')
                } else if (kind == 4) {

                } else {
                    message.error("暂无权限，请联系管理员");
                }
            }
        } else {
            message.error(rsp.Message || '请求失败，请重试');
        }
        callback(false);
    }
    
    // 获取用户信息
    const getUserInfo = async () => {
        const res:any = await Api.GetUserInfo();
        console.log("getUserInfo===",res);
        if (!res.Success) {
            message.error(res.Message);
            return
        }
        setUserInfo(res.Data);
        
    }
    //计算时间差
    const dateDiffHours = (dateBegin:any, dateEnd:any) => {
        if (!dateBegin)
            return 0;
        if (!dateEnd)
            dateEnd = new Date();
        var dateDiff = dateEnd.getTime() - dateBegin.getTime();//时间差的毫秒数
        var hours = Math.floor(dateDiff / (3600 * 1000)); //计算出小时数  
        return hours;
    }
    

    const getDistributionslideshow = async () => {  //获取店管家广告
        let params = {
            type: 'showAD',
            platformName: 'System',
            releaseADVersion: 2,
            releaseADVersionNum: 'fendan',
            shopId: 1443,
            shopName: 13065187972,
            baseURL: 'https://www.dgjapp.com',
        }
        var data = await Api.getDistributionslideshow(params);
        console.log(data);
    }
    // 轮播图
    const [carouselList, setCarouselList] = useState<any>([
        {
            "_id": "633653e4a7921b2fe41bfb50",
            "articletitle02": "",
            "releaseADVersionNum": "",
            "releaseADVersion": 0,
            "articleId": "1664504799236",
            "whiteType": "1",
            "whitelist": "",
            "articleUrl": "./public/images/midleAloaigPic/1682506906241第三张.png"
        },
        {
            "_id": "6405900e5eb7f014ace39f46",
            "articletitle02": "#",
            "releaseADVersionNum": "",
            "releaseADVersion": 0,
            "articleId": "1678086160205",
            "whiteType": "1",
            "whitelist": "",
            "articleUrl": "./public/images/midleAloaigPic/17050254273131682506823818ww.png"
        },
        {
            "_id": "6479936b9520dc1ae809d462",
            "articletitle02": "#",
            "releaseADVersionNum": "",
            "releaseADVersion": 0,
            "articleId": "1685689197391",
            "whiteType": "1",
            "whitelist": "",
            "articleUrl": "./public/images/midleAloaigPic/1685689188074eeeeeee.png"
        },
        {
            "_id": "64098da96456411b908a8597",
            "articletitle02": "https://docs.qq.com/doc/DQ2FGTkJjSmlBb014",
            "releaseADVersionNum": "",
            "releaseADVersion": 0,
            "articleId": "1678347692032",
            "whiteType": "1",
            "whitelist": "",
            "articleUrl": "./public/images/midleAloaigPic/1682506861676第二张.gif"
        },
        {
            "_id": "63493213d2c4c3346c47ccae",
            "articletitle02": "https://docs.qq.com/doc/DQ0lET1FUQkVvSW9N",
            "releaseADVersionNum": "",
            "releaseADVersion": 0,
            "articleId": "1665741331994",
            "whiteType": "1",
            "whitelist": "",
            "articleUrl": "./public/images/midleAloaigPic/1682506927434zzkm7OPEaRbQn41ukhOAQPhu.png"
        }
    ])
    // 绑定厂家商家数据
    const [bindCooperationData, setBindCooperationData] = useState<any>(null)
    // 绑定厂家和商家
    const onBindFactory = async (type:string) => {

        FxPermission(function (p: any) {
            CheckPermission(function (success: any) {
                if (!success) {
                    setIsShowBindCooperation(true);
                    setBindCooperationData({
                        type: type
                    })
                }
                else return;
            }, p.AddBindSupplier);
        });
    }
    
    // 添加店铺
    const onAddShopWarn  = () => {
        FxPermission(function (p: any) {
            CheckPermission(function (success: any) {
                if (!success) {
                    setIsShowSupportPlatform(true)
                }
                else return;
                
            }, p.AddNewShop);
        });
    }
    // 结算教程弹窗
    const [isHelpVedio, setIsHelpVedio] = useState<boolean>(false);

    // 结算教程
    const onSettlementTutorial = () => {
        setIsHelpVedio(true)
    }
    // 新手引导列表
    const [guidanceList, setGuidanceList] = useState<any>([
        {
            key: 'merchantGuidance',
            title: '新手商家引导',
            tutorials: '视频教程',
            tutorialsHref: 'https://img.dgjapp.com/newAgentVideo.mp4?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv',
            steps:[
                {
                    key: 'merchantGuidance-step1',
                    title: '绑定厂家',
                    desc: '请邀请您的发货方来注册系统并与他在系统内建立合作关系。',
                    btnText: '绑定厂家',
                    click: () => {
                        onBindFactory('supplier')
                    }
                },
                {
                    key: 'merchantGuidance-step2',
                    title: '添加店铺',
                    desc: '需前往服务市场订购应用，添加成功后可同步店铺订单与商品。',
                    btnText: '添加店铺',
                    click: () => {
                        onAddShopWarn()
                    }
                },
                {
                    key: 'merchantGuidance-step3',
                    title: '绑定商品',
                    desc: '把商品绑定给合作厂家，该商品产生的订单即可自动推送给厂家。',
                    btnText: '绑定商品',
                    click: () => {
                        setIsBindGoods(true)
                    }
                }
            ],
            rightTiele: '待确认合作关系 ',
            rightTutorials : '',
            rightList: [
                {
                    key: 'WaitConfirmAgentCount',
                    title: '商家',
                    count: '0',
                },
                {
                    key: 'WaitConfirmSupplierCount',
                    title: '厂家',
                    count: '0',
                }
            ]
        },
        {
            key: 'newSupplier',
            title: '新手厂家引导',
            tutorials: '视频教程',
            tutorialsHref: 'https://img.dgjapp.com/newSupplierVideo.mp4?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv',
            steps:[
                {
                    key: 'newSupplier-step1',
                    title: '添加合作商家',
                    desc: '请邀请您的发货方来注册系统并与他在系统内建立合作关系。',
                    btnText: '绑定商家',
                    click: () => {
                        onBindFactory('agent')
                    }
                },
                {
                    key: 'newSupplier-step2',
                    title: '邀请商家推送代发订单',
                    desc: '复制教程发送给商家，让对方按教程流程建立代发订单自动推送',
                    btnText: '邀请商家推送订单',
                    click: () => {
                        Modal.confirm({
                            title: '提示',
                            icon: <i className='iconfont confirmicon icon-a-info-circle-filled1x wu-f20 wu-color-a'></i>,
                            content: (
                                <div className='wu-c06'>
                                    <p>复制以下代发教程链接发送给分销商家，</p>
                                    <p>请商家按教程设置自动推送代发订单。</p>
                                </div>
                            ),
                            wrapClassName: 'modalconfirm-320 wu-closeIcon',
                            okText: "复制教程",
                            onOk() { handleCopy('https://docs.qq.com/doc/DQ2pLQlhqdGtzUGxk')},
                        })
                    }
                },
                {
                    title: '打单设置',
                    key: 'orderSetting',
                    desc: '',
                    btnText: '打单设置'
                }
            ],
            rightTiele: '待确认代发账单',
            rightTutorials : '结算教程',
            rightTutorialsHref: '',
            rightTutorialsRender: () => {
                return (
                    <div className=" wu-f14 wu-color-a wu-operate" onClick={() =>onSettlementTutorial()}>结算教程</div>
                )
            },
            rightList: [
                {
                    key: 'BillManagementWaitConfirmAgentCount',
                    title: '商家',
                    count: '0',
                },
                {
                    key: 'BillManagementWaitConfirmSupplierCount',
                    title: '厂家',
                    count: '0',
                }
            ]
        }
    ]);
    // 帮助中心列表数据
    const [helpList, setHelpList] = useState<any>([
        {
            title: '1分钟了解店管家分销代发ERP',
            href: 'https://docs.qq.com/doc/DQ2FGTkJjSmlBb014?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv',
        },
        {
            title: '中间商如何使用店管家分销代发',
            href: 'https://docs.qq.com/doc/DVGxOZmJ1UFRCRVJ1?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv',
        },
        {
            title: '常见代发打印问题教程',
            href: 'https://docs.qq.com/doc/DVFVQQnJJbXZ2cElx?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv',
        }
    ]);
    
    // 电子面单列表数据
    const [waybillList, setWaybillList] = useState<any>([
        {
            href: "https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT",
            iconClass: "TouTiao",
            text: "抖音电子面单教程",
          },
          {
            href: "https://docs.qq.com/doc/DU0xlTVZ6T2VVUktq?u=d5705e9be5964650a8cfc88cf0e24033",
            iconClass: "KuaiShou",
            text: "快手电子面单教程",
          },
          {
            href: "https://docs.qq.com/doc/DQ0xnUm11eUlNbWxr",
            iconClass: "Pinduoduo",
            text: "拼多多电子面单教程",
          },
          {
            href: "https://docs.qq.com/doc/DQ3NLYklZbnZQTlJK",
            iconClass: "Link",
            text: "菜鸟电子面单教程"
          },
          {
            href: "https://www.dgjapp.com/newHelpsShow.html?id=1695794138878",
            iconClass: "XiaoHongShu",
            text: "小红书电子面单教程"
          },
          {
            href: "https://www.yuque.com/caixiangmiao/ckbbwt/pd6ym0ng4ooyyazw",
            iconClass: "WxVideo",
            text: "视频号电子面单教程"
          },
          {
            href: "https://docs.qq.com/doc/DQ3lYWHNxWnl5eUdW",
            iconClass: "Jingdong",
            text: "京东电子面单教程"
          },
          {
            href: "https://docs.qq.com/doc/DQ1dIcVFnTUdMV1J4",
            iconClass: "duoIcon",
            text: "多多包裹单号回传教程"
          }
    ])
    // 常用功能数据
    const [commonFunctionData, setCommonFunctionData] = useState<any>([
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-dayin",
            text: "打印设置",
            background: "#f59c1b",
            id:'NewOrderPrintSetting',
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-JC_036",
            text: "电子面单",
            background: "#2eb4e4",
            id:'NewOrderEbillAccount',
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-wodecduan_xianxiadingdan",
            text: "线下订单",
            background: "#33d6ff",
            id:'OfflineOrder',
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-dingdandingdanmingxishouzhimingxi",
            text: "已代发明细",
            background: "#37ceb3",
            id:'SendOrderIndex',
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-gongchang",
            text: "厂家打印快递",
            background: "#3aadff",
            id:'WaybillCodeList_cj'
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-chuku",
            text: "打单发货",
            background: "#fe6f4f",
            id: 'WaitOrder'
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-shangpinguanli1",
            text: "代发商品",
            background: "#77bf04",
            id:'ProductIndex'
          },
          {
            href: "/generalize/express",
            iconClass: "iconfont icon-kucunguanli",
            text: "仓库管理",
            background: "#c89ce7",
            id:'StoreManagement'
          }
    ])

    // 统计数量
    const [allTotal, setAllTotal] = useState<any>(null);
    const getGeneralizeIndexAllTotalV2 = async (refresh: boolean = false) => {
        var startTime =  "2025-07-06T16:00:00.000Z";
        var endTime = '2025-07-07T15:59:59.999Z';
        // var diffHours = dateDiffHours(startTime, endTime);
        console.log("时间间隔不能超过30天");
        // if (diffHours > 24 * 30) {
        //     message.warning("时间间隔不能超过30天");
        //     return;
        // }
       const param ={
            startTime: startTime, 
            endTime: endTime, 

            // refresh: refresh
        }
        const res:any = await Api.GetGeneralizeIndexAllTotalV2(param); 
        
        if (!res.Success) {
            message.error(res.Message);
            return
        }

        setAllTotal(res.Data);
        guidanceList.map((item: any) => {
            item.rightList.map((item1: any) =>{
                item1.count = res.Data[item1.key];
            })
        })
        setGuidanceList(guidanceList);
    }

    const [noticeContent, setNoticeContent] = useState<any>("<p><span style=\"font-size: 18px;\"><span style=\"color: rgb(146, 208, 80);\"><strong>用户操作须知</strong></span><strong>：分销代发系统禁止一切平台订单（除手工录入单）明文展示以及导出，使用本系统需遵守各平台代发打印规则，使用每个平台对应的电子面单打单发货。<br/><span style=\"color: rgb(255, 192, 0);\">系统维护公告</span>：每日凌晨两点到六点是系统维护时间，请不要进行打单发货操作！ <br/><span style=\"color: rgb(84, 141, 212);\">客服在线服务时间:</span>【9:30-22:30 】其余时间，您可留言，谢谢。</strong></span></p>")
    const contentStyle: React.CSSProperties = {
        height: '160px',
        color: '#fff',
        lineHeight: '160px',
        textAlign: 'center',
        background: '#364d79',
        width: '100%'
      };
    // 支持平台弹窗
    const [isShowSupportPlatform, setIsShowSupportPlatform] = useState(false)
    const [isShowBindCooperation, setIsShowBindCooperation] = useState(false);
    const onQuick = () => {
        console.log("token===",token)
        window.open("/System/CreateUrl?token="+token);
    }
    // 复制文本
    const handleCopy = (textToCopy: string) => {
        navigator.clipboard.writeText(textToCopy).then(() => {
            message.success('复制成功！');
        }).catch(err => {
            message.error('复制失败: ',err);
        });
    };
    // 待确认代发账单-设置结算价 
    const [isSetAccountDailog, setIsSetAccountDailog] = useState(false);
    const onClickNewSupplier = () => {
        setIsSetAccountDailog(true);
    }
    const [isBindGoods, setIsBindGoods] = useState(false);
    return (
        <div className='wu-container GeneralizeIndex wu-flex wu-column'>
            <div className="wu-warp wu-flex">
                <div className="wu-warp-left">
                    <div className="wu-8radius wu-color-n wu-background banner" style={{height: carouseHeight+ 'px'}}>
                    <Carousel arrows infinite={false} draggable autoplay >
                        {
                            carouselList.map((item: any, index: number) => {
                                return (
                                    <div className='carouselItem' key={item._id} >
                                        <img style={{height: carouseHeight-32 + 'px'}} src={"https://www.dgjapp.com/"+item.articleUrl} alt="" />
                                    </div>
                                )
                            })
                        }
                    </Carousel>
                    </div>
                    {
                        guidanceList && guidanceList.map((item: any, index: number) => {
                            return (
                                <div className="guidanceWrap wu-mT16 wu-flex " key={item.key}>
                                    <div className="guidanceWrap-left wu-8radius card">
                                        <div className="guidanceWrap-header card-header">
                                            <span className="title">{item.title}</span>
                                            <a 
                                            className='newIndexWrap-title-dText  wu-f14 wu-color-a wu-operate'
                                            href={item.tutorialsHref}
                                            >
                                                {item.tutorials}
                                            </a>
                                        </div>
                                        <div className="guidanceWarp-content wu-flex">
                                            {
                                                item.steps && item.steps.map((itemStep: any, indexStep: number) => {
                                                    return (
                                                        <div className="guidanceWarp-content-item wu-flex" key={itemStep.key}>
                                                            <div className="guidanceWarp-content-item-left">
                                                                <div className="step-icon wu-background-blue wu-f12">{indexStep + 1}</div>
                                                            </div>
                                                            <div className="guidanceWarp-content-item-right">
                                                                <div className="guidanceWarp-content-item-right-title">
                                                                {itemStep.title}
                                                                </div>
                                                                <div className="guidanceWarp-content-item-right-text">
                                                                {itemStep.key === 'orderSetting'?

                                                                    <div className="guidanceWarp-content-item-right-list">
                                                                        <div className="guidanceWarp-content-item-right-item">
                                                                            <a href="https://3tfxali.dgjapp.com/SellerInfo?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv">
                                                                            1:添加发件人
                                                                            <i className='iconfont icon-a-jump1x wu-color-a'></i>
                                                                            </a>
                                                                        </div>
                                                                        <div className="guidanceWarp-content-item-right-item">
                                                                            <a href="https://3tfxali.dgjapp.com/AccountList?token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv">
                                                                            2:添加商家对应平台的面单
                                                                            <i className='iconfont icon-a-jump1x wu-color-a'></i>
                                                                            </a>
                                                                        </div>
                                                                        <div className="guidanceWarp-content-item-right-item">
                                                                        3:下载面单对应的打印组件
                                                                        </div>
                                                                    </div>
                                                                :itemStep.desc}
                                                                </div>
                                                                <div className="guidanceWarp-content-item-right-btn">
                                                                    <Button color="primary" variant="outlined" className='wu-mR8' onClick={itemStep.click}>{itemStep.btnText}</Button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                })
                                            }
                                        </div>
                                    </div>
                                    <div className="guidanceWrap-right wu-background wu-8radius card">
                                        <div className="guidanceWrap-header card-header">
                                            <span className="title">{item.rightTiele}</span>
                                            {
                                                item.rightTutorialsRender ?
                                                item.rightTutorialsRender(): null
                                            }
                                        </div>
                                        <ul className="helpSteps">
                                            {
                                                item.rightList && item.rightList.map((itemRightList: any, indexRightList: number) => {
                                                    return (
                                                        <li key={itemRightList.key}>
                                                            <span className='wu-f14 wu-c09'>{itemRightList.title}</span>
                                                            <span className="wu-f24 wu-c09 wu-weight600  pointer">{itemRightList.count}</span>
                                                        </li>
                                                    )
                                                })
                                            }
                                        </ul>
                                        {
                                            item.key === 'newSupplier' ?
                                            <div className='wu-flex wu-xCenter guidanceWrap-right-btn'>
                                            <Button color="primary" variant="outlined" className='wu-mR8' onClick={() => {onClickNewSupplier()}}>设置结算价</Button>
                                            </div>
                                            : null
                                        }
                                    </div>
                                </div>
                            )
                        })
                    }
                </div>
                <div className="wu-warp-right">
                    <div className="useInfo wu-flex wu-column wu-8radius">
                        <div className="useInfo-box wu-flex">
                            {/* <img src="https://img.alicdn.com/imgextra/i1/O1CN01Z6b8kx1O0u9ZQ0j5j_!!*************-2-tps-200-200.png" alt="" /> */}
                            <span className="iconfont icon-yonghu"></span>
                            <div className="useInfo-box-right">
                                <div className="newIndexWrap-useInfo-name wu-f16 wu-c09 ">
                                {userInfo?.MainMobile} <i className='iconfont icon-a-copy1x wu-mL4 copy-account-icon' onClick={() => handleCopy(userInfo?.MainMobile)}></i>
                                </div>
                                <p className='wu-f12 wu-c06 wu-mB4'>欢迎您使用店管家分销代发ERP</p>
                                <p className='wu-f12 wu-c06'>非常荣幸能与您同行第
                                    <span className='wu-color-a wu-weight500'>{userInfo?.RegisteredDays}</span>
                                    天
                                </p>
                            </div>
                        </div>
                        <div className='wu-flex wu-xCenter'>
                            <Button color="primary" variant="outlined" className='wu-mR8' onClick={() => {onQuick()}}>
                                <i className="iconfont icon-a-add1x"></i>
                                添加到桌面快捷方式
                            </Button>
                        </div>
                        
                    </div>
                    <div className="card help-center wu-8radius">
                        <div className="card-header">
                            <span className="title">帮助中心</span>
                            <a 
                                className='newIndexWrap-title-dText  wu-f14 wu-color-a wu-operate'
                                href="https://www.dgjapp.com/newHelpsClassify.html?id=&columnId=1643102261066&type=helps&token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv"
                                >
                                更多
                            </a>
                        </div>
                        <div className="help-center-list">
                            {
                                helpList && helpList.map((item: any, index: number) => {
                                    return (
                                        <a href={item.href} key={"helpList"+index}>
                                        <div className="help-center-item wu-flex">
                                            <span className='wu-f14 wu-c09'>{item.title}</span>
                                            <i className='iconfont icon-a-chevron-right1x '></i>
                                        </div>
                                        </a>
                                    )
                                })
                            }
                            
                        </div>
                    </div>
                    <div className="card notice wu-8radius">
                        <div className="card-header">
                            <span className="title">公告</span>
                        </div>
                        <div className="notice-cont" dangerouslySetInnerHTML={{__html: noticeContent}}>
                        </div>
                    </div>

                   

                </div>
            </div>
            <div className="card wu-8radius card-waybill">
                <div className="card-header">
                    <span className="title">电子面单开通教程</span>
                </div>
                <div className="waybill-cont wu-flex wu-f14">
                    {
                        waybillList.map((item: any, index: number) => {
                            return (
                                <div className="waybill-item wu-flex" key={"waybill"+index}>
                                    {
                                        item.iconClass !== 'duoIcon'?
                                        <PlatformShow size='wu-small' platform={item.iconClass} className={'iconClass'} style={{borderRaduius: '50%'}}/>
                                        : <span className={item.iconClass}>多</span>
                                    }
                                    <a  href={item.href} target="_blank">
                                    <span className="wu-color-e wu-operate">{item.text}</span>
                                    </a>
                                </div>
                            )
                        })
                    }
                </div>
            </div> 
            <div className="card wu-8radius card-common">
                <div className="card-header">
                    <span className="title">常用功能</span>
                </div>
                <div className="common-cont wu-flex wu-f14">
                    {
                        commonFunctionData.map((item: any, index: number) => {
                            if (!item.show) return null;
                            return (
                                <div className="common-cont-item wu-flex wu-column" key={"waybill"+index} >
                                    <a className='wu-flex wu-column wu-xCenter' href={item.href} target="_blank">
                                    <span className={item.iconClass} style={{backgroundColor: item.background}}>
                                        <span className='puhuo-ad-back'></span>
                                    </span>
                                    
                                    <span className="wu-color-e wu-operate">{item.text}</span>
                                    </a>
                                </div>
                            )
                        })
                    }
                </div>
            </div> 
            <SupportPlatform isShow={isShowSupportPlatform} close={()=> setIsShowSupportPlatform(false)}/>
            <BindCooperation isShow={isShowBindCooperation} data={bindCooperationData} close={()=> setIsShowBindCooperation(false)}/>
            {/* 绑定商品弹窗 */}
            <Modal
                title=""
                open={isBindGoods}
                onCancel={() => setIsBindGoods(false)}
                footer={null}
                width={1050}
            >
                <div className="productHelpDailog">
                    <div className="productHelpDailog-title">
                        <span>即将前往代发商品绑定操作，商品绑定成功后，</span>
                        <span>代发订单会自动推送给对方账号，厂家点击 '打单发货功能' 进行打印发货，单号自动回传到店铺后台</span>
                        <div className="">
                            <span className='wu-color-d'>1.商品列表功能找到代发商品</span>
                            <span className='wu-color-d'> 2.点击绿色按钮绑定给厂家供货</span>
                        </div>
                    </div>
                    <div className="productHelpDailog-content">
                        <img src={productHelp} alt="" />
                    </div>
                     <div className="productHelpDailog-footer">
                        <Button color="pink" variant="solid" size='large' onClick={() => setIsBindGoods(false)}>前往绑定商品</Button>
                    </div>
                </div>
            </Modal>
            {/* 请根据对方身份查看对应教程 */}
            <Modal
                title=""
                open={isHelpVedio}
                onCancel={() => setIsHelpVedio(false)}
                footer={null}
                width={450}
            >
                <div className="helpVedioWrap">
                    <p className='helpVedioWrap-title'>请根据对方身份查看对应教程</p>
                    <div className="helpVedioWrap-content">
                        <Button color="primary" variant="filled" 
                            onClick={() => {
                                setIsHelpVedio(false);
                                window.open("https://img.dgjapp.com/fxduizhangjiesuan.mp4?token=61F7CC5D350B0030A4791A13D06BFED8&dbname=tIzxRoFoJOHcRUm2Z87zQVgTLKEdB0Mb")
                            }}
                        >对厂家出账教程</Button>
                        <Button color="primary" variant="filled" 
                        onClick={() => {
                            window.open("https://img.dgjapp.com/fxduizhang.mp4?token=61F7CC5D350B0030A4791A13D06BFED8&dbname=tIzxRoFoJOHcRUm2Z87zQVgTLKEdB0Mb");
                            setIsHelpVedio(false);
                        }}
                        >对分销商家出账教程</Button>
                    </div>
                </div>
            </Modal>

            {/* 待确认代发账单-设置结算价 */}
            <Modal
                title=""
                open={isSetAccountDailog}
                onCancel={() => setIsSetAccountDailog(false)}
                footer={null}
                width={450}
            >
                <div className="helpVedioWrap">
                    <div className='helpVedioWrap-title2 wu-c06 wu-f16'>
                        <p>设置结算价要求:</p>
                        <p>代发订单变成已发货后才能进行对应商品价格的设置</p>
                    </div>
                    <div className=" helpVedioWrap-content">
                        <Button color="primary" variant="filled" 
                            onClick={() => {
                                setIsSetAccountDailog(false);
                                // window.open("https://img.dgjapp.com/fxduizhangjiesuan.mp4?token=61F7CC5D350B0030A4791A13D06BFED8&dbname=tIzxRoFoJOHcRUm2Z87zQVgTLKEdB0Mb")
                            }}
                        >对厂家设置结算价</Button>
                        <Button color="pink" variant="filled" 
                        onClick={() => {
                            // window.open("https://img.dgjapp.com/fxduizhang.mp4?token=61F7CC5D350B0030A4791A13D06BFED8&dbname=tIzxRoFoJOHcRUm2Z87zQVgTLKEdB0Mb");
                            setIsSetAccountDailog(false);
                        }}
                        >对商家设置结算价</Button>
                    </div>
                </div>
            </Modal>
        </div>
    )
}



export default CommonTemplate;