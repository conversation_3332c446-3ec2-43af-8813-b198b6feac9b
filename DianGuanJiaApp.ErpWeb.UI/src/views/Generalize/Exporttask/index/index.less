@import '../../../../styles/unit.less';
.exportExcelIndex-wrap {
    .exportExcelIndex-wrap-left {
        width: 150px;
        height: 100%;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        .exportExcelIndex-wrap-left-title {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            height: 40px;
            background-color: #f7f7f8;
        }
        .exportExcelIndex-wrap-navs {
            li {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 40px;
                cursor: pointer;
                position: relative;
                &.active {
                    background-color: rgba(8, 136, 255, 0.08);
                    color: #0888FF;
                    font-weight: 600;
                    &::after {
                        position: absolute;
                        width: 2px;
                        height: 100%;
                        background-color: #3aadff;
                        top: 0;
                        left: 1px;
                        content: '';
                        display: block;
                    }
                }
                &:hover {
                    background-color: rgba(8, 136, 255, 0.08);
                    color: #0888FF;
                    font-weight: 600;
                    &::after {
                        position: absolute;
                        width: 2px;
                        height: 100%;
                        background-color: #3aadff;
                        top: 0;
                        left: 1px;
                        content: '';
                        display: block;
                    }
                }
                
            }
        }
    }
    .exportExcelIndex-wrap-right {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        .right-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            .iframewrap {
                flex: 1;
                width: 100%;
                height: 100%;
                overflow: hidden;
                iframe {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
            }
        }
        .empty {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding-top: 300px;
            .empty-icon {
                width: 150px;
                height: 150px;
            }
            .empty-text {
                margin-top: 10px;
                font-size: 16px;
                color: #666;
                text-align: center;
            }
        }
    }
}