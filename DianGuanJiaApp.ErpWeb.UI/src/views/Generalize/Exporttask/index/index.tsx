import axios from 'axios'
import request from '@/utils/request'
import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom'
import { Button, Radio, Tabs } from "antd";
import type { CascaderProps } from 'antd';
import './index.less';

import ChangePlatformMenu from '@/components/ChangePlatformMenu/ChangePlatformMenu'

export default function Exporttask() {
    const navigate = useNavigate()  //事件跳转路由


    useEffect(() => {
        filtNavs();

    }, [])

    const contentStyle: React.CSSProperties = {
        margin: 0,
        height: '100%',
        color: '#fff',
        lineHeight: '169px',
        textAlign: 'center',
        background: '#9e70f6',
    };

    const goToIndex=()=>{
        navigate("/order")
    }
    const [navs, setNavs] = useState<any>([
        { id: 'AllOrder', type: '9', title: '所有订单', isSelect: false, isHaCloudPlatform: true,sort:1 },
        { id: 'WaitOrder', type: '10', title: '打单发货', isSelect: false, isHaCloudPlatform: true, sort:2 },
        { id: 'OfflineOrder', type: '12', title: '线下单', isSelect: false, isHaCloudPlatform: true, sort:3 },
        //{ id: 'Purchases', type: '13', title: '备货单', isSelect: false, isHaCloudPlatform: true },
        { id: 'WaybillCodeList', type: '11', title: '底单查询', isSelect: false, isHaCloudPlatform: true, sort:4 },
        { id: 'ErpWaybill', type: '16', title: '厂家底单', isSelect: false, isHaCloudPlatform: true, sort:5 },
        { id: 'PrintHistory', type: '22', title: '打印记录', isSelect: false, isHaCloudPlatform: true, sort:6 },
        { id: 'SendOrder', type: '18', title: '已发货明细', isSelect: false, isHaCloudPlatform: true, sort:7 },
        { id: 'AfterSale', type: '19', title: '代发售后', isSelect: false, isHaCloudPlatform: true, sort: 8},
        { id: 'StockDetail', type: '20', title: '实时库存', isSelect: false, isHaCloudPlatform: false, sort: 9},
        { id: 'DownBill', type: '34', title: '账单下载', isSelect: false, isHaCloudPlatform: false, sort: 10},
        { id: 'ProfitStatistics', type: '50', title: '利润统计', isSelect: false, isHaCloudPlatform: true, sort:11 },
        { id: 'ProfitReport', type: '51', title: '利润报表', isSelect: false, isHaCloudPlatform: true, sort:12 },
    ])
    const [navId, setNavId] = useState('')
    const filtNavs = () => {
        var isShowDownBill = true; // 是否显示账单下载
        var exportPermissions = [
                "Export/AllOrder",
                "Export/WaitOrder",
                "Export/OfflineOrder",
                "Export/WaybillCode",
                "Export/WaybillCode_Cj",
                "PrintHistoryController/PrintHistoryExportExcel",
                "SendOrderController/ExportExcel",
                "AfterSaleController/ExportExcel",
                "StockControlController/StockDetailExportExcel",
                "Export/SettlementBill",
                "ProfitStatisticsController/ExportProfitStatistics",
                "ProfitStatisticsController/ExportProfitReport"
            ];
        var _newNavs:any = [];
        exportPermissions.forEach(function (item, i) {
            switch (item) {
                case 'Export/AllOrder':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'AllOrder' }));
                    break;
                case 'Export/WaitOrder':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'WaitOrder' }));
                    break;
                case 'Export/OfflineOrder':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'OfflineOrder' }));
                    break;
                case 'Export/WaybillCode':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'WaybillCodeList' }));
                    break;
                case 'Export/WaybillCode_Cj':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'ErpWaybill' }));
                    break;
                case 'PrintHistoryController/PrintHistoryExportExcel':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'PrintHistory' }));
                    break;
                case 'SendOrderController/ExportExcel':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'SendOrder' }));
                    break;
                case 'AfterSaleController/ExportExcel':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'AfterSale' }));
                    break;
                case 'StockControlController/StockDetailExportExcel':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'StockDetail' }));
                    break;
                case 'ProfitStatisticsController/ExportProfitStatistics':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'ProfitStatistics' }));
                    break;
                case 'ProfitStatisticsController/ExportProfitReport':
                    _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'ProfitReport' }));
                    break;
                case 'Export/SettlementBill':
                    if (isShowDownBill)
                        _newNavs.push(navs.find(function (m: { id: string; }) { return m.id === 'DownBill' }));
                    break;
            }
        });
        var tempNavs = _newNavs;
        console.log("tempNavs===",tempNavs)
        setNavs(tempNavs);

    //  common.Ajax({
    //      url: '/SubAccount/GetSubPermissionsByParentTag',
    //      loading: false,
    //      async: false,
    //      data:{tag:"Export"},
    //      success: function (rsp) {
    //          if (rsp && rsp.Success) {
                 
    //          } else {
    //              layer.msg(rsp.Message);
    //          }

    //      }
    //  });
    }

    return (
        <div className="exportExcelIndex-wrap wu-bg-fff wu-heigt100 wu-flex">
            <div className="exportExcelIndex-wrap-left">
                <div className="exportExcelIndex-wrap-left-title">功能模块:</div>
                <ul className='exportExcelIndex-wrap-navs'>
                    {
                        navs.map((nav: { id: string; title: string; }) => {
                            return (
                                <li key={nav.id} className={nav.id == navId ? 'active' : ''} onClick={() => { setNavId(nav.id) }}>
                                    {nav.title}
                                </li>
                            )
                        })
                    }
                </ul>
            </div>
            <div className="exportExcelIndex-wrap-right">
                {
                    navId ?
                    <div className='right-content'>
                        <div className='haCloudPlatform wu-platformWrap'>
                            <ChangePlatformMenu isHideAgents={true} />
                        </div>
                        <div className="iframewrap">
                            <iframe src={'https://3tfxali.dgjapp.com/ExportTask/rightContentRender?token=2782D23570DE615CC9D3304EEF6582BF&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv&type=9&platformType=alibaba&tarType=ifarme&nav=AllOrder'} width="100%" height="100%" frameBorder="0" scrolling="no"></iframe>
                        </div>
                    </div> 
                    :<div className="empty">
                        <img className='empty-icon' src="https://3tfxali.dgjapp.com/Content/images/exportExcelIndex-icon.png" alt="" />
                        <p className='empty-text'>操作提示：请点击左侧功能模块，查看对应导出任务</p>
                    </div>
                }
                
            </div>
        </div>
    )
}