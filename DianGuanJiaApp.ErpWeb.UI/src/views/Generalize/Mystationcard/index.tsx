import axios from 'axios'
import request from '@/utils/request'
import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom'
import { Button, Radio, Carousel } from "antd";
import type { CascaderProps } from 'antd';
import './index.less';

export default function MystationCard() {
    const navigate = useNavigate()  //事件跳转路由


    useEffect(() => {


    }, [])


    return (
        <div className='annt-indexWrap'>
            <div className='wu-m16 wu-bg-fff wu-8radius wu-p16'>我的小站</div>
        </div>
    )
}