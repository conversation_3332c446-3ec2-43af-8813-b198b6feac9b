import React from 'react';
import { SearchOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { Input,Button,Card, message } from 'antd';
import request from '@/api/helpcenter.ts';
import img1 from '@/assets/img1.png';
import img2 from '@/assets/img2.png';
import img3 from '@/assets/img3.png';
import mask from '@/assets/mask.png';
import { useNavigate } from 'react-router-dom';
import './index.less'; // 确保路径正确

const CustomInput = styled(Input)`
  &:focus-within {
    box-shadow: none;
    border-color: #d9d9d9;
    /* 其他你想要覆盖的样式 */
  }
`;
const HomePage = () => {
  let navigate = useNavigate();
  const headerRef = React.useRef(null);
  const searchRef = React.useRef(null);
  const searchRef1 = React.useRef(null);
  const [guide, setGuide] = React.useState({ContainerType1: [], ContainerType2: [], ContainerType3: []});
  const [isSearch, setIsSearch] = React.useState(false);
  const [searchList, setSearchList] = React.useState([]);
  const [inputVlue, setInputVlue] = React.useState(null);
  const [isFocus, setIsFocus] = React.useState(false);
  const handleScroll = () => {
    if (!headerRef?.current) {
      return
    }
    if (window.scrollY > 138) {
      // 修改背景颜色
      headerRef.current.style.backgroundColor = '#fff';
      headerRef.current.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
      searchRef.current.style.opacity = 1;
      searchRef1.current.style.opacity = 0;
    }else{
      headerRef.current.style.backgroundColor = 'transparent';
      headerRef.current.style.boxShadow = 'none';
      searchRef.current.style.opacity = 0;
      searchRef1.current.style.opacity = 1;
    }
  };
  React.useEffect(() => {
    request.GetContainerTree().then(res => {
      if (res.Success) {
        setGuide({
          ContainerType1: res.Data.filter(item => item.ContainerType === 1),
          ContainerType2: res.Data.filter(item => item.ContainerType === 2),
          ContainerType3: res.Data.filter(item => item.ContainerType === 3),
        })
      }
    })
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  const customInputBlur = (e) => {
    setIsFocus(false);
  };
  const customInputFocus = (e) => {
    setIsFocus(true);
  };
  const customInputChange = async(e) => {
    setInputVlue(e.target.value);
    if(e.target.value){
      let str = e.target.value;
      const param = {
        title: str
      }
      const res = await request.SearchArticleTitle(param);
      if (!res.Success) return;
      console.log(res,'res');
      let list = Object.keys(res.Data).map(key => ({ key, value: res.Data[key] }));
      const newList = list.map(item => {
        let newString = item.value.replace(new RegExp(str, 'g'), function(match) {
          return `<span style="color: red;">${match}</span>`;
        });
        return {key: item.key, value: newString};
      })
      setSearchList(newList);
      setIsSearch(true);
    }else{
      setIsSearch(false);
      setSearchList([])
    }
  };
  const clickItem = (type, item) => {
    switch (type) {
      case 1:
        navigate(`/Results?id=${item.ArticleCode}`);
        break;
      case 2:
          item.RelatedUrl ? window.open(item.RelatedUrl, '_blank'): message.info('暂无链接');
        break;
      default:
          message.info('暂无关联');
        break;
    }
  }

  return (
    <div style={{height: '100vh'}}>
      <div ref={headerRef} className="header_container">
        <div className="header_title">店管家分销代发ERP
          <span className="divider"></span>
          <span className="text">帮助中心</span>
        </div>
        <div ref={searchRef} className="hc_hc_inputWrap" style={{opacity: 0,transition: 'opacity 500ms',position: 'relative',width: '360px',height: '48px',border: '1px solid rgba(0, 0, 0, 0.09)'}}>
          <CustomInput onBlur={customInputBlur} onFocus={customInputFocus} value={inputVlue} onChange={customInputChange} allowClear prefix={<SearchOutlined style={{color: 'rgba(0, 0, 0, 0.4)',fontSize: '22px',marginRight: '8px'}} />} type="search" className="hc_hc_input" placeholder="请输入关键词，如：打单发货" />
          {isSearch && <div className="hc_hc_inputRightContent">
            <Button style={{borderRadius: '20px'}} type="primary">搜索</Button>
          </div>}
          {searchList.length > 0 && isFocus && isSearch && <div className="bullet_frame" style={{width: '360px'}}>
            {searchList.map((item, index) => (
              <div key={item.key} className="bullet_frame_item" dangerouslySetInnerHTML={{__html: item.value}}>
              </div>
            ))}
          </div>}
        </div>
      </div>
      <img style={{position: 'absolute',top: 0,width: '100%'}} src={mask} alt="" />
      <div className="container" style={{overflow: 'auto'}}>
        <div className="container" style={{margin: 0}}>
          <div className="container_inner">
            <div className="search">
              <h1 style={{marginBottom: '24px'}}>嗨，有什么可以帮你?</h1>
              <div ref={searchRef1} className="hc_hc_inputWrap" style={{position: 'relative',opacity: 1,transition: 'opacity 500ms',}}>
                <CustomInput onBlur={customInputBlur} onFocus={customInputFocus} value={inputVlue} onChange={customInputChange} allowClear prefix={<SearchOutlined style={{color: 'rgba(0, 0, 0, 0.4)',fontSize: '22px',marginRight: '8px'}} />} type="search" className="hc_hc_input" placeholder="请输入关键词，如：打单发货" />
                {isSearch && <div className="hc_hc_inputRightContent">
                  <Button style={{borderRadius: '20px'}} type="primary">搜索</Button>
                </div>}
                {searchList.length > 0 && isFocus && isSearch && <div className="bullet_frame">
                  { searchList.map((item, index) => (
                    <div key={item.key} className="bullet_frame_item" dangerouslySetInnerHTML={{__html: item.value}}>
                    </div>
                  ))}
                </div>}
              </div>
            </div>
            <div className="card">
              <div className="card_title">新人指引</div>
              <div className="card_box">
                {guide.ContainerType1?.[0]?.SubContainers.map((item, index) => (
                  <div onClick={() => clickItem(item.RelatedType, item )} className="card_item" key={index} style={{marginLeft: index === 0 ? 'none':'24px',backgroundImage: `url(${item.Image || img1})`}}>
                    <div className="card_item_title">{item.ContainerName}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="card">
              <div className="card_title">使用指南</div>
              <div className="card_box" style={{marginTop: '4px'}}>
                {guide.ContainerType2?.[0]?.SubContainers.map((item, index) => (
                  <Card
                  title={item.ContainerName}
                  extra={<a className="more">查看更多</a>}
                  headStyle={{
                    fontFamily: 'Source Han Sans',
                    fontSize: '20px',
                    background: '#F2F6FC',
                    color: 'rgba(0, 0, 0, 0.9)',
                    fontWeight: '500',
                    height: '60px',
                    padding: '0 20px',
                  }}
                  bodyStyle={{
                    padding: '8px 12px',
                  }}
                  style={{
                    width: '360px',
                    marginLeft: index === 0 ? 'node':'24px',
                    marginTop: '24px',
                    border: '1px solid rgba(8, 136, 255, 0.2)'
                  }}
                  >
                    {
                      item.RelatedArticles && item.RelatedArticles.map(item1 => <div className="card_item_text" onClick={() => clickItem(1, item1)}>・{item1.ArticleName}</div>)
                    }
                    {/* <div className="card_item_text">・如何使用代发系统打单发货</div>
                    <div className="card_item_text">・如何使用代发系统打单发货</div>
                    <div className="card_item_text">・如何使用代发系统打单发货</div> */}
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container" style={{overflow: 'auto', backgroundImage: 'linear-gradient(to top, #ECF5FF 45%, rgba(255, 255, 255, 0) 104%)'}}>
      <div className="container_inner" style={{marginBottom: '20px'}}>
        <div className="card">
          <div className="card_title">更多资源</div>
            <div className="card_box" style={{marginTop: '4px'}}>
              {guide.ContainerType3?.[0]?.SubContainers.map((item, index) => (
                <Card
                title={item.ContainerName}
                extra={<a className="more">查看更多</a>}
                bordered={false}
                headStyle={{
                  fontSize: '20px',
                  color: 'rgba(0, 0, 0, 0.9)',
                  fontWeight: '500',
                  height: '60px',
                  padding: '0 20px',
                }}
                bodyStyle={{
                  padding: '8px 12px',
                }}
                style={{
                  width: '552px',
                  marginLeft: index % 2 === 1 ? '24px':'node',
                  marginTop: '24px'
                }}
                >
                  {
                    item.RelatedArticles && item.RelatedArticles.map((item1,index) => <div className="card_item_text" style={{borderTop: index === 0 ? 'unset' :'1px solid rgba(0, 0, 0, 0.04)'}} onClick={() => clickItem(1, item1)}>・{item1.ArticleName}</div>)
                  }
                  {/* <div className="card_item_text" >・如何使用代发系统打单发货</div>
                  <div className="card_item_text" style={{borderTop: '1px solid rgba(0, 0, 0, 0.04)'}}>・如何使用代发系统打单发货</div>
                  <div className="card_item_text" style={{borderTop: '1px solid rgba(0, 0, 0, 0.04)'}}>・如何使用代发系统打单发货</div>
                  <div className="card_item_text" style={{borderTop: '1px solid rgba(0, 0, 0, 0.04)'}}>・如何使用代发系统打单发货</div> */}
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
    
  );
};

export default HomePage;
