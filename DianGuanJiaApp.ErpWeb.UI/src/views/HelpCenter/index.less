.header_container{
  position: sticky;
  top: 0;
  width: 100%;
  height: 64px;
  z-index: 9;
  display: flex;
  justify-content: center;
  align-items: center;
  .header_title{
    font-family: Source <PERSON>;
    font-size: 24px;
    font-weight: bold;
    color: #0888FF;
    display: flex;
    align-items: center;
    position: absolute;
    left: 16px;
    .divider{
      height: 20px;
      margin-left: 8px;
      margin-right: 8px;
      border: 1px solid rgba(101, 108, 119, 0.6);
      display: inline-block;
      font-family: Source <PERSON>;
    }
    .text{
      font-family: Source <PERSON>;
      font-size: 18px;
      font-weight: normal;
      color: rgba(0, 0, 0, 0.6);
    }
  }
}
.bullet_frame{
  position: absolute;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid rgba(182, 235, 246, 0.4);
  box-shadow: 0px 2px 8px 0px rgba(180, 198, 216, 0.2);
  border-radius: 32px;
  width: 600px;
  left: 0;
  top: 66px;
  padding: 8px 20px;
  .bullet_frame_item{
    padding: 8px 0;
    cursor: pointer;
  }
  .bullet_frame_item:hover{
    color: #0888FF;
  }
}
.container {
  overflow: hidden;
  .box{
    position: absolute;
    width: 100%;
    height: 100vh;
    .box_abs{
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      img{
        width: 100%;
        height: 600px;
      }
    }
  }
 
  .container_inner {
    width: 1128px;
    margin-left: auto;
    margin-right: auto;
    z-index: 1;
    position: relative;
    padding: 0 16px;
    box-sizing: content-box;
    .search{
      margin-top: 72px;
      display: flex;
      flex-direction: column;
      align-items: center;
      
    }
    
  }
  .card{
    margin-top: 68px;
    .card_title{
      font-family: Source Han Sans;
      font-size: 28px;
      font-weight: 500;
      text-align: center;
    }
    .card_box{
      margin-top: 28px;
      display: flex;
      flex-wrap: wrap;
      .card_item{
        width: 360px;
        height: 220px;
        overflow: hidden;
        padding: 25px;
        background-repeat: no-repeat;
        box-sizing: border-box;
        .card_item_title{
          font-family: Source Han Sans;
          font-size: 22px;
          font-weight: 500;
        }
      }
    }
  }
  
  .more {
    color: rgba(0, 0, 0, 0.6);
    font-family: Source Han Sans;
    font-size: 14px;
  }
  .more:hover{
    color: #0888FF;
  }
  .card_item_text{
    color: rgba(0, 0, 0, 0.6);
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: normal;
    padding: 8px;
    box-sizing: border-box;
  }
  .card_item_text:hover{
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.04);
    cursor: pointer;
  }
}
.hc_hc_inputWrap{
  align-items: center;
  background-color: #fff;
  border-radius: 100px;
  box-shadow: 0 2px 24px rgba(28, 76, 186, .08);
  display: flex;
  height: 60px;
  padding: 0 20px;
  width: 600px;
  .hc_hc_input{
    -webkit-appearance: none;
    appearance: none;
    border: none;
    color: #1f2329;
    flex: 1 1;
    font-size: 16px;
    height: 24px;
    line-height: 24px;
    outline: none;
    overflow: hidden;
    padding: 0;
  }
  .hc_hc_inputRightContent{
    display: flex;
    flex: 0 0 auto;
    height: 24px;
    margin-top: -7px;
  }
}
