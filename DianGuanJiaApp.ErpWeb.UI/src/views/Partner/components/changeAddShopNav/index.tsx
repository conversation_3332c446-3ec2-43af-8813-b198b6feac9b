

import { useState, useEffect } from 'react'
import { Menu } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom'
import './index.less';

const ChangeAddShopNav = (props: any) => {
    const navigate = useNavigate();
    const [isShowSupportPlatform, setIsShowSupportPlatform] = useState(false);
    const [currentNav, setCurrentNav] = useState(props);
    const { pathname } = useLocation();

    useEffect(() => {
        setCurrentNav(pathname)
    }, [pathname])
    var navs = [
        {
            label: <span>国内电商</span>,
            key: '/Partner/Index',
        },
        {
            label: <span>跨境电商</span>,
            key: '/partner/crossborder',
        }
    ]

    // 菜单点击
    const handleClickMenu = ({ key }: { key: string }) => {
        props.changeNav();
        navigate(key);
    }

    return (
        <div className='changeAddShopNav' style={{position:'fixed',width:'100%',zIndex:'1'}}>
            <Menu onClick={handleClickMenu} selectedKeys={[currentNav]} mode="horizontal" items={navs} />
        </div>
    )
}

export default ChangeAddShopNav;